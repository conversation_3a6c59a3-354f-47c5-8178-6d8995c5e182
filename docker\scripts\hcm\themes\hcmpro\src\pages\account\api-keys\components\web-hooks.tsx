/**
 * H‑CareCloud Project – Webhooks Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { hcmApi } from '@/services/hcm-api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

const Webhooks = () => {
  const [webhookUrl, setWebhookUrl] = useState('');
  const [webhookName, setWebhookName] = useState('');
  const [eventType, setEventType] = useState('all_events');
  const [useCustomHeaders, setUseCustomHeaders] = useState(false);
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    if (!webhookUrl.trim() || !webhookName.trim()) {
      toast.error('Please fill in webhook URL and name');
      return;
    }

    try {
      setSaving(true);
      await hcmApi.createWebhook({
        url: webhookUrl,
        name: webhookName,
        event_type: eventType,
        use_custom_headers: useCustomHeaders
      });
      toast.success('Webhook configuration saved successfully');
      logger.debug('Webhook configuration saved', {
        component: 'Webhooks',
        action: 'saveWebhook',
        data: { webhookUrl, webhookName, eventType, useCustomHeaders }
      });
    } catch (error) {
      toast.error('Failed to save webhook configuration');
      logger.error('Failed to save webhook configuration', {
        component: 'Webhooks',
        action: 'saveWebhook',
        data: { webhookUrl, webhookName, eventType }
      }, error as Error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <Card className="pb-2.5">
      <CardHeader id="webhooks">
        <CardTitle>Webhooks</CardTitle>
      </CardHeader>
      <CardContent className="grid gap-5">
        <p className="text-sm text-foreground">
          Set up Webhooks to trigger actions on external services in real-time.
          Stay informed on updates and changes to ensure seamless integration.
        </p>
        <div className="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
          <Label className="flex w-full max-w-56">Webhook URL</Label>
          <div className="grow">
            <Input 
              type="url" 
              placeholder="https://your-server.com/webhook" 
              value={webhookUrl}
              onChange={(e) => setWebhookUrl(e.target.value)}
            />
          </div>
        </div>
        <div className="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
          <Label className="flex w-full max-w-56">Webhook Name</Label>
          <div className="grow">
            <Input
              type="text"
              placeholder="Server Alert Webhook"
              value={webhookName}
              onChange={(e) => setWebhookName(e.target.value)}
            />
          </div>
        </div>
        <div className="flex items-center flex-wrap lg:flex-nowrap gap-2.5">
          <Label className="flex w-full max-w-56">Event Type</Label>
          <div className="grow">
            <Select value={eventType} onValueChange={setEventType}>
              <SelectTrigger>
                <SelectValue placeholder="Select event type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all_events">All Server Events</SelectItem>
                <SelectItem value="system_alerts">System Alerts</SelectItem>
                <SelectItem value="docker_events">Docker Events</SelectItem>
                <SelectItem value="database_events">Database Events</SelectItem>
                <SelectItem value="user_actions">User Actions</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex items-center flex-wrap lg:flex-nowrap gap-2.5 mb-2.5">
          <Label className="flex w-full max-w-56">Custom Headers</Label>
          <div className="grow">
            <div className="flex items-center space-x-2">
              <Label htmlFor="custom-headers" className="text-sm">
                Use Custom Headers
              </Label>
              <Switch 
                id="custom-headers" 
                size="sm" 
                checked={useCustomHeaders}
                onCheckedChange={setUseCustomHeaders}
              />
            </div>
          </div>
        </div>
        <div className="flex justify-end">
          <Button onClick={handleSave} disabled={saving}>
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export { Webhooks };

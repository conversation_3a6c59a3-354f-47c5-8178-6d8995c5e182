/**
 * H‑CareCloud Project – Account Notifications Content
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { useEffect, useState } from 'react';
import { BellDot, BellRing, MessageSquareText } from 'lucide-react';
import { hcmApi } from '@/services/hcm-api';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { Channels, DoNotDistrub, OtherNotifications } from './components';

interface NotificationSettings {
  system_alerts: boolean;
  server_health: boolean;
  docker_events: boolean;
  database_alerts: boolean;
  security_warnings: boolean;
  backup_notifications: boolean;
  email_notifications: boolean;
  push_notifications: boolean;
  sms_notifications: boolean;
}

export function AccountNotificationsContent() {
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [loading, setLoading] = useState(true);

  const loadNotificationSettings = async () => {
    try {
      const settings = await hcmApi.getSettings();
      if (settings) {
        setSettings(settings as NotificationSettings);
      }
    } catch (error) {
      logger.error('Failed to load notification settings', {
        component: 'AccountNotificationsContent',
        action: 'loadNotificationSettings'
      }, error as Error);
      toast.error('Failed to load notification settings');
    } finally {
      setLoading(false);
    }
  };

  const updateNotificationSetting = async (key: keyof NotificationSettings, value: boolean) => {
    try {
      const updatedSettings = { ...settings, [key]: value } as NotificationSettings;
      await hcmApi.updateSettings(updatedSettings);
      setSettings(updatedSettings);
      toast.success('Notification setting updated');

      logger.debug('Notification setting updated', {
        component: 'AccountNotificationsContent',
        action: 'updateNotificationSetting',
        data: { key, value }
      });
    } catch (error) {
      logger.error('Failed to update notification setting', {
        component: 'AccountNotificationsContent',
        action: 'updateNotificationSetting',
        data: { key, value }
      }, error as Error);
      toast.error('Failed to update notification setting');
    }
  };

  useEffect(() => {
    loadNotificationSettings();
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-5 lg:gap-7.5">
        <div className="col-span-2">
          <div className="flex flex-col gap-5 lg:gap-7.5">
            <div className="card">
              <div className="card-header">
                <Skeleton className="h-6 w-48" />
              </div>
              <div className="card-body">
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </div>
            <div className="card">
              <div className="card-header">
                <Skeleton className="h-6 w-40" />
              </div>
              <div className="card-body">
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            </div>
          </div>
        </div>
        <div className="col-span-1">
          <div className="card">
            <div className="card-header">
              <Skeleton className="h-6 w-32" />
            </div>
            <div className="card-body">
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 xl:grid-cols-3 gap-5 lg:gap-7.5">
      <div className="col-span-2">
        <div className="flex flex-col gap-5 lg:gap-7.5">
          <Channels />
          <OtherNotifications />
          
          {/* Server Management Notifications */}
          <div className="card">
            <div className="card-header">
              <div className="card-title">
                <BellRing className="text-primary size-5 me-2" />
                Server Management Alerts
              </div>
              <div className="card-description">
                Configure notifications for server monitoring, health checks, and system events.
              </div>
            </div>
            <div className="card-body">
              <div className="grid gap-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <BellDot className="text-danger size-4" />
                    <div>
                      <div className="text-sm font-medium">System Health Alerts</div>
                      <div className="text-2xs text-gray-600">Critical server health warnings and resource alerts</div>
                    </div>
                  </div>
                  <input
                    type="checkbox"
                    className="form-check-input"
                    checked={settings?.system_alerts || false}
                    onChange={(e) => updateNotificationSetting('system_alerts', e.target.checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <MessageSquareText className="text-info size-4" />
                    <div>
                      <div className="text-sm font-medium">Docker Container Events</div>
                      <div className="text-2xs text-gray-600">Container status changes and deployment notifications</div>
                    </div>
                  </div>
                  <input
                    type="checkbox"
                    className="form-check-input"
                    checked={settings?.docker_events || false}
                    onChange={(e) => updateNotificationSetting('docker_events', e.target.checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <BellRing className="text-warning size-4" />
                    <div>
                      <div className="text-sm font-medium">Database Maintenance</div>
                      <div className="text-2xs text-gray-600">Database backup completion and maintenance schedules</div>
                    </div>
                  </div>
                  <input
                    type="checkbox"
                    className="form-check-input"
                    checked={settings?.database_alerts || false}
                    onChange={(e) => updateNotificationSetting('database_alerts', e.target.checked)}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="col-span-1">
        <div className="flex flex-col gap-5 lg:gap-7.5">
          <DoNotDistrub />
        </div>
      </div>
    </div>
  );
}

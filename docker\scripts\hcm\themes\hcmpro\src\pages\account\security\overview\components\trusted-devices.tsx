/**
 * H‑CareCloud Project – Security Trusted Devices Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { useState, useEffect } from 'react';
import { hcmApi } from '@/services/hcm-api';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { LogOut, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';
import { toAbsoluteUrl } from '@/lib/helpers';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';

interface ITrustedDevicesItem {
  id: string;
  device_name: string;
  device_type: string;
  browser: string;
  os: string;
  location: string;
  ip_address: string;
  trusted_at: string;
  last_used: string;
  is_current: boolean;
}
type ITrustedDevicesItems = Array<ITrustedDevicesItem>;

const TrustedDevices = () => {
  const [devices, setDevices] = useState<ITrustedDevicesItems>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTrustedDevices();
  }, []);

  const loadTrustedDevices = async () => {
    try {
      // For now, using sessions API to get device info
      const sessions = await hcmApi.getUserSessions();
      // Filter to only trusted/recognized devices (could be based on trust flag in future)
      const trustedDevices = sessions
        .filter((session: any) => session.trusted || session.login_count > 5)
        .map((session: any) => ({
          id: session.id,
          device_name: session.device_name || `${session.browser} on ${session.device_type}`,
          device_type: session.device_type,
          browser: session.browser,
          os: session.os || session.device_type,
          location: session.location,
          ip_address: session.ip_address,
          trusted_at: session.first_login || session.created_at,
          last_used: session.last_activity,
          is_current: session.is_current
        }));
      setDevices(trustedDevices);
    } catch (error) {
      toast.error('Failed to load trusted devices');
      logger.error('Failed to load trusted devices', {
        component: 'TrustedDevices',
        action: 'loadTrustedDevices'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const removeTrustedDevice = async (deviceId: string) => {
    try {
      // This would terminate the session and remove trust
      await hcmApi.terminateSession(deviceId);
      setDevices(devices.filter(d => d.id !== deviceId));
      toast.success('Device removed from trusted list');
    } catch (error) {
      toast.error('Failed to remove trusted device');
      logger.error('Failed to remove trusted device', {
        component: 'TrustedDevices',
        action: 'removeTrustedDevice',
        data: { deviceId }
      }, error as Error);
    }
  };

  const getBrowserLogo = (browser: string) => {
    const browserName = browser.toLowerCase();
    if (browserName.includes('chrome')) return 'chrome.svg';
    if (browserName.includes('firefox')) return 'firefox.svg';
    if (browserName.includes('safari')) return 'safari.svg';
    if (browserName.includes('edge')) return 'edge.svg';
    return 'chrome.svg'; // default
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const deviceDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    
    if (deviceDate.getTime() === today.getTime()) {
      return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }
    
    return date.toLocaleDateString([], { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const renderItem = (device: ITrustedDevicesItem, index: number) => {
    if (loading) {
      return (
        <TableRow key={index}>
          <TableCell className="min-w-48 w-48">
            <div className="flex items-center gap-2.5">
              <Skeleton className="h-6 w-6" />
              <div className="flex flex-col gap-1">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
          </TableCell>
          <TableCell className="min-w-56">
            <Skeleton className="h-4 w-32 mb-1" />
            <Skeleton className="h-3 w-28" />
          </TableCell>
          <TableCell className="pr-7.5! min-w-16 text-end">
            <Skeleton className="h-6 w-6 ml-auto" />
          </TableCell>
        </TableRow>
      );
    }

    return (
      <TableRow key={device.id}>
        <TableCell className="min-w-48 w-48">
          <div className="flex items-center gap-2.5">
            <img
              src={toAbsoluteUrl(`/media/brand-logos/${getBrowserLogo(device.browser)}`)}
              className="h-6"
              alt={device.browser}
            />
            <div className="flex flex-col">
              <div className="text-sm font-medium text-mono mb-px flex items-center gap-2">
                {device.browser}
                {device.is_current && (
                  <Shield className="size-3 text-green-600" />
                )}
              </div>
              <div className="flex gap-1.5 items-center">
                <span className="text-xs text-secondary-foreground">
                  {device.location || 'Unknown location'}
                </span>
              </div>
            </div>
          </div>
        </TableCell>
        <TableCell className="min-w-56 text-secondary-foreground font-normal">
          {device.os}
          <br />
          Last used: {formatDate(device.last_used)}
        </TableCell>
        <TableCell className="pr-7.5! min-w-16 text-end">
          {!device.is_current && (
            <Button 
              variant="ghost" 
              mode="icon"
              onClick={() => removeTrustedDevice(device.id)}
              title="Remove device"
            >
              <LogOut size={16} />
            </Button>
          )}
        </TableCell>
      </TableRow>
    );
  };

  return (
    <Card className="min-w-full">
      <CardHeader>
        <CardTitle>Trusted Devices</CardTitle>
      </CardHeader>
      <CardContent className="kt-scrollable-x-auto p-0">
        <div className="kt-scrollable-auto">
          <Table className="align-middle text-secondary-foreground font-medium text-sm">
            <TableBody>
              {loading ? (
                [1, 2].map((i) => renderItem({} as ITrustedDevicesItem, i))
              ) : devices.length > 0 ? (
                devices.map((device, index) => renderItem(device, index))
              ) : (
                <TableRow>
                  <TableCell colSpan={3} className="text-center py-8 text-muted-foreground">
                    No trusted devices found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      {!loading && devices.length > 0 && (
        <CardFooter className="justify-center">
          <Button mode="link" underlined="dashed" asChild>
            <Link to="/account/security/devices">Manage Trusted Devices</Link>
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

export { TrustedDevices, type ITrustedDevicesItem, type ITrustedDevicesItems };

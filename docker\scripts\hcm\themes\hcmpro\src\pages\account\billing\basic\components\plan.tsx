/**
 * H‑CareCloud Project – Billing Plan Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { useEffect, useState } from 'react';
import { Link } from 'react-router';
import { toast } from 'sonner';
import { hcmApi, type UserSubscription } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Skeleton } from '@/components/ui/skeleton';

interface IPlanItem {
  total: string;
  description: string;
}
type IPlanItems = Array<IPlanItem>;

const Plan = () => {
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSubscription();
  }, []);

  const loadSubscription = async () => {
    try {
      const userSubscription = await hcmApi.getUserSubscription();
      setSubscription(userSubscription);
    } catch (error) {
      toast.error('Failed to load subscription details');
      logger.error('Failed to load subscription details', {
        component: 'Plan',
        action: 'loadSubscription'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const getStatistics = (): IPlanItems => {
    if (!subscription) {
      return [
        { total: 'N/A', description: 'Annual Total' },
        { total: 'N/A', description: 'Next Bill Amount' },
        { total: 'N/A', description: 'Next Billing Date' },
      ];
    }

    const annualTotal = subscription.plan.billing_cycle === 'yearly' 
      ? `$${subscription.plan.price.toFixed(2)}`
      : `$${(subscription.plan.price * 12).toFixed(2)}`;

    const nextBillAmount = `$${subscription.plan.price.toFixed(2)}`;
    
    const nextBillDate = subscription.current_period_end
      ? new Date(subscription.current_period_end).toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'short',
          year: '2-digit'
        })
      : 'N/A';

    return [
      { total: annualTotal, description: 'Annual Total' },
      { total: nextBillAmount, description: 'Next Bill Amount' },
      { total: nextBillDate, description: 'Next Billing Date' },
    ];
  };

  const getUsagePercentage = () => {
    if (!subscription || !subscription.plan.max_users) return 0;
    // This would come from actual usage data in a real implementation
    return Math.min((32 / subscription.plan.max_users) * 100, 100);
  };

  const renderItem = (statistic: IPlanItem, index: number) => {
    return (
      <div
        key={index}
        className="grid grid-cols-1 content-between gap-1.5 border border-dashed border-input shrink-0 rounded-md px-3.5 py-2 min-w-24 max-w-auto"
      >
        <span className="text-mono text-base leading-none font-medium">
          {statistic.total}
        </span>
        <span className="text-secondary-foreground text-sm">
          {statistic.description}
        </span>
      </div>
    );
  };

  const statistics = getStatistics();

  if (loading) {
    return (
      <Card>
        <CardContent className="lg:py-7.5">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Skeleton className="h-8 w-32" />
                <Skeleton className="h-4 w-48" />
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-24" />
              </div>
            </div>
            <div className="flex gap-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-16 w-24" />
              ))}
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-2 w-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="lg:py-7.5">
        <div className="flex flex-col items-stretch gap-5 lg:gap-7.5">
          <div className="flex flex-wrap items-center gap-5 justify-between">
            <div className="flex flex-col gap-1">
              <div className="flex items-center gap-2.5">
                <h2 className="text-2xl font-semibold text-mono">
                  {subscription?.plan.name || 'H-CareManager Plan'}
                </h2>
                <Badge size="md" variant="success" appearance="outline">
                  {subscription?.plan.billing_cycle === 'yearly' ? 'Yearly' : 'Monthly'}
                </Badge>
              </div>
              <p className="text-sm text-secondary-foreground">
                {subscription?.plan.description || 'Server management and hosting features'}
              </p>
            </div>
            <div className="flex gap-2.5">
              <Button variant="outline">
                <Link to="/account/billing/basic">Cancel Plan</Link>
              </Button>
              <Button>
                <Link to="/account/billing/plans">Upgrade Plan</Link>
              </Button>
            </div>
          </div>
          <div className="flex items-center flex-wrap gap-2 lg:gap-5">
            {statistics.map((statistic, index) => {
              return renderItem(statistic, index);
            })}
          </div>
          <div className="flex flex-col gap-3.5">
            <span className="text-sm text-secondary-foreground">
              Server Usage ({subscription?.plan.max_users ? `32 of ${subscription.plan.max_users}` : 'Unlimited'} users)
            </span>
            <Slider 
              value={[getUsagePercentage()]} 
              max={100} 
              step={1}
              disabled
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export { Plan, type IPlanItem, type IPlanItems };

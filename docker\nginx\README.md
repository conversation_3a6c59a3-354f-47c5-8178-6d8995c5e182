# H-CareCloud Nginx Web Server Configuration

## Overview
This directory contains the Nginx web server configuration for H-CareCloud Hospital Management System. Nginx serves as the reverse proxy, load balancer, and static file server for both the main HMS Laravel application and the H-CareManager React dashboard, providing optimized performance and security for healthcare applications.

## Architecture

### Nginx Structure
```
/docker/nginx/
├── nginx.conf              # Main Nginx configuration
├── nginx.conf.template     # Template for environment-based config
├── default.conf            # Default server configuration
├── docker-entrypoint.sh    # Container initialization script
└── conf.d/                 # Additional configuration files
    ├── ssl.conf            # SSL/TLS configuration
    ├── security.conf       # Security headers and settings
    ├── gzip.conf          # Compression configuration
    └── upstream.conf      # Backend server definitions
```

### Server Architecture
- **Main HMS**: Laravel application served via PHP-FPM
- **H-CareManager**: React TypeScript dashboard (static files)
- **API Gateway**: RESTful API routing and load balancing
- **Static Assets**: Optimized serving of CSS, JS, images, and medical files

## Server Configuration

### Main nginx.conf
```nginx
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for"';

    # Performance optimization
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Security
    server_tokens off;
    client_max_body_size 100M;

    # Gzip compression
    include /etc/nginx/conf.d/gzip.conf;

    # Security headers
    include /etc/nginx/conf.d/security.conf;

    # Upstream servers
    include /etc/nginx/conf.d/upstream.conf;

    # Server configurations
    include /etc/nginx/conf.d/*.conf;
}
```

### Virtual Host Configuration
```nginx
# H-CareCloud HMS (Laravel)
server {
    listen 80;
    server_name hcarecloud.local;
    root /var/www/html/public;
    index index.php index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Laravel routing
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP-FPM processing
    location ~ \.php$ {
        fastcgi_pass fpm:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_read_timeout 300;
    }

    # Static assets caching
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
}

# H-CareManager Dashboard
server {
    listen 80;
    server_name manager.hcarecloud.local;
    root /var/www/manager/dist;
    index index.html;

    # SPA routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API proxy to H-CareManager backend
    location /api/ {
        proxy_pass http://hcm-api:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static assets optimization
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        gzip_static on;
    }
}
```

## Performance Optimization

### Gzip Compression
```nginx
# conf.d/gzip.conf
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_proxied any;
gzip_comp_level 6;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    image/svg+xml;
```

### Caching Strategy
```nginx
# Static asset caching
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary "Accept-Encoding";
    access_log off;
}

# API response caching
location /api/static/ {
    proxy_cache api_cache;
    proxy_cache_valid 200 1h;
    proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
    add_header X-Cache-Status $upstream_cache_status;
}
```

### Load Balancing
```nginx
# conf.d/upstream.conf
upstream hcm_api {
    least_conn;
    server hcm-api-1:5000 weight=3;
    server hcm-api-2:5000 weight=2;
    server hcm-api-3:5000 weight=1 backup;
}

upstream hms_app {
    ip_hash;
    server hcarecloud-app-1:8000;
    server hcarecloud-app-2:8000;
    keepalive 32;
}
```

## Security Configuration

### Security Headers
```nginx
# conf.d/security.conf
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';" always;

# HIPAA compliance headers
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
add_header X-Permitted-Cross-Domain-Policies "none" always;
```

### SSL/TLS Configuration
```nginx
# conf.d/ssl.conf
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_stapling on;
ssl_stapling_verify on;

# HIPAA-compliant SSL settings
ssl_certificate /etc/nginx/ssl/hcarecloud.crt;
ssl_certificate_key /etc/nginx/ssl/hcarecloud.key;
ssl_dhparam /etc/nginx/ssl/dhparam.pem;
```

### Rate Limiting
```nginx
# Rate limiting for API endpoints
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

# Apply rate limits
location /api/ {
    limit_req zone=api burst=20 nodelay;
    limit_req_status 429;
}

location /api/auth/login {
    limit_req zone=login burst=3 nodelay;
    limit_req_status 429;
}
```

## HIPAA Compliance

### Access Logging
```nginx
# Detailed access logging for HIPAA compliance
log_format hipaa '$remote_addr - $remote_user [$time_local] '
                '"$request" $status $body_bytes_sent '
                '"$http_referer" "$http_user_agent" '
                '$request_time $upstream_response_time '
                '"$http_x_forwarded_for" "$http_authorization"';

access_log /var/log/nginx/hipaa_access.log hipaa;
```

### Secure File Handling
```nginx
# Medical file uploads
location /uploads/ {
    internal;
    alias /var/www/html/storage/app/uploads/;
    
    # Restrict file types
    location ~* \.(php|pl|py|jsp|asp|sh|cgi)$ {
        deny all;
    }
    
    # Add security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
}

# Secure download endpoint
location /secure-download/ {
    internal;
    alias /var/www/html/storage/app/secure/;
    add_header Content-Disposition "attachment";
}
```

## Environment Configuration

### Environment Variables
```bash
# Nginx configuration
NGINX_HOST=hcarecloud.local
NGINX_PORT=80
NGINX_SSL_PORT=443

# Backend servers
HMS_BACKEND=hcarecloud-app:8000
HCM_BACKEND=hcm-api:5000
PHP_FPM_BACKEND=fpm:9000

# SSL configuration
SSL_CERTIFICATE_PATH=/etc/nginx/ssl/hcarecloud.crt
SSL_PRIVATE_KEY_PATH=/etc/nginx/ssl/hcarecloud.key

# Performance settings
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024
NGINX_CLIENT_MAX_BODY_SIZE=100M
```

### Docker Integration
```yaml
# docker-compose.yml
nginx:
  image: nginx:alpine
  ports:
    - "80:80"
    - "443:443"
  volumes:
    - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
    - ./docker/nginx/conf.d:/etc/nginx/conf.d
    - ./docker/nginx/ssl:/etc/nginx/ssl
    - ./docker/logs/nginx:/var/log/nginx
  depends_on:
    - fpm
    - hcm-api
    - hcarecloud-app
```

## Monitoring and Logging

### Health Checks
```nginx
# Health check endpoint
location /nginx-health {
    access_log off;
    return 200 "healthy\n";
    add_header Content-Type text/plain;
}

# Status monitoring
location /nginx_status {
    stub_status on;
    access_log off;
    allow 127.0.0.1;
    allow 172.16.0.0/12;
    deny all;
}
```

### Log Analysis
```bash
# Real-time access monitoring
tail -f /var/log/nginx/access.log

# Error monitoring
tail -f /var/log/nginx/error.log | grep -E "(error|crit|alert|emerg)"

# Performance analysis
awk '{print $NF}' /var/log/nginx/access.log | grep -v '-' | sort -n | tail -10
```

## Troubleshooting

### Common Issues
1. **502 Bad Gateway**: Check PHP-FPM or backend service connectivity
2. **413 Request Entity Too Large**: Increase client_max_body_size
3. **SSL Certificate Errors**: Verify certificate paths and permissions
4. **High Response Times**: Check upstream server performance

### Debug Commands
```bash
# Test Nginx configuration
nginx -t

# Reload configuration
nginx -s reload

# Check upstream status
curl http://localhost/nginx_status

# Monitor connections
netstat -an | grep :80 | wc -l
```

## Related Documentation
- [PHP-FPM Configuration Documentation](../fpm/README.md)
- [SSL Certificate Management Documentation](../../docs/ssl.md)
- [Load Balancing Documentation](../../docs/load-balancing.md)
- [Main Project Documentation](../scripts/hcm/themes/hcmpro/docs.md)

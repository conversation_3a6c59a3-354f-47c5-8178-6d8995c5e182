# H-CareCloud Python Modules (hcc_pymodules)

## Overview
This directory contains shared Python modules that provide core functionality for the H-CareCloud Manager system. These modules handle configuration, database connections, Docker operations, environment management, and utility functions used across the entire application.

## Architecture

### Module Structure
```
/hcc_pymodules/
├── __init__.py          # Package initialization
├── hcc_config.py        # Configuration management
├── hcc_database.py      # Database connection & operations
├── hcc_docker.py        # Docker container management
├── hcc_environment.py   # Environment variable handling
├── hcc_backup.py        # Backup and recovery operations
├── hcc_utils.py         # General utility functions
└── hcc_web.py           # Web server utilities
```

### Design Principles
- **Centralized Configuration**: Single source of truth for all settings
- **Database Abstraction**: Unified database connection management
- **Environment Isolation**: Proper separation of dev/staging/production
- **Error Handling**: Comprehensive error handling and logging
- **Docker Integration**: Native Docker API integration
- **Security First**: HIPAA-compliant security standards

## Core Modules

### hcc_config.py
**Purpose**: Centralized configuration management for the entire H-CareCloud system.

**Key Features**:
- Environment variable loading from `.env` files
- Configuration validation and defaults
- Multi-environment support (development, staging, production)
- Secure credential management
- Path resolution for project files

**Usage**:
```python
from hcc_pymodules import hcc_config

# Get database configuration
db_host = hcc_config.get_db_host()
db_name = hcc_config.get_db_name()

# Get project paths
project_root = hcc_config.PROJECT_ROOT
env_file = hcc_config.ENV_FILE
```

### hcc_database.py
**Purpose**: Database connection management and operations for MySQL databases.

**Key Features**:
- Centralized database connection pooling
- Multi-database support (hcarecloud_manager, main HMS database)
- Connection health monitoring
- Transaction management
- Query logging and debugging
- HIPAA-compliant audit logging

**Usage**:
```python
from hcc_pymodules import hcc_database

# Get database connection
connection = hcc_database.get_db_connection()
with connection.cursor() as cursor:
    cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))
    user = cursor.fetchone()
connection.close()
```

### hcc_docker.py
**Purpose**: Docker container management and monitoring.

**Key Features**:
- Container lifecycle management
- Health monitoring
- Log aggregation
- Resource usage tracking
- Network management
- Volume management

**Usage**:
```python
from hcc_pymodules import hcc_docker

# Get container status
status = hcc_docker.get_container_status('hcarecloud-app')

# Restart container
hcc_docker.restart_container('hcarecloud-mysql')
```

### hcc_environment.py
**Purpose**: Environment variable management and validation.

**Key Features**:
- Environment variable validation
- Type conversion and defaults
- Secure credential handling
- Environment-specific configurations
- Configuration file generation

**Usage**:
```python
from hcc_pymodules import hcc_environment

# Get environment variables with validation
debug_mode = hcc_environment.get_bool('VITE_DEBUG', default=False)
api_timeout = hcc_environment.get_int('VITE_API_TIMEOUT', default=30000)
```

### hcc_backup.py
**Purpose**: Backup and recovery operations for databases and files.

**Key Features**:
- Automated database backups
- File system backups
- Incremental backup support
- Backup verification
- Recovery procedures
- Backup scheduling

**Usage**:
```python
from hcc_pymodules import hcc_backup

# Create database backup
backup_file = hcc_backup.create_database_backup('hcarecloud_manager')

# Restore from backup
hcc_backup.restore_database_backup(backup_file)
```

### hcc_utils.py
**Purpose**: General utility functions used throughout the application.

**Key Features**:
- String manipulation utilities
- Date/time formatting
- File operations
- Encryption/decryption helpers
- Validation functions
- Logging utilities

**Usage**:
```python
from hcc_pymodules import hcc_utils

# Generate secure token
token = hcc_utils.generate_secure_token(32)

# Format timestamp
formatted_date = hcc_utils.format_timestamp(datetime.now())
```

### hcc_web.py
**Purpose**: Web server utilities and Flask application helpers.

**Key Features**:
- Flask application factory
- Middleware configuration
- Request/response utilities
- CORS handling
- Security headers
- Rate limiting

**Usage**:
```python
from hcc_pymodules import hcc_web

# Create Flask app with standard configuration
app = hcc_web.create_app()

# Add security headers
hcc_web.add_security_headers(app)
```

## Configuration Management

### Environment Variables
The modules use a hierarchical configuration system:

1. **Default Values**: Built-in defaults for all settings
2. **Environment Files**: `.env` file in project root
3. **System Environment**: OS environment variables
4. **Runtime Override**: Programmatic configuration

### Database Configuration
```python
# Database connection settings
MYSQL_HOST = "localhost"
MYSQL_PORT = 3306
MYSQL_USER = "hcarecloud_user"
MYSQL_PASSWORD = "secure_password"
MYSQL_DATABASE = "hcarecloud"
MYSQL_MANAGER_DATABASE = "hcarecloud_manager"
```

### Security Configuration
```python
# Security settings
SECRET_KEY = "your-secret-key"
JWT_SECRET = "jwt-secret-key"
ENCRYPTION_KEY = "encryption-key"
SESSION_TIMEOUT = 3600
```

## Database Integration

### Connection Management
- **Connection Pooling**: Efficient connection reuse
- **Health Monitoring**: Automatic connection health checks
- **Failover Support**: Automatic failover to backup databases
- **Transaction Safety**: Proper transaction handling

### Multi-Database Support
- **Manager Database**: `hcarecloud_manager` for H-CareManager data
- **HMS Database**: Main H-CareCloud hospital management system
- **Audit Database**: Separate database for audit logs (optional)

### Query Optimization
- **Prepared Statements**: All queries use parameterized statements
- **Connection Reuse**: Efficient connection pooling
- **Query Logging**: Debug logging for development
- **Performance Monitoring**: Query execution time tracking

## Security Standards

### HIPAA Compliance
- **Data Encryption**: All sensitive data encrypted at rest and in transit
- **Audit Logging**: Comprehensive audit trails for all data access
- **Access Control**: Role-based access control (RBAC)
- **Session Management**: Secure session handling

### Authentication & Authorization
- **Token-based Auth**: JWT-like tokens for API authentication
- **Role Management**: Hierarchical role system
- **Permission Checking**: Granular permission validation
- **Session Security**: IP tracking and device fingerprinting

## Development Guidelines

### Adding New Modules
1. Create new `.py` file in `/hcc_pymodules/`
2. Follow naming convention: `hcc_<module_name>.py`
3. Include proper docstrings and type hints
4. Add comprehensive error handling
5. Include unit tests
6. Update this README.md and CHANGELOG.md

### Error Handling
```python
import logging
logger = logging.getLogger('HCareCloud.hcc_pymodules.module_name')

try:
    # Module logic
    return result
except Exception as e:
    logger.error(f"Error in module operation: {e}")
    raise
```

### Logging Standards
- Use structured logging with proper levels
- Include context information (user_id, action, etc.)
- Log security events for audit trails
- Use debug logging for development troubleshooting

## Testing

### Unit Tests
Each module should have corresponding unit tests:
```
/tests/
├── test_hcc_config.py
├── test_hcc_database.py
├── test_hcc_docker.py
└── ...
```

### Integration Tests
Test module interactions and database connectivity:
```python
def test_database_connection():
    connection = hcc_database.get_db_connection()
    assert connection is not None
    connection.close()
```

## Troubleshooting

### Common Issues
1. **Import Errors**: Ensure `/hcc_pymodules/` is in Python path
2. **Database Connection**: Check environment variables and database status
3. **Docker Issues**: Verify Docker daemon is running
4. **Permission Errors**: Check file permissions and user access

### Debug Mode
Enable debug logging by setting:
```bash
export FLASK_ENV=development
export VITE_DEBUG=true
```

## Related Documentation
- `/api/README.md` - API layer documentation
- `/mysql/README.md` - Database schema documentation
- `../themes/hcmpro/README.md` - Frontend documentation

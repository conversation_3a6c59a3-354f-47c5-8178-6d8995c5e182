/**
 * H‑CareCloud Project – Server Access Permissions Check Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @com    <Card>
      <CardHeader className="gap-2">
        <CardTitle>
          <Button mode="link" asChild className="text-xl">
            <Link to="#">Server Admin</Link>
          </Button>{' '}
          Server Access Role Permissions
        </CardTitle>
        <div className="flex gap-5">
          <Button variant="outline">
            View All Permissions
          </Button>
        </div>
      </CardHeader>TD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { Link } from 'react-router';
import { useEffect, useState } from 'react';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface IPermissionData {
  id: number;
  name: string;
  module: string;
  action: string;
  description?: string;
  resource_type: string;
  created_at: string;
}

interface IPermissionsCheckItem {
  module: string;
  view: boolean;
  modify: boolean;
  publish: boolean;
  configure: boolean;
}
type IPermissionsCheckItems = Array<IPermissionsCheckItem>;

const PermissionsCheck = () => {
  // State for API data
  const [permissions, setPermissions] = useState<IPermissionData[]>([]);
  const [loading, setLoading] = useState(true);

  // Load permissions from API on component mount
  useEffect(() => {
    const loadPermissions = async () => {
      try {
        setLoading(true);
        const response = await hcmApi.getPermissions();
        setPermissions(response);
      } catch (error) {
        logger.error('Failed to load server permissions', {
          component: 'PermissionsCheck',
          action: 'loadPermissions'
        }, error as Error);
      } finally {
        setLoading(false);
      }
    };

    loadPermissions();
  }, []);

  // Helper function to group permissions by module and convert to UI format
  const getPermissionsByModule = (): IPermissionsCheckItems => {
    const modules = new Map<string, IPermissionsCheckItem>();

    // Define core server access modules based on your bash script infrastructure
    // These are the MAIN BRANCHES from ru-menu.sh
    const coreModules = [
      'Docker Management',      // Containers, Images, Volumes, Networks
      'Database Operations',    // MySQL, Backups, Migrations
      'System Operations',      // Monitoring, Logs, Resources, DNS fixes
      'Environment Management', // Dev/Staging/Production, Environment Variables
      'Security & Auth',        // User management, Sessions, Permissions
      'Backup & Recovery',      // Professional backup system, Restore operations
    ];

    // Initialize core modules
    coreModules.forEach(moduleName => {
      modules.set(moduleName, {
        module: moduleName,
        view: false,
        modify: false,
        publish: false,
        configure: false,
      });
    });

    // Process API permissions and map to appropriate main branches
    permissions.forEach((permission: IPermissionData) => {
      const module = permission.module || 'Server Access';
      const action = permission.action.toLowerCase();
      
      // Map API permissions to appropriate MAIN BRANCHES
      let targetModule = module;
      if (module.toLowerCase().includes('docker') || permission.resource_type.includes('container')) {
        targetModule = 'Docker Management'; // Includes: containers, images, volumes, networks
      } else if (module.toLowerCase().includes('database') || module.toLowerCase().includes('db')) {
        targetModule = 'Database Operations'; // Includes: MySQL, backups, migrations  
      } else if (module.toLowerCase().includes('system') || module.toLowerCase().includes('monitor') || 
                 module.toLowerCase().includes('log') || module.toLowerCase().includes('dns')) {
        targetModule = 'System Operations'; // Includes: monitoring, logs, resources, DNS
      } else if (module.toLowerCase().includes('environment') || module.toLowerCase().includes('env')) {
        targetModule = 'Environment Management'; // Includes: dev/staging/prod switching
      } else if (module.toLowerCase().includes('security') || module.toLowerCase().includes('auth')) {
        targetModule = 'Security & Auth'; // Includes: users, sessions, permissions
      } else if (module.toLowerCase().includes('backup') || module.toLowerCase().includes('restore')) {
        targetModule = 'Backup & Recovery'; // Includes: backup jobs, restore operations
      }
      
      if (!modules.has(targetModule)) {
        modules.set(targetModule, {
          module: targetModule,
          view: false,
          modify: false,
          publish: false,
          configure: false,
        });
      }

      const modulePermissions = modules.get(targetModule)!;

      // Map different action types to our UI categories
      if (action.includes('view') || action.includes('read') || action.includes('list') || action.includes('get')) {
        modulePermissions.view = true;
      }
      if (action.includes('edit') || action.includes('update') || action.includes('modify') || action.includes('restart')) {
        modulePermissions.modify = true;
      }
      if (action.includes('create') || action.includes('publish') || action.includes('deploy') || action.includes('build')) {
        modulePermissions.publish = true;
      }
      if (action.includes('config') || action.includes('manage') || action.includes('admin') || action.includes('setup')) {
        modulePermissions.configure = true;
      }
    });

    return Array.from(modules.values());
  };

  const data: IPermissionsCheckItems = getPermissionsByModule();

  // Show loading skeleton while data loads
  if (loading) {
    return (
      <div className="grid gap-5">
        {Array.from({ length: 2 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="gap-2">
              <CardTitle>
                <Skeleton className="h-6 w-48" />
              </CardTitle>
              <div className="flex gap-5">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-24" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-12 w-full" />
                {Array.from({ length: 4 }).map((_, rowIndex) => (
                  <div key={rowIndex} className="flex justify-between items-center">
                    <Skeleton className="h-4 w-32" />
                    <div className="flex gap-4">
                      <Skeleton className="h-4 w-4" />
                      <Skeleton className="h-4 w-4" />
                      <Skeleton className="h-4 w-4" />
                      <Skeleton className="h-4 w-4" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="gap-2.5">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-24" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  const renderItem = (each: IPermissionsCheckItem, index: number) => {
    return (
      <TableRow key={index}>
        <TableCell className="py-5.5!">{each.module}</TableCell>
        <TableCell className="py-5.5! text-center">
          <Checkbox checked={each.view} />
        </TableCell>
        <TableCell className="py-5.5! text-center">
          <Checkbox checked={each.modify} />
        </TableCell>
        <TableCell className="py-5.5! text-center">
          <Checkbox checked={each.publish} />
        </TableCell>
        <TableCell className="py-5.5! text-center">
          <Checkbox checked={each.configure} />
        </TableCell>
      </TableRow>
    );
  };

  return (
    <Card>
      <CardHeader className="gap-2">
        <CardTitle>
          <Button mode="link" asChild className="text-xl">
            <Link to="#">Project Manager</Link>
          </Button>{' '}
          Role Permissions
        </CardTitle>
        <div className="flex gap-5">
          <Button variant="outline">
            <Link to="#">New Permission</Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="kt-scrollable-x-auto p-0">
        <Table>
          <TableHeader>
            <TableRow className="bg-accent/60">
              <TableHead className="text-start text-secondary-foreground font-normal min-w-[300px] h-10">
                Module
              </TableHead>
              <TableHead className="min-w-24 text-secondary-foreground font-normal text-center h-10">
                View
              </TableHead>
              <TableHead className="min-w-24 text-secondary-foreground font-normal text-center h-10">
                Modify
              </TableHead>
              <TableHead className="min-w-24 text-secondary-foreground font-normal text-center h-10">
                Publish
              </TableHead>
              <TableHead className="min-w-24 text-secondary-foreground font-normal text-center h-10">
                Configure
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="text-mono font-medium">
            {data.map((each, index) => {
              return renderItem(each, index);
            })}
          </TableBody>
        </Table>
      </CardContent>
      <CardFooter className="justify-end py-7.5 gap-2.5">
        <Button variant="outline">
          <Link to="#">Restore Defaults</Link>
        </Button>
        <Button>
          <Link to="#">Save Changes</Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export {
  PermissionsCheck,
  type IPermissionsCheckItem,
  type IPermissionsCheckItems,
};

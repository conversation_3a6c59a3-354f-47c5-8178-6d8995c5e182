# H-CareManager Routing System

## Overview
This directory contains the routing configuration for H-CareManager Project, implementing React Router with protected routes, role-based access control, and dynamic navigation. The routing system provides secure navigation for server management functionality while maintaining clean URL structure and user experience.

## Architecture

### Routing Structure
```
/src/routing/
├── index.tsx               # Main routing configuration
├── ProtectedRoute.tsx      # Route protection wrapper
├── PublicRoute.tsx         # Public route wrapper
├── RouteGuard.tsx          # Permission-based route guard
├── routes/                 # Route definitions
│   ├── auth-routes.tsx     # Authentication routes
│   ├── account-routes.tsx  # Account management routes
│   ├── dashboard-routes.tsx # Dashboard routes
│   ├── network-routes.tsx  # Network management routes
│   └── public-routes.tsx   # Public profile routes
├── navigation/             # Navigation components
│   ├── Sidebar.tsx         # Main navigation sidebar
│   ├── Breadcrumbs.tsx     # Breadcrumb navigation
│   └── TabNavigation.tsx   # Tab-based navigation
└── utils/                  # Routing utilities
    ├── route-utils.ts      # Route helper functions
    ├── navigation-utils.ts # Navigation utilities
    └── url-utils.ts        # URL manipulation utilities
```

### Route Categories
- **Public Routes**: Authentication and public pages
- **Protected Routes**: Authenticated user routes
- **Account Routes**: User account management
- **Dashboard Routes**: Server management dashboards
- **Network Routes**: Network and team management
- **Admin Routes**: Administrative functionality

## Core Routing Components

### index.tsx
**Purpose**: Main routing configuration with React Router setup.

**Implementation**:
```typescript
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { ProtectedRoute } from './ProtectedRoute';
import { PublicRoute } from './PublicRoute';
import { RouteGuard } from './RouteGuard';

// Route imports
import { authRoutes } from './routes/auth-routes';
import { accountRoutes } from './routes/account-routes';
import { dashboardRoutes } from './routes/dashboard-routes';
import { networkRoutes } from './routes/network-routes';
import { publicRoutes } from './routes/public-routes';

// Layout imports
import { MainLayout } from '@/layouts/MainLayout';
import { AuthLayout } from '@/layouts/AuthLayout';
import { ErrorBoundary } from '@/components/shared/ErrorBoundary';

const router = createBrowserRouter([
  // Public routes (authentication)
  {
    path: '/auth',
    element: (
      <PublicRoute>
        <AuthLayout />
      </PublicRoute>
    ),
    children: authRoutes,
    errorElement: <ErrorBoundary />
  },
  
  // Protected routes (main application)
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <MainLayout />
      </ProtectedRoute>
    ),
    children: [
      // Dashboard routes
      {
        index: true,
        element: <Navigate to="/dashboard" replace />
      },
      ...dashboardRoutes,
      
      // Account management routes
      {
        path: 'account',
        children: accountRoutes
      },
      
      // Network management routes
      {
        path: 'network',
        element: (
          <RouteGuard requiredPermission="network.view">
            <Outlet />
          </RouteGuard>
        ),
        children: networkRoutes
      },
      
      // Public profile routes
      {
        path: 'public-profile',
        children: publicRoutes
      }
    ],
    errorElement: <ErrorBoundary />
  },
  
  // Catch-all route
  {
    path: '*',
    element: <Navigate to="/dashboard" replace />
  }
]);

export const AppRouter: React.FC = () => {
  return (
    <ErrorBoundary>
      <RouterProvider router={router} />
    </ErrorBoundary>
  );
};
```

### ProtectedRoute.tsx
**Purpose**: Route protection for authenticated users.

**Implementation**:
```typescript
interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  fallback = <Navigate to="/auth/signin" replace />
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <Skeleton className="w-100 h-100" />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    // Save the attempted location for redirect after login
    sessionStorage.setItem('redirect_after_login', location.pathname);
    return fallback;
  }

  return <>{children}</>;
};
```

### RouteGuard.tsx
**Purpose**: Permission-based route protection.

**Implementation**:
```typescript
interface RouteGuardProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredRole?: string;
  requiredPermissions?: string[];
  fallback?: React.ReactNode;
}

export const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  requiredPermission,
  requiredRole,
  requiredPermissions,
  fallback = <AccessDenied />
}) => {
  const { checkPermission, hasRole, hasAllPermissions } = usePermissions();
  const { user } = useAuth();

  // Check single permission
  if (requiredPermission && !checkPermission(requiredPermission)) {
    logger.warn('Access denied: insufficient permission', {
      userId: user?.id,
      requiredPermission,
      userPermissions: user?.permissions
    });
    return fallback;
  }

  // Check role requirement
  if (requiredRole && !hasRole(requiredRole)) {
    logger.warn('Access denied: insufficient role', {
      userId: user?.id,
      requiredRole,
      userRole: user?.role
    });
    return fallback;
  }

  // Check multiple permissions
  if (requiredPermissions && !hasAllPermissions(requiredPermissions)) {
    logger.warn('Access denied: insufficient permissions', {
      userId: user?.id,
      requiredPermissions,
      userPermissions: user?.permissions
    });
    return fallback;
  }

  return <>{children}</>;
};

// Access denied component
const AccessDenied: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="d-flex flex-column align-items-center justify-content-center min-vh-100">
      <div className="text-center">
        <h1 className="display-1 fw-bold">403</h1>
        <p className="fs-3">
          <span className="text-danger">Access Denied</span>
        </p>
        <p className="lead">
          You don't have permission to access this resource.
        </p>
        <Button
          variant="primary"
          onClick={() => navigate('/dashboard')}
        >
          Go to Dashboard
        </Button>
      </div>
    </div>
  );
};
```

## Route Definitions

### account-routes.tsx
**Purpose**: Account management route definitions.

**Implementation**:
```typescript
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

// Lazy load account components
const AccountLayout = lazy(() => import('@/pages/account/AccountLayout'));
const SecurityPage = lazy(() => import('@/pages/account/security/SecurityPage'));
const ServersPage = lazy(() => import('@/pages/account/servers/ServersPage'));
const ApiKeysPage = lazy(() => import('@/pages/account/api-keys/ApiKeysPage'));
const BillingPage = lazy(() => import('@/pages/account/billing/BillingPage'));
const NotificationsPage = lazy(() => import('@/pages/account/notifications/NotificationsPage'));
const IntegrationsPage = lazy(() => import('@/pages/account/integrations/IntegrationsPage'));
const AppearancePage = lazy(() => import('@/pages/account/appearance/AppearancePage'));
const HomePage = lazy(() => import('@/pages/account/home/<USER>'));
const ActivityPage = lazy(() => import('@/pages/account/activity/ActivityPage'));

export const accountRoutes: RouteObject[] = [
  {
    path: '',
    element: (
      <Suspense fallback={<Skeleton className="w-100 h-100" />}>
        <AccountLayout />
      </Suspense>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="home" replace />
      },
      {
        path: 'home',
        element: (
          <Suspense fallback={<Skeleton className="w-100 h-64" />}>
            <HomePage />
          </Suspense>
        )
      },
      {
        path: 'security',
        element: (
          <Suspense fallback={<Skeleton className="w-100 h-64" />}>
            <SecurityPage />
          </Suspense>
        )
      },
      {
        path: 'servers',
        element: (
          <RouteGuard requiredPermission="server.view">
            <Suspense fallback={<Skeleton className="w-100 h-64" />}>
              <ServersPage />
            </Suspense>
          </RouteGuard>
        )
      },
      {
        path: 'api-keys',
        element: (
          <RouteGuard requiredPermission="api.view">
            <Suspense fallback={<Skeleton className="w-100 h-64" />}>
              <ApiKeysPage />
            </Suspense>
          </RouteGuard>
        )
      },
      {
        path: 'billing',
        element: (
          <Suspense fallback={<Skeleton className="w-100 h-64" />}>
            <BillingPage />
          </Suspense>
        )
      },
      {
        path: 'notifications',
        element: (
          <Suspense fallback={<Skeleton className="w-100 h-64" />}>
            <NotificationsPage />
          </Suspense>
        )
      },
      {
        path: 'integrations',
        element: (
          <RouteGuard requiredPermission="integration.view">
            <Suspense fallback={<Skeleton className="w-100 h-64" />}>
              <IntegrationsPage />
            </Suspense>
          </RouteGuard>
        )
      },
      {
        path: 'appearance',
        element: (
          <Suspense fallback={<Skeleton className="w-100 h-64" />}>
            <AppearancePage />
          </Suspense>
        )
      },
      {
        path: 'activity',
        element: (
          <RouteGuard requiredPermission="audit.view">
            <Suspense fallback={<Skeleton className="w-100 h-64" />}>
              <ActivityPage />
            </Suspense>
          </RouteGuard>
        )
      }
    ]
  }
];
```

### dashboard-routes.tsx
**Purpose**: Dashboard route definitions.

**Implementation**:
```typescript
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

// Lazy load dashboard components
const DashboardPage = lazy(() => import('@/pages/dashboards/DashboardPage'));
const ServerDashboard = lazy(() => import('@/pages/dashboards/ServerDashboard'));
const AnalyticsDashboard = lazy(() => import('@/pages/dashboards/AnalyticsDashboard'));

export const dashboardRoutes: RouteObject[] = [
  {
    path: 'dashboard',
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<Skeleton className="w-100 h-100" />}>
            <DashboardPage />
          </Suspense>
        )
      },
      {
        path: 'servers',
        element: (
          <RouteGuard requiredPermission="server.view">
            <Suspense fallback={<Skeleton className="w-100 h-100" />}>
              <ServerDashboard />
            </Suspense>
          </RouteGuard>
        )
      },
      {
        path: 'analytics',
        element: (
          <RouteGuard requiredPermission="analytics.view">
            <Suspense fallback={<Skeleton className="w-100 h-100" />}>
              <AnalyticsDashboard />
            </Suspense>
          </RouteGuard>
        )
      }
    ]
  }
];
```

## Navigation Components

### Sidebar.tsx
**Purpose**: Main navigation sidebar with role-based menu items.

**Implementation**:
```typescript
interface SidebarProps {
  isCollapsed?: boolean;
  onToggle?: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed = false,
  onToggle
}) => {
  const location = useLocation();
  const { checkPermission, hasRole } = usePermissions();
  const { user } = useAuth();

  const menuItems = useMemo(() => [
    {
      title: 'Dashboard',
      path: '/dashboard',
      icon: 'dashboard',
      permission: null
    },
    {
      title: 'Server Management',
      path: '/dashboard/servers',
      icon: 'server',
      permission: 'server.view'
    },
    {
      title: 'Analytics',
      path: '/dashboard/analytics',
      icon: 'chart',
      permission: 'analytics.view'
    },
    {
      title: 'Network',
      path: '/network',
      icon: 'network',
      permission: 'network.view',
      children: [
        {
          title: 'Teams',
          path: '/network/teams',
          permission: 'network.teams'
        },
        {
          title: 'Departments',
          path: '/network/departments',
          permission: 'network.departments'
        }
      ]
    },
    {
      title: 'Account',
      path: '/account',
      icon: 'user',
      permission: null,
      children: [
        {
          title: 'Profile',
          path: '/account/home',
          permission: null
        },
        {
          title: 'Security',
          path: '/account/security',
          permission: null
        },
        {
          title: 'Servers',
          path: '/account/servers',
          permission: 'server.view'
        },
        {
          title: 'API Keys',
          path: '/account/api-keys',
          permission: 'api.view'
        },
        {
          title: 'Billing',
          path: '/account/billing',
          permission: null
        }
      ]
    }
  ], []);

  const filteredMenuItems = useMemo(() => {
    return menuItems.filter(item => {
      if (item.permission && !checkPermission(item.permission)) {
        return false;
      }
      
      if (item.children) {
        item.children = item.children.filter(child => 
          !child.permission || checkPermission(child.permission)
        );
      }
      
      return true;
    });
  }, [menuItems, checkPermission]);

  const isActiveRoute = (path: string): boolean => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  return (
    <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="sidebar-header">
        <div className="sidebar-brand">
          {!isCollapsed && (
            <span className="brand-text">H-CareManager</span>
          )}
        </div>
        <button
          className="sidebar-toggle"
          onClick={onToggle}
          aria-label="Toggle sidebar"
        >
          <i className="fas fa-bars" />
        </button>
      </div>
      
      <nav className="sidebar-nav">
        <ul className="nav-list">
          {filteredMenuItems.map((item, index) => (
            <SidebarMenuItem
              key={index}
              item={item}
              isActive={isActiveRoute(item.path)}
              isCollapsed={isCollapsed}
            />
          ))}
        </ul>
      </nav>
      
      <div className="sidebar-footer">
        <div className="user-info">
          {!isCollapsed && (
            <>
              <img
                src={user?.avatar || '/default-avatar.png'}
                alt={user?.name}
                className="user-avatar"
              />
              <div className="user-details">
                <div className="user-name">{user?.name}</div>
                <div className="user-role">{user?.role}</div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
```

### Breadcrumbs.tsx
**Purpose**: Breadcrumb navigation for current page context.

**Implementation**:
```typescript
interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: string;
}

export const Breadcrumbs: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const breadcrumbs = useMemo(() => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const items: BreadcrumbItem[] = [
      { label: 'Dashboard', path: '/dashboard', icon: 'home' }
    ];

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      // Skip the first segment if it's 'dashboard'
      if (segment === 'dashboard' && index === 0) return;
      
      const label = getBreadcrumbLabel(segment, currentPath);
      const isLast = index === pathSegments.length - 1;
      
      items.push({
        label,
        path: isLast ? undefined : currentPath,
        icon: getBreadcrumbIcon(segment)
      });
    });

    return items;
  }, [location.pathname]);

  const getBreadcrumbLabel = (segment: string, path: string): string => {
    const labelMap: Record<string, string> = {
      'account': 'Account',
      'security': 'Security',
      'servers': 'Servers',
      'api-keys': 'API Keys',
      'billing': 'Billing',
      'notifications': 'Notifications',
      'integrations': 'Integrations',
      'appearance': 'Appearance',
      'activity': 'Activity',
      'network': 'Network',
      'teams': 'Teams',
      'departments': 'Departments'
    };

    return labelMap[segment] || capitalize(segment);
  };

  const getBreadcrumbIcon = (segment: string): string | undefined => {
    const iconMap: Record<string, string> = {
      'account': 'user',
      'security': 'shield',
      'servers': 'server',
      'api-keys': 'key',
      'billing': 'credit-card',
      'network': 'network-wired'
    };

    return iconMap[segment];
  };

  return (
    <nav aria-label="breadcrumb">
      <ol className="breadcrumb">
        {breadcrumbs.map((item, index) => (
          <li
            key={index}
            className={`breadcrumb-item ${!item.path ? 'active' : ''}`}
          >
            {item.path ? (
              <button
                className="breadcrumb-link"
                onClick={() => navigate(item.path!)}
              >
                {item.icon && <i className={`fas fa-${item.icon} me-1`} />}
                {item.label}
              </button>
            ) : (
              <span>
                {item.icon && <i className={`fas fa-${item.icon} me-1`} />}
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};
```

## Routing Utilities

### route-utils.ts
**Purpose**: Route helper functions and utilities.

**Implementation**:
```typescript
export const generatePath = (pattern: string, params: Record<string, string>): string => {
  return Object.keys(params).reduce((path, key) => {
    return path.replace(`:${key}`, params[key]);
  }, pattern);
};

export const parseSearchParams = (search: string): Record<string, string> => {
  const params = new URLSearchParams(search);
  const result: Record<string, string> = {};
  
  params.forEach((value, key) => {
    result[key] = value;
  });
  
  return result;
};

export const buildSearchParams = (params: Record<string, string>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      searchParams.set(key, value);
    }
  });
  
  return searchParams.toString();
};

export const isExternalUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    return urlObj.origin !== window.location.origin;
  } catch {
    return false;
  }
};

export const getRouteTitle = (pathname: string): string => {
  const titleMap: Record<string, string> = {
    '/dashboard': 'Dashboard',
    '/dashboard/servers': 'Server Management',
    '/dashboard/analytics': 'Analytics',
    '/account/home': 'Profile',
    '/account/security': 'Security Settings',
    '/account/servers': 'Server Access',
    '/account/api-keys': 'API Keys',
    '/account/billing': 'Billing & Subscription'
  };

  return titleMap[pathname] || 'H-CareManager';
};

// Usage examples
const serverPath = generatePath('/servers/:id/edit', { id: '123' });
const queryParams = parseSearchParams('?page=1&limit=10');
const searchString = buildSearchParams({ page: '2', filter: 'active' });
const title = getRouteTitle('/account/security');
```

## Related Documentation
- [Authentication Documentation](../auth/README.md)
- [Components Documentation](../components/README.md)
- [Pages Documentation](../pages/README.md)
- [H-CareManager Project Documentation](../../docs.md)

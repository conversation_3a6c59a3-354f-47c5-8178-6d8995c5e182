# H-CareManager Services Layer

## Overview
This directory contains the service layer for H-CareManager Project, providing API integration, data management, and external service communication. The services layer abstracts backend communication and provides a clean interface for React components to interact with H-CareCloud APIs and external services.

## Architecture

### Services Structure
```
/src/services/
├── hcm-api.ts              # Main H-CareManager API service
├── auth-service.ts         # Authentication service integration
├── storage-service.ts      # Local storage and session management
├── notification-service.ts # Toast notifications and alerts
├── websocket-service.ts    # Real-time communication
└── external-services/      # External API integrations
    ├── docker-api.ts       # Docker container management
    ├── monitoring-api.ts   # System monitoring integration
    └── backup-api.ts       # Backup service integration
```

### Service Categories
- **Core API Services**: Primary backend communication
- **Authentication Services**: User authentication and session management
- **Storage Services**: Data persistence and caching
- **Notification Services**: User feedback and alerts
- **Real-time Services**: WebSocket and live updates
- **External Services**: Third-party API integrations

## Core Services

### hcm-api.ts
**Purpose**: Main API service for H-CareManager backend communication.

**Key Features**:
- Centralized HTTP client with authentication
- Request/response interceptors for error handling
- Automatic token management and refresh
- Type-safe API method definitions
- Environment-based URL configuration

**API Endpoints**:
```typescript
// User Management
async getProfile(): Promise<UserProfile>
async updateProfile(data: Partial<UserProfile>): Promise<void>
async uploadAvatar(file: File): Promise<string>

// Authentication
async login(credentials: LoginCredentials): Promise<AuthResponse>
async logout(): Promise<void>
async refreshToken(): Promise<string>

// Server Management
async getServerAdmins(): Promise<ServerAdmin[]>
async getServerPermissions(): Promise<Permissions>
async updateServerPermissions(userId: string, permissions: string[]): Promise<void>

// Account Management
async getApiKeys(): Promise<ApiKey[]>
async createApiKey(data: CreateApiKeyRequest): Promise<ApiKey>
async deleteApiKey(token: string): Promise<void>

// Billing
async getBillingPlans(): Promise<BillingPlan[]>
async getSubscription(): Promise<Subscription>
async getBillingHistory(): Promise<BillingHistory[]>

// Security
async getSecuritySettings(): Promise<SecuritySettings>
async updateSecuritySettings(settings: SecuritySettings): Promise<void>
async getAuditLog(): Promise<AuditLogEntry[]>

// Notifications
async getNotifications(): Promise<Notification[]>
async markNotificationRead(id: string): Promise<void>
async getNotificationSettings(): Promise<NotificationSettings>
```

### auth-service.ts
**Purpose**: Authentication state management and token handling.

**Key Features**:
- Token storage and validation
- Authentication state persistence
- Automatic logout on token expiration
- Role-based access control
- Session monitoring

**Usage**:
```typescript
import { authService } from '@/services/auth-service';

// Check authentication status
const isAuthenticated = authService.isAuthenticated();

// Get current user
const user = authService.getCurrentUser();

// Handle logout
await authService.logout();
```

### storage-service.ts
**Purpose**: Local storage and session management with encryption.

**Key Features**:
- Encrypted local storage for sensitive data
- Session storage for temporary data
- Automatic data expiration
- Type-safe storage operations
- HIPAA-compliant data handling

**Usage**:
```typescript
import { storageService } from '@/services/storage-service';

// Store encrypted data
storageService.setSecure('user-preferences', preferences);

// Retrieve encrypted data
const preferences = storageService.getSecure('user-preferences');

// Session storage
storageService.setSession('temp-data', data);
```

## API Integration Patterns

### Request Configuration
```typescript
// Base configuration
const apiConfig = {
  baseURL: import.meta.env.VITE_APP_API_URL,
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000'),
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

// Authentication interceptor
axios.interceptors.request.use((config) => {
  const token = authService.getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

### Error Handling
```typescript
// Response interceptor for error handling
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      authService.logout();
      window.location.href = '/auth/signin';
    }
    
    if (error.response?.status >= 500) {
      notificationService.error('Server error occurred');
    }
    
    return Promise.reject(error);
  }
);
```

### Type Safety
```typescript
// API response types
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// Service method with type safety
async getServerAdmins(): Promise<ServerAdmin[]> {
  const response = await this.request<ServerAdmin[]>('/api/user/servers/admins');
  return response.data || [];
}
```

## Real-time Services

### WebSocket Integration
```typescript
// websocket-service.ts
class WebSocketService {
  private ws: WebSocket | null = null;
  
  connect(url: string): void {
    this.ws = new WebSocket(url);
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };
  }
  
  subscribe(channel: string, callback: (data: any) => void): void {
    // Subscribe to real-time updates
  }
}
```

### Notification Service
```typescript
// notification-service.ts
class NotificationService {
  success(message: string): void {
    toast.success(message);
  }
  
  error(message: string): void {
    toast.error(message);
  }
  
  info(message: string): void {
    toast.info(message);
  }
}
```

## External Service Integrations

### Docker API Integration
```typescript
// external-services/docker-api.ts
class DockerApiService {
  async getContainers(): Promise<Container[]> {
    return this.request('/api/docker/containers');
  }
  
  async restartContainer(id: string): Promise<void> {
    return this.request(`/api/docker/containers/${id}/restart`, {
      method: 'POST'
    });
  }
}
```

### Monitoring Integration
```typescript
// external-services/monitoring-api.ts
class MonitoringApiService {
  async getSystemMetrics(): Promise<SystemMetrics> {
    return this.request('/api/monitoring/metrics');
  }
  
  async getHealthStatus(): Promise<HealthStatus> {
    return this.request('/api/monitoring/health');
  }
}
```

## Environment Configuration

### API URLs
```typescript
// Environment-based configuration
const API_CONFIG = {
  HCM_API_URL: import.meta.env.VITE_APP_API_URL,
  HMS_API_URL: import.meta.env.VITE_HMS_API_URL,
  WEBSOCKET_URL: import.meta.env.VITE_WEBSOCKET_URL,
  TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000'),
  MAX_RETRIES: parseInt(import.meta.env.VITE_MAX_RETRIES || '3')
};
```

### Development vs Production
```typescript
// Development configuration
if (import.meta.env.VITE_DEV) {
  // Enable request/response logging
  axios.interceptors.request.use(request => {
    console.log('API Request:', request);
    return request;
  });
}
```

## Security Standards

### HIPAA Compliance
- **Data Encryption**: All sensitive data encrypted in transit and at rest
- **Access Logging**: All API calls logged for audit trails
- **Token Security**: Secure token storage and automatic expiration
- **Session Management**: Secure session handling with IP tracking

### Authentication Security
```typescript
// Secure token handling
class AuthService {
  private tokenKey = 'hcm_auth_token';
  
  setToken(token: string): void {
    // Store token securely
    storageService.setSecure(this.tokenKey, token);
  }
  
  getToken(): string | null {
    return storageService.getSecure(this.tokenKey);
  }
  
  isTokenValid(): boolean {
    const token = this.getToken();
    if (!token) return false;
    
    // Validate token expiration
    const payload = this.decodeToken(token);
    return payload.exp > Date.now() / 1000;
  }
}
```

## Performance Optimization

### Request Caching
```typescript
// Cache frequently accessed data
class CacheService {
  private cache = new Map<string, { data: any; expiry: number }>();
  
  set(key: string, data: any, ttl: number = 300000): void {
    this.cache.set(key, {
      data,
      expiry: Date.now() + ttl
    });
  }
  
  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item || item.expiry < Date.now()) {
      this.cache.delete(key);
      return null;
    }
    return item.data;
  }
}
```

### Request Debouncing
```typescript
// Debounce API calls
const debouncedSearch = debounce(async (query: string) => {
  const results = await hcmApi.search(query);
  setSearchResults(results);
}, 300);
```

## Error Handling and Logging

### Comprehensive Error Handling
```typescript
// Service error handling
class ApiService {
  async request<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new ApiError(response.status, response.statusText);
      }
      
      return await response.json();
    } catch (error) {
      logger.error('API request failed', {
        url,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      throw error;
    }
  }
}
```

## Testing and Development

### Service Testing
```typescript
// Mock service for testing
export const mockHcmApi = {
  getProfile: jest.fn().mockResolvedValue(mockUserProfile),
  getServerAdmins: jest.fn().mockResolvedValue(mockServerAdmins),
  // ... other mocked methods
};
```

### Development Tools
```typescript
// Development debugging
if (import.meta.env.VITE_DEBUG) {
  window.hcmApi = hcmApi;
  window.authService = authService;
}
```

## Related Documentation
- [Authentication Documentation](../auth/README.md)
- [API Integration Documentation](../../../../api/README.md)
- [Component Integration Documentation](../components/README.md)
- [H-CareManager Project Documentation](../../docs.md)

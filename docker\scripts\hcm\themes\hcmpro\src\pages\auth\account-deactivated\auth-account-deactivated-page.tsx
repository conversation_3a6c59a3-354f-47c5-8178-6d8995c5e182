/**
 * H‑CareCloud Project – Account Deactivated Page
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { Fragment, useState } from 'react';
import { PageNavbar } from '@/pages/account';
import { AccountGetStartedContent } from '@/pages/account/home/<USER>';
import {
  Toolbar,
  ToolbarDescription,
  ToolbarHeading,
  ToolbarPageTitle,
} from '@/partials/common/toolbar';
import { AccountDeactivatedDialog } from '@/partials/dialogs/account-deactivated-dialog';
import { Link } from 'react-router';
import { Button } from '@/components/ui/button';
import { Container } from '@/components/common/container';
import { useAuth } from '@/auth/providers/hcm-auth-provider';

export function AuthAccountDeactivatedPage() {
  const { user } = useAuth();
  const [profileModalOpen, setProfileModalOpen] = useState(true);

  const handleClose = () => {
    setProfileModalOpen(false);
  };

  return (
    <Fragment>
      <PageNavbar />
      <Container>
        <Toolbar>
          <ToolbarHeading>
            <ToolbarPageTitle />
            <ToolbarDescription>
              <div className="flex items-center gap-2 text-sm font-medium">
                <span className="text-foreground font-medium">
                  {user?.name || 'H-CareManager User'}
                </span>
                <Link
                  to={`mailto:${user?.email || ''}`}
                  className="text-secondary-foreground hover:text-primary"
                >
                  {user?.email || '<EMAIL>'}
                </Link>
                <span className="size-0.75 bg-mono/50 rounded-full"></span>
                <Button mode="link" asChild>
                  <Link to="/account/servers/server-info">Server Info</Link>
                </Button>
              </div>
            </ToolbarDescription>
          </ToolbarHeading>
        </Toolbar>
      </Container>
      <Container>
        <AccountGetStartedContent />
        <AccountDeactivatedDialog
          open={profileModalOpen}
          onOpenChange={handleClose}
        />
      </Container>
    </Fragment>
  );
}

"""
H‑CareCloud Project – Server Management API
<AUTHOR> <<EMAIL>>
@scrum_master <PERSON><PERSON> <<EMAIL>>
@company Hostwek LTD – WekTurbo Dev
@website https://hostwek.com/wekturbo
@support <EMAIL>
@version 1.0.8
@year 2025

Server management for H-CareManager server access using production database.
"""

from flask import Blueprint, request, jsonify, g
import json
from datetime import datetime
from .auth import get_db_connection, require_auth
import logging

servers_bp = Blueprint('servers', __name__, url_prefix='/api/user/servers')

@servers_bp.route('/admins', methods=['GET'])
@require_auth
def get_server_admins():
    """Get all server administrators with their roles and details."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Get server administrators with their roles and session data
        query = """
            SELECT DISTINCT
                u.id,
                u.name,
                u.email,
                u.status,
                u.avatar,
                u.location,
                u.created_at,
                GROUP_CONCAT(DISTINCT r.name ORDER BY r.level DESC SEPARATOR ',') as roles,
                COALESCE(r_primary.name, 'No Role') as role,
                COALESCE(us.last_activity, u.created_at) as last_login_at,
                COALESCE(task_counts.task_count, 0) as task_count
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            LEFT JOIN roles r ON ur.role_id = r.id
            LEFT JOIN roles r_primary ON ur.role_id = r_primary.id AND ur.is_primary = 1
            LEFT JOIN user_sessions us ON u.id = us.user_id AND us.is_current = 1
            LEFT JOIN (
                SELECT user_id, COUNT(*) as task_count 
                FROM activity_log 
                WHERE action LIKE '%task%' OR action LIKE '%assignment%'
                GROUP BY user_id
            ) task_counts ON u.id = task_counts.user_id
            WHERE u.id != %s
            GROUP BY u.id, u.name, u.email, u.status, u.avatar, u.location, u.created_at, r_primary.name, us.last_activity, task_counts.task_count
            ORDER BY u.created_at DESC
        """
        
        cursor.execute(query, (g.user['id'],))
        members = cursor.fetchall()
        
        # Transform data to match frontend expectations
        admin_list = []
        for admin in members:
            server_admin = {
                'id': admin['id'],
                'name': admin['name'],
                'email': admin['email'],
                'role': admin['role'],
                'roles': admin['roles'] if admin['roles'] else admin['role'],
                'status': admin['status'],
                'avatar': admin['avatar'],
                'location': admin['location'] or 'Server Location',
                'last_login_at': admin['last_login_at'].isoformat() if admin['last_login_at'] else None,
                'task_count': admin['task_count'],
                'created_at': admin['created_at'].isoformat() if admin['created_at'] else None
            }
            admin_list.append(server_admin)
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'data': admin_list
        })

    except Exception as e:
        logging.error(f"Error fetching server administrators: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to fetch server administrators'
        }), 500

@servers_bp.route('/overview', methods=['GET'])
@require_auth
def get_server_overview():
    """Get server overview statistics."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Get total server admin count
        cursor.execute("SELECT COUNT(*) as total FROM users WHERE id != %s", (g.user['id'],))
        total_admins = cursor.fetchone()['total']

        # Get active admin count (logged in recently)
        cursor.execute("""
            SELECT COUNT(DISTINCT u.id) as active
            FROM users u
            LEFT JOIN user_sessions us ON u.id = us.user_id
            WHERE u.id != %s AND (us.last_activity > DATE_SUB(NOW(), INTERVAL 30 DAY) OR u.status = 'active')
        """, (g.user['id'],))
        active_admins = cursor.fetchone()['active']
        
        # Get department count
        cursor.execute("SELECT COUNT(*) as departments FROM departments WHERE status = 'active'")
        department_count = cursor.fetchone()['departments']
        
        # Get role count
        cursor.execute("SELECT COUNT(*) as roles FROM roles WHERE status = 'active'")
        role_count = cursor.fetchone()['roles']
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'data': {
                'total_admins': total_admins,
                'active_admins': active_admins,
                'departments': department_count,
                'roles': role_count
            }
        })

    except Exception as e:
        logging.error(f"Error fetching server overview: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to fetch server overview'
        }), 500

@servers_bp.route('/departments', methods=['GET'])
@require_auth
def get_departments():
    """Get all departments."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        query = """
            SELECT 
                d.id,
                d.name,
                d.description,
                d.status,
                d.created_at,
                u.name as manager_name
            FROM departments d
            LEFT JOIN users u ON d.manager_id = u.id
            WHERE d.status = 'active'
            ORDER BY d.name
        """
        
        cursor.execute(query)
        departments = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'data': departments
        })
        
    except Exception as e:
        logging.error(f"Error fetching departments: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to fetch departments'
        }), 500

@servers_bp.route('/roles', methods=['GET'])
@require_auth
def get_roles():
    """Get all roles."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        query = """
            SELECT 
                id,
                name,
                description,
                level,
                permissions,
                status,
                created_at
            FROM roles
            WHERE status = 'active'
            ORDER BY level DESC, name
        """
        
        cursor.execute(query)
        roles = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'data': roles
        })
        
    except Exception as e:
        logging.error(f"Error fetching roles: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to fetch roles'
        }), 500

@servers_bp.route('/permissions', methods=['GET'])
@require_auth
def get_server_permissions():
    """Get server permissions for the authenticated user."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        user_id = g.user['id']

        # Get user permissions through roles
        query = """
            SELECT DISTINCT
                r.permissions,
                r.name as role_name,
                r.level as role_level
            FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = %s AND r.status = 'active'
            ORDER BY r.level DESC
        """

        cursor.execute(query, (user_id,))
        user_roles = cursor.fetchall()

        # Combine all permissions
        all_permissions = set()
        roles = []

        for role in user_roles:
            roles.append({
                'name': role['role_name'],
                'level': role['role_level']
            })

            if role['permissions']:
                try:
                    permissions = json.loads(role['permissions'])
                    if isinstance(permissions, list):
                        all_permissions.update(permissions)
                    elif isinstance(permissions, dict):
                        all_permissions.update(permissions.keys())
                except json.JSONDecodeError:
                    # Handle legacy comma-separated permissions
                    perms = role['permissions'].split(',')
                    all_permissions.update([p.strip() for p in perms])

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'permissions': list(all_permissions),
                'roles': roles,
                'user_id': user_id
            }
        })

    except Exception as e:
        logging.error(f"Error fetching server permissions: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to fetch server permissions'
        }), 500

@servers_bp.route('/permissions', methods=['PUT'])
@require_auth
def update_server_permissions():
    """Update server permissions (admin only)."""
    try:
        data = request.get_json()

        if not data or 'user_id' not in data or 'permissions' not in data:
            return jsonify({
                'success': False,
                'message': 'User ID and permissions are required'
            }), 400

        # Check if current user has admin privileges
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Verify admin permissions
        cursor.execute("""
            SELECT r.level FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = %s AND r.status = 'active'
            ORDER BY r.level DESC LIMIT 1
        """, (g.user['id'],))

        admin_role = cursor.fetchone()
        if not admin_role or admin_role['level'] < 8:  # Assuming level 8+ is admin
            return jsonify({
                'success': False,
                'message': 'Insufficient permissions to update user permissions'
            }), 403

        # Update user permissions by updating their role
        target_user_id = data['user_id']
        new_permissions = data['permissions']

        # Find appropriate role or create custom permissions
        # For now, we'll update the user's primary role permissions
        cursor.execute("""
            UPDATE roles r
            JOIN user_roles ur ON r.id = ur.role_id
            SET r.permissions = %s, r.updated_at = NOW()
            WHERE ur.user_id = %s AND ur.is_primary = 1
        """, (json.dumps(new_permissions), target_user_id))

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'Server permissions updated successfully'
        })

    except Exception as e:
        logging.error(f"Error updating server permissions: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to update server permissions'
        }), 500

@servers_bp.route('/admins', methods=['POST'])
@require_auth
def create_server_admin():
    """Create a new server administrator."""
    try:
        data = request.get_json()
        
        if not data.get('name') or not data.get('email'):
            return jsonify({
                'success': False,
                'message': 'Name and email are required'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Insert new user
        user_query = """
            INSERT INTO users (name, email, password, status, location, avatar, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        
        user_values = (
            data['name'],
            data['email'],
            data.get('password', 'temp_password'),  # Should be properly hashed
            data.get('status', 'active'),
            data.get('location', 'Server Location'),
            data.get('avatar'),
            datetime.now()
        )
        
        cursor.execute(user_query, user_values)
        user_id = cursor.lastrowid
        
        # Assign role if provided
        if data.get('role_id'):
            role_query = """
                INSERT INTO user_roles (user_id, role_id, assigned_by, is_primary, created_at)
                VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(role_query, (user_id, data['role_id'], g.user['id'], 1, datetime.now()))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Server administrator created successfully',
            'data': {'id': user_id}
        })

    except Exception as e:
        logging.error(f"Error creating server administrator: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to create server administrator'
        }), 500

@servers_bp.route('/admins/<int:admin_id>', methods=['PUT'])
@require_auth
def update_server_admin(admin_id):
    """Update a server administrator."""
    try:
        data = request.get_json()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Update user details
        update_fields = []
        update_values = []
        
        if 'name' in data:
            update_fields.append('name = %s')
            update_values.append(data['name'])
        
        if 'email' in data:
            update_fields.append('email = %s')
            update_values.append(data['email'])
            
        if 'status' in data:
            update_fields.append('status = %s')
            update_values.append(data['status'])
            
        if 'location' in data:
            update_fields.append('location = %s')
            update_values.append(data['location'])
            
        if 'avatar' in data:
            update_fields.append('avatar = %s')
            update_values.append(data['avatar'])
        
        if update_fields:
            update_fields.append('updated_at = %s')
            update_values.append(datetime.now())
            update_values.append(admin_id)

            user_query = f"UPDATE users SET {', '.join(update_fields)} WHERE id = %s"
            cursor.execute(user_query, update_values)

        # Update role if provided
        if 'role_id' in data:
            # Remove existing primary role
            cursor.execute("DELETE FROM user_roles WHERE user_id = %s AND is_primary = 1", (admin_id,))

            # Add new primary role
            if data['role_id']:
                role_query = """
                    INSERT INTO user_roles (user_id, role_id, assigned_by, is_primary, created_at)
                    VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(role_query, (admin_id, data['role_id'], g.user['id'], 1, datetime.now()))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Server administrator updated successfully'
        })

    except Exception as e:
        logging.error(f"Error updating server administrator: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to update server administrator'
        }), 500

@servers_bp.route('/admins/<int:admin_id>', methods=['DELETE'])
@require_auth
def delete_server_admin(admin_id):
    """Delete a server administrator."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if user exists and is not the current user
        if admin_id == g.user['id']:
            return jsonify({
                'success': False,
                'message': 'Cannot delete yourself'
            }), 400

        # Soft delete - update status to inactive
        cursor.execute(
            "UPDATE users SET status = 'inactive', updated_at = %s WHERE id = %s",
            (datetime.now(), admin_id)
        )

        if cursor.rowcount == 0:
            return jsonify({
                'success': False,
                'message': 'Server administrator not found'
            }), 404

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'Server administrator deleted successfully'
        })

    except Exception as e:
        logging.error(f"Error deleting server administrator: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to delete server administrator'
        }), 500

# Register routes with Flask app
def init_app(app):
    """Initialize the Servers API with the Flask app"""
    app.register_blueprint(servers_bp)
    logging.info("Servers API initialized with production database connection")

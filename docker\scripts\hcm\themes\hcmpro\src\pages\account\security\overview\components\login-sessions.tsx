/**
 * H‑CareCloud Project – Security Login Sessions Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { useState, useEffect } from 'react';
import { Link } from 'react-router';
import { hcmApi } from '@/services/hcm-api';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { EllipsisVertical, Monitor, Smartphone, TabletSmartphone } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';

interface ILoginSessionsItem {
  id: string;
  device_type: string;
  device_name: string;
  browser: string;
  ip_address: string;
  location: string;
  last_activity: string;
  is_current: boolean;
  created_at: string;
}
type ILoginSessionsItems = Array<ILoginSessionsItem>;

const LoginSessions = () => {
  const [sessions, setSessions] = useState<ILoginSessionsItems>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      const data = await hcmApi.getUserSessions();
      setSessions(data);
    } catch (error) {
      toast.error('Failed to load login sessions');
      logger.error('Failed to load user login sessions', {
        component: 'LoginSessions',
        action: 'loadSessions'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const terminateSession = async (sessionId: string) => {
    try {
      await hcmApi.terminateSession(sessionId);
      setSessions(sessions.filter(s => s.id !== sessionId));
      toast.success('Session terminated');
    } catch (error) {
      toast.error('Failed to terminate session');
      logger.error('Failed to terminate user session', {
        component: 'LoginSessions',
        action: 'terminateSession',
        data: { sessionId }
      }, error as Error);
    }
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType.toLowerCase()) {
      case 'mobile':
        return <Smartphone className="size-4 text-muted-foreground" />;
      case 'tablet':
        return <TabletSmartphone className="size-4 text-muted-foreground" />;
      default:
        return <Monitor className="size-4 text-muted-foreground" />;
    }
  };

  const formatLastActivity = (lastActivity: string) => {
    const date = new Date(lastActivity);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 5) return 'Active now';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffHours < 24) return `${diffHours} hours ago`;
    if (diffDays < 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const renderItem = (session: ILoginSessionsItem, index: number) => {
    if (loading) {
      return (
        <TableRow key={index}>
          <TableCell>
            <div className="flex items-center grow gap-2.5">
              <Skeleton className="size-9 rounded-full" />
              <div className="flex flex-col gap-0.5">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-20" />
              </div>
            </div>
          </TableCell>
          <TableCell className="text-end">
            <Skeleton className="h-4 w-16 ml-auto" />
          </TableCell>
          <TableCell className="text-end">
            <Skeleton className="h-4 w-20 ml-auto" />
          </TableCell>
          <TableCell className="pr-7.5!">
            <Skeleton className="h-6 w-6" />
          </TableCell>
        </TableRow>
      );
    }

    return (
      <TableRow key={session.id}>
        <TableCell>
          <div className="flex items-center grow gap-2.5">
            <div className="flex items-center justify-center size-9 bg-muted/30 rounded-full">
              {getDeviceIcon(session.device_type)}
            </div>
            <div className="flex flex-col gap-0.5">
              <span className="text-sm font-medium text-mono">
                {session.device_name}
                {session.is_current && (
                  <span className="ml-2 text-xs text-green-600 font-normal">
                    Current
                  </span>
                )}
              </span>
              <span className="text-xs font-normal text-secondary-foreground">
                {session.browser} • {session.ip_address}
              </span>
            </div>
          </div>
        </TableCell>
        <TableCell className="text-end text-secondary-foreground font-normal">
          {session.location || 'Unknown'}
        </TableCell>
        <TableCell className="text-end text-secondary-foreground font-normal">
          {formatLastActivity(session.last_activity)}
        </TableCell>
        <TableCell className="pr-7.5!">
          {!session.is_current && (
            <Button 
              variant="ghost" 
              mode="icon" 
              onClick={() => terminateSession(session.id)}
              title="Terminate session"
            >
              <EllipsisVertical />
            </Button>
          )}
        </TableCell>
      </TableRow>
    );
  };

  return (
    <Card className="min-w-full">
      <CardHeader>
        <CardTitle>Login Sessions</CardTitle>
      </CardHeader>
      <CardContent className="kt-scrollable-x-auto p-0">
        <div className="kt-scrollable-auto">
          <Table className="align-middle text-secondary-foreground text-sm">
            <TableHeader>
              <TableRow className="bg-accent/60">
                <TableHead className="text-start min-w-48 h-10">Device</TableHead>
                <TableHead className="text-end min-w-20 h-10">
                  Location
                </TableHead>
                <TableHead className="text-end min-w-20 h-10">
                  Last Activity
                </TableHead>
                <TableHead className="text-end w-[70px] h-10"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                [1, 2, 3, 4, 5].map((i) => renderItem({} as ILoginSessionsItem, i))
              ) : (
                sessions.map((session, index) => renderItem(session, index))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      {!loading && sessions.length > 5 && (
        <CardFooter className="justify-center">
          <Button mode="link" underlined="dashed" asChild>
            <Link to="/account/security/sessions">View all sessions</Link>
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

export { LoginSessions, type ILoginSessionsItem, type ILoginSessionsItems };

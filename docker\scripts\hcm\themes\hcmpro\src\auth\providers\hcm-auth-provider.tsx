/**
 * H‑CareCloud Project – HCM Authentication Provider
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { logger } from '@/lib/logger';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  phone?: string;
  department?: string;
  position?: string;
  status: 'active' | 'inactive' | 'suspended';
  last_login_at?: string;
  created_at: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (email: string, password: string, firstName: string, lastName: string) => Promise<boolean>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function HcmAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const apiUrl = import.meta.env.VITE_APP_API_URL;
  const tenantId = import.meta.env.VITE_TENANT_ID;

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('hcm_auth_token');
      if (!token || !apiUrl || !tenantId) {
        setLoading(false);
        return;
      }

      const response = await fetch(`${apiUrl}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData.user);
      } else {
        localStorage.removeItem('hcm_auth_token');
      }
    } catch (error) {
      logger.error('Authentication check failed', {
        component: 'HcmAuth',
        action: 'checkAuthStatus'
      }, error as Error);
      localStorage.removeItem('hcm_auth_token');
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setLoading(true);

      if (!apiUrl || !tenantId) {
        return false;
      }

      const response = await fetch(`${apiUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Tenant-ID': tenantId,
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        localStorage.setItem('hcm_auth_token', data.token);
        setUser(data.user);
        return true;
      } else {
        logger.warn('Login failed', {
          component: 'HcmAuth',
          action: 'login',
          data: { email, message: data.message }
        });
        return false;
      }
    } catch (error) {
      logger.error('Login error', {
        component: 'HcmAuth',
        action: 'login',
        data: { email }
      }, error as Error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const register = async (email: string, password: string, firstName: string, lastName: string): Promise<boolean> => {
    try {
      setLoading(true);

      if (!apiUrl || !tenantId) {
        return false;
      }

      const response = await fetch(`${apiUrl}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Tenant-ID': tenantId,
        },
        body: JSON.stringify({ email, password, firstName, lastName }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return true;
      } else {
        logger.warn('Registration failed', {
          component: 'HcmAuth',
          action: 'register',
          data: { email, firstName, lastName, message: data.message }
        });
        return false;
      }
    } catch (error) {
      logger.error('Registration error', {
        component: 'HcmAuth',
        action: 'register',
        data: { email, firstName, lastName }
      }, error as Error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      const token = localStorage.getItem('hcm_auth_token');
      if (token && apiUrl && tenantId) {
        await fetch(`${apiUrl}/api/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'X-Tenant-ID': tenantId,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (error) {
      logger.error('Logout error', {
        component: 'HcmAuth',
        action: 'logout'
      }, error as Error);
    } finally {
      localStorage.removeItem('hcm_auth_token');
      setUser(null);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an HcmAuthProvider');
  }
  return context;
}

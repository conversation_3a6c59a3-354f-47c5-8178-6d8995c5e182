#!/bin/bash
# Dev Mode Implementation for HCC Web Manager UI

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source helper functions if available
if [ -f "${SCRIPT_DIR}/ru-helper.sh" ]; then
    source "${SCRIPT_DIR}/ru-helper.sh"
    echo -e "${GREEN}Using centralized helper functions from ru-helper.sh${NC}"

    # Log script execution
    log_message "INFO" "Starting Web Manager UI in development mode"
    log_message "DEBUG" "Script directory: ${SCRIPT_DIR}"
    log_message "DEBUG" "Project root: ${PROJECT_ROOT}"
else
    # Colors for output
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    RED='\033[0;31m'
    BLUE='\033[0;34m'
    CYAN='\033[0;36m'
    MAGENTA='\033[0;35m'
    BOLD='\033[1m'
    NC='\033[0m' # No Color

    # Project root
    PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"

    echo -e "${YELLOW}Warning: ru-helper.sh not found. Using fallback definitions.${NC}"
fi

# Function to ensure Docker is running
ensure_docker_running() {
    # Use is_docker_running from ru-helper.sh if available
    if type is_docker_running &>/dev/null; then
        if is_docker_running; then
            log_message "SUCCESS" "Docker is running"
            echo -e "${GREEN}Docker is running.${NC}"
            return 0
        else
            log_message "ERROR" "Docker is not running"
            echo -e "${RED}Error: Docker daemon is not running.${NC}"
            return 1
        fi
    else
        # Fallback if is_docker_running is not available
        if ! command -v docker &> /dev/null; then
            echo -e "${RED}Error: Docker is not installed.${NC}"
            return 1
        fi

        if ! docker info &> /dev/null; then
            echo -e "${RED}Error: Docker daemon is not running.${NC}"
            return 1
        fi

        return 0
    fi
}

# Function to check environment
check_environment() {
    local current_env=""
    
    # Check if .env file exists and get APP_ENV
    if [ -f "${PROJECT_ROOT}/.env" ]; then
        local env_value=$(grep "^APP_ENV=" "${PROJECT_ROOT}/.env" | cut -d= -f2 | tr -d '"' | tr -d "'" | xargs)
        current_env="${env_value:-development}"
    else
        current_env="development"
    fi

    echo -e "${CYAN}Current Environment: ${BOLD}${current_env}${NC}"
    
    if [ "$current_env" != "development" ]; then
        echo -e "${YELLOW}⚠️  Warning: You are not in development environment!${NC}"
        echo -e "${YELLOW}   Current environment is: ${current_env}${NC}"
        echo -e "${YELLOW}   Dev mode is recommended for development environment only.${NC}"
        echo -e "${CYAN}   Do you want to continue anyway? (y/n)${NC}"
        read -r continue_anyway
        if [[ ! "$continue_anyway" == [Yy]* ]]; then
            echo -e "${YELLOW}Operation cancelled.${NC}"
            return 1
        fi
    fi
    
    return 0
}

# Function to start dev server
start_dev_server() {
    local nodejs_container="${NODEJS_CONTAINER_NAME:-nodejs}"
    local theme_name="${THEME_NAME:-hcc_themeData}"
    local dev_port="${MANAGER_PORT:-6600}"
    local nodejs_running=false
    local container_path="/var/www/docker/scripts/hcc_management/themes/${theme_name}"

    echo -e "${CYAN}Starting development server for theme: ${BOLD}${theme_name}${NC}"

    # Check if NodeJS container is running
    if type check_container_status &>/dev/null; then
        if check_container_status "$nodejs_container"; then
            nodejs_running=true
        fi
    else
        # Fallback to direct Docker commands
        if docker ps | grep -q "$nodejs_container\|${CONTAINER_PREFIX}$nodejs_container"; then
            nodejs_running=true
        fi
    fi

    if [ "$nodejs_running" = false ]; then
        echo -e "${RED}Error: Node.js container is not running.${NC}"
        echo -e "${YELLOW}Would you like to start the Node.js container? (y/n)${NC}"
        read -r start_nodejs
        if [[ "$start_nodejs" == [Yy]* ]]; then
            echo -e "${YELLOW}Starting Node.js container...${NC}"

            if type start_container &>/dev/null; then
                start_container "$nodejs_container"
            else
                docker-compose up -d "$nodejs_container" || docker-compose up -d "${CONTAINER_PREFIX}$nodejs_container"
            fi

            # Verify container started
            if docker ps | grep -q "$nodejs_container\|${CONTAINER_PREFIX}$nodejs_container"; then
                echo -e "${GREEN}✓ Node.js container started successfully.${NC}"
                nodejs_running=true
            else
                echo -e "${RED}✗ Failed to start Node.js container.${NC}"
                return 1
            fi
        else
            echo -e "${YELLOW}Operation cancelled.${NC}"
            return 1
        fi
    fi

    # Start development server with appropriate command
    echo -e "\n${BOLD}${GREEN}╔════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BOLD}${GREEN}║              🛠️  STARTING DEV MODE SERVER                      ║${NC}"
    echo -e "${BOLD}${GREEN}╚════════════════════════════════════════════════════════════════╝${NC}\n"

    echo -e "${CYAN}📋 TypeScript Development Options:${NC}"
    echo -e "   ${BOLD}1.${NC} Build Development Version (for Flask Manager)"
    echo -e "   ${BOLD}2.${NC} Start Hot Reload Server (for live development)"
    echo -e "   ${BOLD}3.${NC} TypeScript Check Only (no build)"
    echo -e "   ${BOLD}4.${NC} Lint & Format TypeScript Files"
    echo -e "   ${BOLD}5.${NC} Install/Update Dependencies"
    echo -e "   ${BOLD}Note:${NC} For production build, use ${BOLD}Option 43${NC} from main menu"
    echo -e ""
    read -p "Choose development mode (1-5): " dev_mode_choice

    case $dev_mode_choice in
        1)
            echo -e "${CYAN}📋 Development Build Information:${NC}"
            echo -e "   ${BOLD}Mode:${NC} Development build (unminified)"
            echo -e "   ${BOLD}Theme:${NC} ${theme_name}"
            echo -e "   ${BOLD}URL:${NC} http://localhost:${dev_port} (Flask Manager)"
            ;;
        2)
            echo -e "${CYAN}📋 Hot Reload Server Information:${NC}"
            echo -e "   ${BOLD}Mode:${NC} Hot reload development"
            echo -e "   ${BOLD}Theme:${NC} ${theme_name}"
            echo -e "   ${BOLD}URL:${NC} http://localhost:${dev_port} (Vite Dev Server with API Proxy)"
            ;;
        3)
            echo -e "${CYAN}📋 TypeScript Check Information:${NC}"
            echo -e "   ${BOLD}Mode:${NC} Type checking only (no build)"
            echo -e "   ${BOLD}Theme:${NC} ${theme_name}"
            echo -e "   ${BOLD}Action:${NC} Run tsc --noEmit to check types"
            ;;
        4)
            echo -e "${CYAN}📋 Lint & Format Information:${NC}"
            echo -e "   ${BOLD}Mode:${NC} Code quality check and formatting"
            echo -e "   ${BOLD}Theme:${NC} ${theme_name}"
            echo -e "   ${BOLD}Action:${NC} ESLint fix + Prettier format"
            ;;
        5)
            echo -e "${CYAN}📋 Dependencies Update Information:${NC}"
            echo -e "   ${BOLD}Mode:${NC} Install/Update npm packages"
            echo -e "   ${BOLD}Theme:${NC} ${theme_name}"
            echo -e "   ${BOLD}Action:${NC} npm install with TypeScript support"
            ;;
        *)
            echo -e "${RED}Invalid choice. Defaulting to build mode.${NC}"
            dev_mode_choice=1
            ;;
    esac
    echo -e "\n${YELLOW}📝 How to use:${NC}"
    if [ "$dev_mode_choice" = "1" ]; then
        echo -e "   • Access your application at http://localhost:${dev_port}"
        echo -e "   • Development build provides unminified code for debugging"
        echo -e "   • All APIs are connected and functional"
        echo -e "   • To rebuild after changes, run this option again"
        echo -e "   • Check browser console for detailed error messages"
    else
        echo -e "   • Access your application at http://localhost:${dev_port}"
        echo -e "   • Hot reload: Changes to CSS/JS will update instantly"
        echo -e "   • No build step required - files served directly by Vite"
        echo -e "   • All APIs are proxied and functional"
        echo -e "   • Press Ctrl+C to stop the hot reload server"
    fi

    echo -e "\n${CYAN}Starting development server...${NC}"

    # Check/install dependencies before starting dev server (DEVELOPMENT MODE ONLY)
    echo -e "${YELLOW}Checking and installing Node.js dependencies (DEVELOPMENT MODE)...${NC}"
    local npm_flags="--legacy-peer-deps"

    # Only add dev dependencies in development mode
    if [[ "${NODE_ENV:-development}" == "development" ]]; then
        npm_flags="--include=dev --legacy-peer-deps"
        echo -e "${CYAN}Development mode detected - installing dev dependencies${NC}"
    else
        echo -e "${CYAN}Production mode detected - installing production dependencies only${NC}"
    fi

    if docker-compose exec "$nodejs_container" sh -c "cd ${container_path} && NODE_ENV=development npm install ${npm_flags}" 2>/dev/null; then
        echo -e "${GREEN}✓ Dependencies installed via docker-compose.${NC}"
    elif docker exec "${CONTAINER_PREFIX}${nodejs_container}" sh -c "cd ${container_path} && NODE_ENV=development npm install ${npm_flags}" 2>/dev/null; then
        echo -e "${GREEN}✓ Dependencies installed via ${CONTAINER_PREFIX}${nodejs_container}.${NC}"
    elif docker exec "$nodejs_container" sh -c "cd ${container_path} && NODE_ENV=development npm install ${npm_flags}" 2>/dev/null; then
        echo -e "${GREEN}✓ Dependencies installed via ${nodejs_container}.${NC}"
    else
        echo -e "${RED}✗ Failed to install dependencies. Please check your Node.js container and theme path.${NC}"
        return 1
    fi

    # Execute based on selected development mode
    case $dev_mode_choice in
        1)
            echo -e "${YELLOW}Building development version for Flask manager...${NC}"
            local build_command="NODE_ENV=development npx vite build --mode development"
            ;;
        2)
            echo -e "${CYAN}Skipping build step for hot reload mode - Vite will serve files directly${NC}"
            ;;
        3)
            echo -e "${YELLOW}Running TypeScript type checking...${NC}"
            local build_command="npx tsc --noEmit"
            ;;
        4)
            echo -e "${YELLOW}Running ESLint and Prettier...${NC}"
            local build_command="npm run lint && npm run format"
            ;;
        5)
            echo -e "${YELLOW}Installing/updating dependencies with TypeScript support...${NC}"
            local build_command="npm install --legacy-peer-deps && npm install typescript @types/react @types/react-dom --save-dev"
            ;;
    esac

    # Execute the command for options that need it (skip option 2 hot reload setup)
    if [ "$dev_mode_choice" != "2" ]; then
        if docker-compose exec "$nodejs_container" sh -c "cd ${container_path} && ${build_command}" 2>/dev/null; then
            echo -e "${GREEN}✓ Command completed via docker-compose.${NC}"
        elif docker exec "${CONTAINER_PREFIX}${nodejs_container}" sh -c "cd ${container_path} && ${build_command}" 2>/dev/null; then
            echo -e "${GREEN}✓ Command completed via ${CONTAINER_PREFIX}${nodejs_container}.${NC}"
        elif docker exec "$nodejs_container" sh -c "cd ${container_path} && ${build_command}" 2>/dev/null; then
            echo -e "${GREEN}✓ Command completed via ${nodejs_container}.${NC}"
        else
            echo -e "${RED}✗ Failed to execute command.${NC}"
            return 1
        fi
    fi

    # Handle different development modes
    if [ "$dev_mode_choice" = "1" ]; then
        # Build mode - just build and exit
        echo -e "${GREEN}✓ Development build completed successfully!${NC}"
        echo -e "${CYAN}Your application is ready at: ${BOLD}http://localhost:${dev_port}${NC}"
        return 0
    else
        # Hot reload mode - start Vite dev server on same port as Flask
        # This allows seamless hot reload without port switching
        local dev_command="NODE_ENV=development npx vite --mode development --host 0.0.0.0 --port ${dev_port}"

        echo -e "${CYAN}Starting Vite hot reload server on port ${dev_port}...${NC}"
        echo -e "${YELLOW}Note: This will temporarily replace the Flask server${NC}"
        echo -e "${YELLOW}Note: Press Ctrl+C to stop the server${NC}"
        echo -e "${YELLOW}Server logs will appear below:${NC}\n"

        # Start Vite dev server interactively
        if docker-compose exec "$nodejs_container" sh -c "cd ${container_path} && ${dev_command}"; then
            echo -e "${GREEN}✓ Hot reload server stopped.${NC}"
        elif docker exec -it "${CONTAINER_PREFIX}${nodejs_container}" sh -c "cd ${container_path} && ${dev_command}"; then
            echo -e "${GREEN}✓ Hot reload server stopped.${NC}"
        elif docker exec -it "$nodejs_container" sh -c "cd ${container_path} && ${dev_command}"; then
            echo -e "${GREEN}✓ Hot reload server stopped.${NC}"
        else
            echo -e "${RED}✗ Failed to start hot reload server.${NC}"
            return 1
        fi
    fi


}

# Main function
main() {
    echo -e "\n${BOLD}${MAGENTA}╔════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BOLD}${MAGENTA}║              🛠️  WEB MANAGER UI - DEV MODE                      ║${NC}"
    echo -e "${BOLD}${MAGENTA}╚════════════════════════════════════════════════════════════════╝${NC}\n"

    # Check if Docker is running
    if ! ensure_docker_running; then
        echo -e "${YELLOW}Press Enter to continue...${NC}"
        read -r
        return 1
    fi

    # Check environment (warn if not development)
    if ! check_environment; then
        echo -e "${YELLOW}Press Enter to continue...${NC}"
        read -r
        return 1
    fi

    # Start the development server
    if start_dev_server; then
        if [ "$dev_mode_choice" = "1" ]; then
            echo -e "\n${GREEN}✅ Development build completed!${NC}"
            echo -e "${CYAN}🌐 Access your application: ${BOLD}http://localhost:${MANAGER_PORT:-6600}${NC}"
            echo -e "${GREEN}✓ Development build with unminified code for debugging${NC}"
            echo -e "${GREEN}✓ Professional error handling with no mock data${NC}"
            echo -e "${GREEN}✓ All APIs connected and functional${NC}"
            echo -e "${YELLOW}💡 To enable hot reload, run this option again and choose option 2${NC}"
        else
            echo -e "\n${GREEN}✅ Hot reload server stopped!${NC}"
            echo -e "${CYAN}Development session completed.${NC}"
        fi
    else
        echo -e "${RED}✗ Failed to start development environment.${NC}"
    fi

    echo -e "\n${YELLOW}Press Enter to continue...${NC}"
    read -r
}

# Run the main function
main "$@"

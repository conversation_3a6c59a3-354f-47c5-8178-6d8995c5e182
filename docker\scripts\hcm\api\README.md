# H-CareManager API Layer

## Overview
This directory contains the Flask API endpoints for H-CareManager, providing RESTful services for the React frontend. All APIs use Blueprint architecture for modular organization and connect to the production database via centralized configuration.

## Architecture

### Core Components
- **Flask Blueprints**: Each API module is a separate Blueprint for clean organization
- **Authentication**: Token-based authentication using `@token_required` decorator
- **Database**: Centralized connection via `hcc_database.get_db_connection()`
- **Configuration**: Environment variables managed through `hcc_config`
- **Logging**: Structured logging with debug levels for development/production

### API Structure
```
/api/
├── auth.py              # Authentication & user management
├── user.py              # User profile & settings
├── api_keys.py          # API token management
├── billing.py           # Subscription & payment management
├── security.py          # Security settings & audit logs
├── notifications.py     # Notification management
├── settings.py          # Application settings
├── integrations.py      # External service integrations
├── staffs.py           # Server administration (to be renamed)
├── activity.py         # User activity logging
└── routes.py           # Route registration
```

## Database Integration

### Connection Management
- **Centralized**: All APIs use `get_db_connection()` from auth.py
- **Production Database**: Connects to `hcarecloud_manager` database
- **Table Validation**: Each API validates required tables exist
- **Transaction Safety**: Proper commit/rollback handling

### Key Database Tables
- `users` - User accounts and profiles
- `api_tokens` - Authentication tokens
- `user_sessions` - Session management
- `billing_plans` - Subscription plans
- `user_subscriptions` - User billing
- `notifications` - System notifications
- `security_devices` - Trusted devices
- `audit_logs` - Security audit trail

## Security Standards

### Authentication
- **Token-based**: JWT-like tokens stored in `api_tokens` table
- **User Context**: `g.user` available in all protected endpoints
- **Session Tracking**: IP address and user agent logging
- **Token Expiration**: Configurable token lifetime

### HIPAA Compliance
- **Audit Logging**: All data access logged to `audit_logs`
- **Data Encryption**: Sensitive data properly hashed
- **Access Control**: Role-based permissions
- **Session Security**: Secure session management

## API Endpoints

### Authentication (`/api/auth/`)
- `POST /login` - User authentication
- `GET /verify` - Token verification
- `POST /logout` - Session termination
- `GET /status` - System status

### User Management (`/api/user/`)
- `GET /profile` - Get user profile
- `PUT /profile` - Update user profile
- `POST /profile/image` - Upload profile image
- `GET /security/overview` - Security dashboard
- `POST /security/change-password` - Password change

### API Keys (`/api/user/api-keys/`)
- `GET /` - List API tokens
- `POST /` - Create new token
- `PUT /<token>` - Update token
- `DELETE /<token>` - Delete token
- `POST /regenerate/<token>` - Regenerate token

### Billing (`/api/user/billing/`)
- `GET /plans` - Available plans
- `GET /subscription` - Current subscription
- `GET /history` - Billing history
- `POST /subscribe` - Subscribe to plan
- `POST /cancel` - Cancel subscription

## Development Guidelines

### Adding New Endpoints
1. Create or modify appropriate Blueprint file
2. Use `@token_required` decorator for protected endpoints
3. Validate input data and sanitize
4. Use centralized database connection
5. Implement proper error handling
6. Add audit logging for sensitive operations
7. Update this README.md and CHANGELOG.md

### Error Handling
```python
try:
    # API logic
    return jsonify({'success': True, 'data': result})
except Exception as e:
    logger.error(f"Error in endpoint: {e}")
    return jsonify({'success': False, 'error': 'Operation failed'}), 500
```

### Response Format
```python
# Success Response
{
    "success": true,
    "data": {...},
    "message": "Optional success message"
}

# Error Response
{
    "success": false,
    "error": "Error description",
    "code": "ERROR_CODE"
}
```

## Configuration

### Environment Variables
- `MYSQL_HOST` - Database host
- `MYSQL_USER` - Database user
- `MYSQL_PASSWORD` - Database password
- `MYSQL_DATABASE` - Main database name
- `FLASK_ENV` - Environment (development/production)
- `VITE_DEBUG` - Debug logging flag

### Initialization
Each API module has an `init_app(app)` function that:
1. Registers the Blueprint with Flask app
2. Validates required database tables
3. Sets up logging configuration
4. Returns success/failure status

## Testing

### Local Development
```bash
# Start Flask development server
python hcc_manager.py

# Test API endpoints
curl -H "Authorization: Bearer <token>" http://localhost:5000/api/user/profile
```

### Database Testing
- Ensure `init-db.sh` has been run
- Verify all required tables exist
- Test with sample data

## Troubleshooting

### Common Issues
1. **Database Connection Failed**: Check environment variables and database status
2. **Token Invalid**: Verify token exists in `api_tokens` table and is active
3. **Missing Tables**: Run `init-db.sh` to create required database schema
4. **Import Errors**: Ensure `hcc_pymodules` is in Python path

### Debug Logging
Set `FLASK_ENV=development` to enable detailed debug logging for API requests and database operations.

## Related Documentation
- `/hcc_pymodules/README.md` - Shared modules documentation
- `/mysql/README.md` - Database schema documentation
- `../themes/hcmpro/src/services/README.md` - Frontend API integration

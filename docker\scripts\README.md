# H-CareCloud Docker Scripts and Utilities

## Overview
This directory contains utility scripts and automation tools for H-CareCloud Hospital Management System Docker infrastructure. These scripts handle container management, deployment automation, maintenance tasks, and system administration for the entire healthcare platform.

## Architecture

### Scripts Structure
```
/docker/scripts/
├── bash/                   # Bash utility scripts
│   ├── backup.sh          # Database and file backup automation
│   ├── deploy.sh          # Deployment and update scripts
│   ├── health-check.sh    # System health monitoring
│   └── maintenance.sh     # Routine maintenance tasks
├── cleanup-backup/         # Backup cleanup and archival
│   ├── cleanup.sh         # Automated backup cleanup
│   └── archive.sh         # Long-term backup archival
└── hcm/                   # H-CareManager core application
    ├── api/               # Flask API endpoints
    ├── hcc_pymodules/     # Shared Python modules
    ├── utils/             # Utility functions
    └── themes/hcmpro/     # React TypeScript frontend
```

### Script Categories
- **Deployment Scripts**: Automated deployment and updates
- **Backup Scripts**: Database and file system backup automation
- **Maintenance Scripts**: Routine system maintenance and optimization
- **Monitoring Scripts**: Health checks and performance monitoring
- **Utility Scripts**: General-purpose administration tools

## Deployment Scripts

### deploy.sh
```bash
#!/bin/bash
# H-CareCloud Deployment Script
set -e

ENVIRONMENT=${1:-production}
VERSION=${2:-latest}

echo "Deploying H-CareCloud $VERSION to $ENVIRONMENT environment..."

# Pre-deployment checks
./scripts/bash/health-check.sh --pre-deploy

# Backup current state
./scripts/bash/backup.sh --pre-deploy

# Pull latest images
docker-compose pull

# Update containers
docker-compose up -d --remove-orphans

# Run database migrations
docker exec hcarecloud-app php artisan migrate --force

# Clear caches
docker exec hcarecloud-app php artisan cache:clear
docker exec hcarecloud-app php artisan config:cache
docker exec hcarecloud-app php artisan route:cache

# Build frontend assets
docker exec manager yarn build --mode $ENVIRONMENT

# Post-deployment verification
./scripts/bash/health-check.sh --post-deploy

echo "Deployment completed successfully!"
```

### update.sh
```bash
#!/bin/bash
# H-CareCloud Update Script
set -e

echo "Updating H-CareCloud system..."

# Check for updates
git fetch origin
LOCAL=$(git rev-parse HEAD)
REMOTE=$(git rev-parse origin/main)

if [ $LOCAL = $REMOTE ]; then
    echo "System is up to date."
    exit 0
fi

# Backup before update
./scripts/bash/backup.sh --pre-update

# Pull latest changes
git pull origin main

# Update dependencies
docker exec hcarecloud-app composer install --no-dev --optimize-autoloader
docker exec manager yarn install --frozen-lockfile

# Run migrations
docker exec hcarecloud-app php artisan migrate --force

# Restart services
docker-compose restart

echo "Update completed successfully!"
```

## Backup Scripts

### backup.sh
```bash
#!/bin/bash
# H-CareCloud Backup Script
set -e

BACKUP_TYPE=${1:-full}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/docker/backups"

echo "Starting $BACKUP_TYPE backup at $TIMESTAMP..."

# Database backup
if [ "$BACKUP_TYPE" = "full" ] || [ "$BACKUP_TYPE" = "database" ]; then
    echo "Backing up databases..."
    
    # HMS database
    docker exec mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD} \
        ${MYSQL_DATABASE} > $BACKUP_DIR/database/hms_${TIMESTAMP}.sql
    
    # H-CareManager database
    docker exec mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD} \
        hcarecloud_manager > $BACKUP_DIR/database/manager_${TIMESTAMP}.sql
    
    # Compress backups
    gzip $BACKUP_DIR/database/*_${TIMESTAMP}.sql
fi

# File system backup
if [ "$BACKUP_TYPE" = "full" ] || [ "$BACKUP_TYPE" = "files" ]; then
    echo "Backing up files..."
    
    # Application files
    tar -czf $BACKUP_DIR/files/app_${TIMESTAMP}.tar.gz \
        --exclude='*/node_modules/*' \
        --exclude='*/vendor/*' \
        --exclude='*/storage/logs/*' \
        /var/www/html
    
    # Configuration files
    tar -czf $BACKUP_DIR/files/config_${TIMESTAMP}.tar.gz \
        /docker/*/
fi

# Verify backups
echo "Verifying backup integrity..."
find $BACKUP_DIR -name "*_${TIMESTAMP}*" -exec md5sum {} \; > $BACKUP_DIR/metadata/checksums_${TIMESTAMP}.md5

echo "Backup completed successfully!"
```

## Maintenance Scripts

### maintenance.sh
```bash
#!/bin/bash
# H-CareCloud Maintenance Script
set -e

TASK=${1:-all}

echo "Running maintenance task: $TASK"

# Database maintenance
if [ "$TASK" = "all" ] || [ "$TASK" = "database" ]; then
    echo "Performing database maintenance..."
    
    # Optimize tables
    docker exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "
        OPTIMIZE TABLE hcarecloud_manager.users;
        OPTIMIZE TABLE hcarecloud_manager.user_sessions;
        OPTIMIZE TABLE hcarecloud_manager.audit_logs;
        OPTIMIZE TABLE ${MYSQL_DATABASE}.patients;
        OPTIMIZE TABLE ${MYSQL_DATABASE}.appointments;
    "
    
    # Update statistics
    docker exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "
        ANALYZE TABLE hcarecloud_manager.users;
        ANALYZE TABLE ${MYSQL_DATABASE}.patients;
    "
fi

# Log cleanup
if [ "$TASK" = "all" ] || [ "$TASK" = "logs" ]; then
    echo "Cleaning up logs..."
    
    # Rotate logs
    find /docker/logs -name "*.log" -mtime +30 -delete
    find /docker/logs -name "*.log.gz" -mtime +90 -delete
    
    # Clear Laravel logs
    docker exec hcarecloud-app php artisan log:clear
fi

# Cache optimization
if [ "$TASK" = "all" ] || [ "$TASK" = "cache" ]; then
    echo "Optimizing caches..."
    
    # Clear and rebuild Laravel caches
    docker exec hcarecloud-app php artisan cache:clear
    docker exec hcarecloud-app php artisan config:cache
    docker exec hcarecloud-app php artisan route:cache
    docker exec hcarecloud-app php artisan view:cache
    
    # Redis maintenance
    docker exec redis redis-cli FLUSHDB
fi

echo "Maintenance completed successfully!"
```

## Monitoring Scripts

### health-check.sh
```bash
#!/bin/bash
# H-CareCloud Health Check Script
set -e

MODE=${1:-standard}
FAILED_CHECKS=0

echo "Running health checks in $MODE mode..."

# Container health
echo "Checking container status..."
CONTAINERS=("mysql" "redis" "nginx" "fpm" "hcarecloud-app" "manager")

for container in "${CONTAINERS[@]}"; do
    if ! docker ps | grep -q $container; then
        echo "❌ Container $container is not running"
        ((FAILED_CHECKS++))
    else
        echo "✅ Container $container is healthy"
    fi
done

# Database connectivity
echo "Checking database connectivity..."
if docker exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    ((FAILED_CHECKS++))
fi

# Redis connectivity
echo "Checking Redis connectivity..."
if docker exec redis redis-cli ping | grep -q "PONG"; then
    echo "✅ Redis connection successful"
else
    echo "❌ Redis connection failed"
    ((FAILED_CHECKS++))
fi

# Web server response
echo "Checking web server response..."
if curl -f http://localhost > /dev/null 2>&1; then
    echo "✅ Web server responding"
else
    echo "❌ Web server not responding"
    ((FAILED_CHECKS++))
fi

# API endpoints
if [ "$MODE" = "full" ]; then
    echo "Checking API endpoints..."
    
    # HMS API
    if curl -f http://localhost/api/health > /dev/null 2>&1; then
        echo "✅ HMS API responding"
    else
        echo "❌ HMS API not responding"
        ((FAILED_CHECKS++))
    fi
    
    # H-CareManager API
    if curl -f http://localhost:5000/api/health > /dev/null 2>&1; then
        echo "✅ H-CareManager API responding"
    else
        echo "❌ H-CareManager API not responding"
        ((FAILED_CHECKS++))
    fi
fi

# Summary
if [ $FAILED_CHECKS -eq 0 ]; then
    echo "🎉 All health checks passed!"
    exit 0
else
    echo "⚠️  $FAILED_CHECKS health check(s) failed!"
    exit 1
fi
```

## Utility Scripts

### Environment Management
```bash
# setup-env.sh
#!/bin/bash
# Environment setup script

ENVIRONMENT=${1:-development}

echo "Setting up $ENVIRONMENT environment..."

# Copy environment file
cp .env.$ENVIRONMENT .env

# Generate application key
docker exec hcarecloud-app php artisan key:generate

# Run migrations
docker exec hcarecloud-app php artisan migrate

# Seed database (development only)
if [ "$ENVIRONMENT" = "development" ]; then
    docker exec hcarecloud-app php artisan db:seed
fi

echo "Environment setup completed!"
```

### Performance Monitoring
```bash
# monitor.sh
#!/bin/bash
# Performance monitoring script

echo "System Performance Report - $(date)"
echo "=================================="

# Container resource usage
echo "Container Resource Usage:"
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

# Database performance
echo -e "\nDatabase Performance:"
docker exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "
    SHOW STATUS LIKE 'Threads_connected';
    SHOW STATUS LIKE 'Queries';
    SHOW STATUS LIKE 'Slow_queries';
"

# Redis performance
echo -e "\nRedis Performance:"
docker exec redis redis-cli info stats | grep -E "(total_commands_processed|total_connections_received|used_memory_human)"

# Disk usage
echo -e "\nDisk Usage:"
df -h | grep -E "(docker|var)"
```

## Automation and Scheduling

### Cron Jobs
```bash
# /etc/crontab entries for automated tasks

# Daily backup at 2:00 AM
0 2 * * * root /docker/scripts/bash/backup.sh full

# Weekly maintenance on Sunday at 3:00 AM
0 3 * * 0 root /docker/scripts/bash/maintenance.sh all

# Hourly health checks
0 * * * * root /docker/scripts/bash/health-check.sh standard

# Daily log cleanup at 1:00 AM
0 1 * * * root /docker/scripts/cleanup-backup/cleanup.sh
```

## Related Documentation
- [Backup System Documentation](../backups/README.md)
- [Monitoring and Alerting Documentation](../../docs/monitoring.md)
- [Deployment Documentation](../../docs/deployment.md)
- [Main Project Documentation](hcm/themes/hcmpro/docs.md)

/**
 * H‑CareCloud Project – Authentication Callback Page
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { useEffect, useState } from 'react';
import { useAuth } from '@/auth/providers/hcm-auth-provider';
import { useNavigate, useSearchParams } from 'react-router';
import { logger } from '@/lib/logger';

export function CallbackPage() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    const errorParam = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    if (errorParam) {
      setError(errorDescription || 'Authentication failed');
      setTimeout(() => {
        navigate(
          `/auth/signin?error=${errorParam}&error_description=${encodeURIComponent(errorDescription || 'Authentication failed')}`,
        );
      }, 1500);
      return;
    }

    const handleCallback = async () => {
      try {
        logger.debug('Processing HCM auth callback', {
          component: 'CallbackPage',
          action: 'handleCallback'
        });

        const token = searchParams.get('token');
        const code = searchParams.get('code');

        if (!token && !code) {
          logger.error('No authentication credentials found in callback', {
            component: 'CallbackPage',
            action: 'handleCallback'
          });
          throw new Error('Authentication credentials not found');
        }

        if (user) {
          const nextPath = searchParams.get('next') || '/';
          logger.debug('User already authenticated, redirecting', {
            component: 'CallbackPage',
            action: 'handleCallback',
            data: { nextPath }
          });
          navigate(nextPath);
          return;
        }

        logger.debug('Redirecting to signin page', {
          component: 'CallbackPage',
          action: 'handleCallback'
        });
        navigate('/auth/signin');
      } catch (err) {
        logger.error('Error processing auth callback', {
          component: 'CallbackPage',
          action: 'handleCallback'
        }, err as Error);
        setError('An unexpected error occurred during authentication');

        setTimeout(() => {
          navigate(
            '/auth/signin?error=auth_callback_error&error_description=Failed to complete authentication',
          );
        }, 1500);
      }
    };

    handleCallback();
  }, [navigate, searchParams, user]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
      {error ? (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-destructive">
            Authentication Error
          </h2>
          <p className="text-muted-foreground">{error}</p>
          <p className="text-sm">Redirecting to sign-in page...</p>
        </div>
      ) : (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Processing Authentication</h2>
          <p className="text-muted-foreground">Please wait...</p>
        </div>
      )}
    </div>
  );
}
/**
 * H‑CareCloud Project – Billing Hook
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { useState, useEffect } from 'react';
import { hcmApi, type BillingPlan, type UserSubscription, type BillingHistory } from '@/services/hcm-api';
import { logger } from '@/lib/logger';

export function useBilling() {
  const [plans, setPlans] = useState<BillingPlan[]>([]);
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [history, setHistory] = useState<BillingHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchBillingData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [plansData, subscriptionData, historyData] = await Promise.all([
        hcmApi.getBillingPlans(),
        hcmApi.getUserSubscription(),
        hcmApi.getBillingHistory(),
      ]);
      
      setPlans(plansData);
      setSubscription(subscriptionData);
      setHistory(historyData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch billing data');
      logger.error('Failed to fetch billing data', {
        component: 'useBilling',
        action: 'fetchBillingData'
      }, err as Error);
    } finally {
      setLoading(false);
    }
  };

  const subscribeToPlan = async (planId: number) => {
    try {
      setError(null);
      await hcmApi.subscribeToPlan(planId);
      // Refetch subscription data after subscribing
      const newSubscription = await hcmApi.getUserSubscription();
      setSubscription(newSubscription);
      return newSubscription;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to subscribe to plan');
      logger.error('Failed to subscribe to plan', {
        component: 'useBilling',
        action: 'subscribeToPlan',
        data: { planId }
      }, err as Error);
      throw err;
    }
  };

  const cancelSubscription = async () => {
    try {
      setError(null);
      await hcmApi.cancelSubscription();
      if (subscription) {
        setSubscription({ ...subscription, status: 'cancelled', cancelled_at: new Date().toISOString() });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cancel subscription');
      logger.error('Failed to cancel subscription', {
        component: 'useBilling',
        action: 'cancelSubscription'
      }, err as Error);
      throw err;
    }
  };

  useEffect(() => {
    fetchBillingData();
  }, []);

  return {
    plans,
    subscription,
    history,
    loading,
    error,
    subscribeToPlan,
    cancelSubscription,
    refetch: fetchBillingData,
  };
}

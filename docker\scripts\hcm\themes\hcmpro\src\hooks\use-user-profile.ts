/**
 * H‑CareCloud Project – User Profile Hook
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { useState, useEffect } from 'react';
import { hcmApi, type UserProfile } from '@/services/hcm-api';
import { logger } from '@/lib/logger';

export function useUserProfile() {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await hcmApi.getUserProfile();
      setProfile(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch profile');
      logger.error('Failed to fetch user profile', {
        component: 'useUserProfile',
        action: 'fetchProfile'
      }, err as Error);
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    try {
      setError(null);
      const updatedProfile = await hcmApi.updateUserProfile(updates);
      setProfile(updatedProfile);
      return updatedProfile;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update profile');
      logger.error('Failed to update user profile', {
        component: 'useUserProfile',
        action: 'updateProfile',
        data: { updates }
      }, err as Error);
      throw err;
    }
  };

  const uploadAvatar = async (file: File) => {
    try {
      setError(null);
      const result = await hcmApi.uploadAvatar(file);
      if (profile) {
        setProfile({ ...profile, avatar: result.avatar_url });
      }
      return result.avatar_url;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload avatar');
      logger.error('Failed to upload avatar', {
        component: 'useUserProfile',
        action: 'uploadAvatar',
        data: { file: file.name, size: file.size }
      }, err as Error);
      throw err;
    }
  };

  useEffect(() => {
    fetchProfile();
  }, []);

  return {
    profile,
    loading,
    error,
    updateProfile,
    uploadAvatar,
    refetch: fetchProfile,
  };
}

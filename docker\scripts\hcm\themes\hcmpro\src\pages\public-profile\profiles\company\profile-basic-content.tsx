import { useEffect, useState } from 'react';
import {
  RiFacebookCircleLine,
  RiTwitterXLine,
  RiYoutubeLine,
} from '@remixicon/react';
import { Dribbble, Mail } from 'lucide-react';
import { Connections, Contributions, Projects, Tags } from '../default';
import {
  CompanyProfile,
  Highlights,
  INetworkItems,
  IStatisticsItems,
  Locations,
  Network,
  OpenJobs,
  Statistics,
} from './components';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { Skeleton } from '@/components/ui/skeleton';

export function ProfileCompanyContent() {
  const [loading, setLoading] = useState(true);
  const [statistics, setStatistics] = useState<IStatisticsItems>([]);
  const [networkData, setNetworkData] = useState<INetworkItems>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadServerData();
  }, []);

  const loadServerData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load real server statistics from API
      const [systemInfo] = await Promise.all([
        hcmApi.getSystemInfo(),
        hcmApi.getIntegrations()
      ]);

      // Convert real data to statistics format
      const stats: IStatisticsItems = [
        { number: systemInfo.containers?.active || '0', label: 'Active Containers' },
        { number: systemInfo.docker?.images || '0', label: 'Docker Images' },
        { number: `$${systemInfo.costs?.monthly || '0'}`, label: 'Monthly Costs' },
        { number: `${systemInfo.uptime?.percentage || '99.9'}%`, label: 'System Uptime' },
      ];

      // Convert integrations to network format
      const network: INetworkItems = [
        { icon: Dribbble, link: 'https://hostwek.com' },
        { icon: Mail, link: '<EMAIL>' },
        { icon: RiFacebookCircleLine, link: 'hostwek' },
        { icon: RiTwitterXLine, link: 'hostwek-updates' },
        { icon: RiYoutubeLine, link: 'hostwek-docs' },
      ];

      setStatistics(stats);
      setNetworkData(network);

      logger.info('Server data loaded successfully', {
        component: 'ProfileCompanyContent',
        action: 'loadServerData',
        data: { statisticsCount: stats.length, networkCount: network.length }
      });

    } catch (error) {
      setError('Failed to load server data');
      logger.error('Failed to load server data', {
        component: 'ProfileCompanyContent',
        action: 'loadServerData'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
        <div className="col-span-1 lg:col-span-3">
          <Skeleton className="h-32 w-full" />
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-5 lg:gap-7.5">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-48 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
        <div className="col-span-1 lg:col-span-2">
          <div className="flex flex-col gap-5 lg:gap-7.5">
            <Skeleton className="h-96 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
        <div className="col-span-1 lg:col-span-3">
          <div className="text-center py-12">
            <p className="text-red-500 mb-4">{error}</p>
            <button
              onClick={loadServerData}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
      <div className="col-span-1 lg:col-span-3">
        <Statistics items={statistics} />
      </div>
      <div className="col-span-1">
        <div className="flex flex-col gap-5 lg:gap-7.5">
          <Highlights />
          <OpenJobs />
          <Network title="Infrastructure" data={networkData} />
          <Tags title="Technologies" />
        </div>
      </div>
      <div className="col-span-1 lg:col-span-2">
        <div className="flex flex-col gap-5 lg:gap-7.5">
          <CompanyProfile />
          <Locations />
          <Projects />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-5 lg:gap-7.5">
            <Connections title="Server Admins" />
            <Contributions title="Resource Usage" />
          </div>
        </div>
      </div>
    </div>
  );
}

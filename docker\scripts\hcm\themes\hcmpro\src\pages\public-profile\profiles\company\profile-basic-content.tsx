import {
  RiFacebookCircleLine,
  RiTwitterXLine,
  RiYoutubeLine,
} from '@remixicon/react';
import { Dribbble, Mail } from 'lucide-react';
import { Connections, Contributions, Projects, Tags } from '../default';
import {
  CompanyProfile,
  Highlights,
  INetworkItems,
  IStatisticsItems,
  Locations,
  Network,
  OpenJobs,
  Statistics,
} from './components';

export function ProfileCompanyContent() {
  const items: IStatisticsItems = [
    { number: '24', label: 'Active Servers' },
    { number: '1.2K', label: 'Containers' },
    { number: '$12.5K', label: 'Monthly Costs' },
    { number: '99.9%', label: 'Uptime' },
  ];

  const data: INetworkItems = [
    { icon: Dribbble, link: 'https://hcarecloud.com' },
    { icon: Mail, link: '<EMAIL>' },
    { icon: RiFacebookCircleLine, link: 'hcarecloud' },
    { icon: RiTwitterXLine, link: 'hcarecloud-updates' },
    { icon: RiYoutubeLine, link: 'hcarecloud-docs' },
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
      <div className="col-span-1 lg:col-span-3">
        <Statistics items={items} />
      </div>
      <div className="col-span-1">
        <div className="flex flex-col gap-5 lg:gap-7.5">
          <Highlights />
          <OpenJobs />
          <Network title="Infrastructure" data={data} />
          <Tags title="Technologies" />
        </div>
      </div>
      <div className="col-span-1 lg:col-span-2">
        <div className="flex flex-col gap-5 lg:gap-7.5">
          <CompanyProfile />
          <Locations />
          <Projects />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-5 lg:gap-7.5">
            <Connections title="Server Admins" />
            <Contributions title="Resource Usage" />
          </div>
        </div>
      </div>
    </div>
  );
}

# H-CareManager Dashboard Pages

## Overview
This directory contains dashboard pages for H-CareManager Project, providing comprehensive server management, analytics, and monitoring interfaces. The dashboards are designed for healthcare server administrators with real-time data, professional visualizations, and actionable insights.

## Architecture

### Dashboard Structure
```
/src/pages/dashboards/
├── DashboardPage.tsx       # Main dashboard overview
├── ServerDashboard.tsx     # Server management dashboard
├── AnalyticsDashboard.tsx  # Analytics and reporting dashboard
└── components/             # Dashboard-specific components
    ├── MetricsCard.tsx     # Metric display cards
    ├── ServerStatus.tsx    # Server status widgets
    ├── ChartWidget.tsx     # Chart and graph components
    └── AlertPanel.tsx      # Alert and notification panel
```

### Dashboard Categories
- **Main Dashboard**: Overview of system health and key metrics
- **Server Dashboard**: Detailed server management and monitoring
- **Analytics Dashboard**: Performance analytics and reporting
- **Monitoring Dashboard**: Real-time system monitoring

## Dashboard Pages

### DashboardPage.tsx
**Purpose**: Main dashboard with system overview and key metrics.

**Key Features**:
- System health overview
- Server status summary
- Recent activity feed
- Quick action buttons
- Real-time metrics
- Alert notifications

**Implementation**:
```typescript
export const DashboardPage: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [servers, setServers] = useState<ServerSummary[]>([]);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);

  const { success, error } = useToast();
  const { checkPermission } = usePermissions();

  useEffect(() => {
    loadDashboardData();
    
    // Set up real-time updates
    const interval = setInterval(loadDashboardData, 30000); // 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      const [metricsData, serversData, alertsData] = await Promise.all([
        hcmApi.getSystemMetrics(),
        hcmApi.getServerSummary(),
        hcmApi.getActiveAlerts()
      ]);
      
      setMetrics(metricsData);
      setServers(serversData);
      setAlerts(alertsData);
      
    } catch (err) {
      error('Failed to load dashboard data');
      logger.error('Dashboard data loading failed', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading && !metrics) {
    return (
      <div className="row g-5 g-xl-8">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="col-xl-4">
            <Skeleton className="w-100 h-64" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="content d-flex flex-column flex-column-fluid">
      <div className="container-xxl">
        {/* Page Header */}
        <div className="page-title d-flex flex-column justify-content-center flex-wrap me-3">
          <h1 className="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
            Dashboard
          </h1>
          <ul className="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
            <li className="breadcrumb-item text-muted">
              <span className="text-muted">H-CareManager</span>
            </li>
            <li className="breadcrumb-item">
              <span className="bullet bg-gray-400 w-5px h-2px"></span>
            </li>
            <li className="breadcrumb-item text-muted">Dashboard</li>
          </ul>
        </div>

        {/* Alert Panel */}
        {alerts.length > 0 && (
          <div className="row mb-5">
            <div className="col-12">
              <AlertPanel alerts={alerts} onDismiss={loadDashboardData} />
            </div>
          </div>
        )}

        {/* Metrics Cards */}
        <div className="row g-5 g-xl-8 mb-5">
          <div className="col-xl-3">
            <MetricsCard
              title="Total Servers"
              value={metrics?.totalServers || 0}
              change={metrics?.serverChange || 0}
              icon="server"
              color="primary"
            />
          </div>
          <div className="col-xl-3">
            <MetricsCard
              title="Active Servers"
              value={metrics?.activeServers || 0}
              change={metrics?.activeChange || 0}
              icon="check-circle"
              color="success"
            />
          </div>
          <div className="col-xl-3">
            <MetricsCard
              title="CPU Usage"
              value={`${metrics?.avgCpuUsage || 0}%`}
              change={metrics?.cpuChange || 0}
              icon="microchip"
              color="warning"
            />
          </div>
          <div className="col-xl-3">
            <MetricsCard
              title="Memory Usage"
              value={`${metrics?.avgMemoryUsage || 0}%`}
              change={metrics?.memoryChange || 0}
              icon="memory"
              color="info"
            />
          </div>
        </div>

        {/* Server Status Grid */}
        <div className="row g-5 g-xl-8 mb-5">
          <div className="col-xl-8">
            <div className="card card-xl-stretch mb-xl-8">
              <div className="card-header border-0 pt-5">
                <h3 className="card-title align-items-start flex-column">
                  <span className="card-label fw-bold fs-3 mb-1">Server Status</span>
                  <span className="text-muted fw-semibold fs-7">Real-time server monitoring</span>
                </h3>
                <div className="card-toolbar">
                  {checkPermission('server.manage') && (
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => navigate('/dashboard/servers')}
                    >
                      Manage Servers
                    </Button>
                  )}
                </div>
              </div>
              <div className="card-body py-3">
                <ServerStatusGrid servers={servers} onRefresh={loadDashboardData} />
              </div>
            </div>
          </div>
          
          <div className="col-xl-4">
            <div className="card card-xl-stretch mb-xl-8">
              <div className="card-header border-0 pt-5">
                <h3 className="card-title align-items-start flex-column">
                  <span className="card-label fw-bold fs-3 mb-1">Quick Actions</span>
                  <span className="text-muted fw-semibold fs-7">Common tasks</span>
                </h3>
              </div>
              <div className="card-body py-3">
                <QuickActionsPanel onAction={loadDashboardData} />
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="row g-5 g-xl-8">
          <div className="col-12">
            <div className="card card-xl-stretch">
              <div className="card-header border-0 pt-5">
                <h3 className="card-title align-items-start flex-column">
                  <span className="card-label fw-bold fs-3 mb-1">Recent Activity</span>
                  <span className="text-muted fw-semibold fs-7">Latest system events</span>
                </h3>
                <div className="card-toolbar">
                  <Link
                    to="/account/activity"
                    className="btn btn-sm btn-light-primary"
                  >
                    View All
                  </Link>
                </div>
              </div>
              <div className="card-body py-3">
                <RecentActivityFeed limit={10} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
```

### ServerDashboard.tsx
**Purpose**: Detailed server management and monitoring dashboard.

**Key Features**:
- Server list with real-time status
- Performance metrics and charts
- Server actions and controls
- Resource usage monitoring
- Alert management

### AnalyticsDashboard.tsx
**Purpose**: Analytics and reporting dashboard with data visualization.

**Key Features**:
- Performance analytics
- Usage statistics
- Trend analysis
- Custom reports
- Data export capabilities

## Dashboard Components

### MetricsCard.tsx
**Purpose**: Reusable metric display card with trend indicators.

**Implementation**:
```typescript
interface MetricsCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: string;
  color: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  loading?: boolean;
}

export const MetricsCard: React.FC<MetricsCardProps> = ({
  title,
  value,
  change,
  icon,
  color,
  loading = false
}) => {
  if (loading) {
    return (
      <div className="card">
        <div className="card-body">
          <Skeleton className="w-100 h-20" />
        </div>
      </div>
    );
  }

  const changeColor = change && change > 0 ? 'success' : change && change < 0 ? 'danger' : 'muted';
  const changeIcon = change && change > 0 ? 'arrow-up' : change && change < 0 ? 'arrow-down' : '';

  return (
    <div className="card">
      <div className="card-body">
        <div className="d-flex align-items-center">
          <div className="symbol symbol-50px me-5">
            <span className={`symbol-label bg-light-${color}`}>
              <i className={`fas fa-${icon} fs-2x text-${color}`}></i>
            </span>
          </div>
          <div className="d-flex flex-column">
            <span className="fw-bold fs-6 text-gray-800">{title}</span>
            <div className="d-flex align-items-center">
              <span className="fw-bolder fs-2 text-gray-900 me-2">{value}</span>
              {change !== undefined && (
                <span className={`fw-bold fs-7 text-${changeColor}`}>
                  {changeIcon && <i className={`fas fa-${changeIcon} me-1`}></i>}
                  {Math.abs(change)}%
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
```

### ServerStatusGrid.tsx
**Purpose**: Grid display of server status with actions.

**Implementation**:
```typescript
interface ServerStatusGridProps {
  servers: ServerSummary[];
  onRefresh: () => void;
}

export const ServerStatusGrid: React.FC<ServerStatusGridProps> = ({
  servers,
  onRefresh
}) => {
  const { checkPermission } = usePermissions();
  const { success, error } = useToast();

  const handleServerAction = async (action: string, serverId: string) => {
    try {
      switch (action) {
        case 'restart':
          await hcmApi.restartServer(serverId);
          success('Server restart initiated');
          break;
        case 'stop':
          await hcmApi.stopServer(serverId);
          success('Server stopped');
          break;
        case 'start':
          await hcmApi.startServer(serverId);
          success('Server started');
          break;
      }
      
      onRefresh();
    } catch (err) {
      error(`Failed to ${action} server`);
    }
  };

  return (
    <div className="table-responsive">
      <table className="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
        <thead>
          <tr className="fw-bold text-muted">
            <th className="min-w-150px">Server</th>
            <th className="min-w-100px">Status</th>
            <th className="min-w-100px">CPU</th>
            <th className="min-w-100px">Memory</th>
            <th className="min-w-100px">Uptime</th>
            <th className="min-w-100px text-end">Actions</th>
          </tr>
        </thead>
        <tbody>
          {servers.map((server) => (
            <tr key={server.id}>
              <td>
                <div className="d-flex align-items-center">
                  <div className="symbol symbol-45px me-5">
                    <span className="symbol-label bg-light-primary text-primary fw-bold">
                      {server.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div className="d-flex justify-content-start flex-column">
                    <span className="text-dark fw-bold text-hover-primary fs-6">
                      {server.name}
                    </span>
                    <span className="text-muted fw-semibold text-muted d-block fs-7">
                      {server.ipAddress}
                    </span>
                  </div>
                </div>
              </td>
              <td>
                <span className={`badge badge-light-${getStatusColor(server.status)} fw-bold`}>
                  {server.status.toUpperCase()}
                </span>
              </td>
              <td>
                <div className="d-flex flex-column w-100 me-2">
                  <div className="d-flex flex-stack mb-2">
                    <span className="text-muted me-2 fs-7 fw-bold">{server.cpuUsage}%</span>
                  </div>
                  <div className="progress h-6px w-100">
                    <div
                      className={`progress-bar bg-${getCpuColor(server.cpuUsage)}`}
                      style={{ width: `${server.cpuUsage}%` }}
                    ></div>
                  </div>
                </div>
              </td>
              <td>
                <div className="d-flex flex-column w-100 me-2">
                  <div className="d-flex flex-stack mb-2">
                    <span className="text-muted me-2 fs-7 fw-bold">{server.memoryUsage}%</span>
                  </div>
                  <div className="progress h-6px w-100">
                    <div
                      className={`progress-bar bg-${getMemoryColor(server.memoryUsage)}`}
                      style={{ width: `${server.memoryUsage}%` }}
                    ></div>
                  </div>
                </div>
              </td>
              <td>
                <span className="text-dark fw-bold d-block fs-7">
                  {formatUptime(server.uptime)}
                </span>
              </td>
              <td className="text-end">
                {checkPermission('server.manage') && (
                  <div className="d-flex justify-content-end flex-shrink-0">
                    <Button
                      variant="light"
                      size="sm"
                      className="me-2"
                      onClick={() => handleServerAction('restart', server.id)}
                    >
                      Restart
                    </Button>
                    <Button
                      variant={server.status === 'online' ? 'light-danger' : 'light-success'}
                      size="sm"
                      onClick={() => handleServerAction(
                        server.status === 'online' ? 'stop' : 'start',
                        server.id
                      )}
                    >
                      {server.status === 'online' ? 'Stop' : 'Start'}
                    </Button>
                  </div>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
```

## Real-time Features

### WebSocket Integration
- Real-time server status updates
- Live performance metrics
- Instant alert notifications
- Auto-refresh capabilities

### Performance Monitoring
- CPU and memory usage tracking
- Network performance metrics
- Disk usage monitoring
- Response time analysis

## Related Documentation
- [Components Documentation](../../components/README.md)
- [Services Documentation](../../services/README.md)
- [Hooks Documentation](../../hooks/README.md)
- [H-CareManager Project Documentation](../../../docs.md)

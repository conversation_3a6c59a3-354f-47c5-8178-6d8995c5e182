# H-CareManager Custom Hooks

## Overview
This directory contains custom React hooks for H-CareManager Project, providing reusable logic for API integration, state management, and common functionality. The hooks follow React best practices and provide type-safe interfaces for component integration.

## Architecture

### Hooks Structure
```
/src/hooks/
├── api/                    # API-related hooks
│   ├── useServerAdmins.ts  # Server administrators management
│   ├── useApiKeys.ts       # API key management
│   ├── useBilling.ts       # Billing and subscription hooks
│   ├── useNotifications.ts # Notification management
│   └── useAuditLog.ts      # Audit logging hooks
├── auth/                   # Authentication hooks
│   ├── useAuth.ts          # Main authentication hook
│   ├── usePermissions.ts   # Permission checking
│   └── useSession.ts       # Session management
├── ui/                     # UI-related hooks
│   ├── useModal.ts         # Modal state management
│   ├── useToast.ts         # Toast notifications
│   ├── useLoading.ts       # Loading state management
│   └── useTheme.ts         # Theme management
├── data/                   # Data management hooks
│   ├── useLocalStorage.ts  # Local storage management
│   ├── useDebounce.ts      # Input debouncing
│   ├── usePagination.ts    # Pagination logic
│   └── useSearch.ts        # Search functionality
└── utils/                  # Utility hooks
    ├── useAsync.ts         # Async operation handling
    ├── useInterval.ts      # Interval management
    ├── useEventListener.ts # Event listener management
    └── useClipboard.ts     # Clipboard operations
```

### Hook Categories
- **API Hooks**: Backend integration and data fetching
- **Authentication Hooks**: User authentication and permissions
- **UI Hooks**: User interface state and interactions
- **Data Hooks**: Data management and persistence
- **Utility Hooks**: Common functionality and helpers

## API Hooks

### useServerAdmins.ts
**Purpose**: Server administrators management with CRUD operations.

**Implementation**:
```typescript
interface UseServerAdminsReturn {
  admins: ServerAdmin[];
  loading: boolean;
  error: string | null;
  
  // Actions
  loadAdmins: () => Promise<void>;
  createAdmin: (data: CreateAdminRequest) => Promise<void>;
  updateAdmin: (id: string, data: UpdateAdminRequest) => Promise<void>;
  deleteAdmin: (id: string) => Promise<void>;
  
  // Filters and search
  searchAdmins: (query: string) => void;
  filterByRole: (role: string) => void;
  filterByStatus: (status: string) => void;
}

export const useServerAdmins = (): UseServerAdminsReturn => {
  const [admins, setAdmins] = useState<ServerAdmin[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { success, error: showError } = useToast();

  const loadAdmins = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await hcmApi.getServerAdmins();
      setAdmins(data);
    } catch (err) {
      const errorMessage = 'Failed to load server administrators';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const createAdmin = useCallback(async (data: CreateAdminRequest) => {
    setLoading(true);
    
    try {
      await hcmApi.createServerAdmin(data);
      success('Server administrator created successfully');
      await loadAdmins(); // Refresh list
    } catch (err) {
      showError('Failed to create server administrator');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadAdmins, success, showError]);

  const updateAdmin = useCallback(async (id: string, data: UpdateAdminRequest) => {
    setLoading(true);
    
    try {
      await hcmApi.updateServerAdmin(id, data);
      success('Server administrator updated successfully');
      await loadAdmins(); // Refresh list
    } catch (err) {
      showError('Failed to update server administrator');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadAdmins, success, showError]);

  const deleteAdmin = useCallback(async (id: string) => {
    setLoading(true);
    
    try {
      await hcmApi.deleteServerAdmin(id);
      success('Server administrator deleted successfully');
      await loadAdmins(); // Refresh list
    } catch (err) {
      showError('Failed to delete server administrator');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadAdmins, success, showError]);

  // Load admins on mount
  useEffect(() => {
    loadAdmins();
  }, [loadAdmins]);

  return {
    admins,
    loading,
    error,
    loadAdmins,
    createAdmin,
    updateAdmin,
    deleteAdmin,
    searchAdmins,
    filterByRole,
    filterByStatus
  };
};

// Usage example
const ServerAdminsPage = () => {
  const {
    admins,
    loading,
    createAdmin,
    updateAdmin,
    deleteAdmin
  } = useServerAdmins();

  if (loading) {
    return <Skeleton className="w-full h-64" />;
  }

  return (
    <div>
      <ServerAdminsList
        admins={admins}
        onUpdate={updateAdmin}
        onDelete={deleteAdmin}
      />
      <CreateAdminForm onSubmit={createAdmin} />
    </div>
  );
};
```

### useApiKeys.ts
**Purpose**: API key management with abilities and usage tracking.

**Implementation**:
```typescript
interface UseApiKeysReturn {
  apiKeys: ApiKey[];
  loading: boolean;
  error: string | null;
  
  createApiKey: (data: CreateApiKeyRequest) => Promise<ApiKey>;
  deleteApiKey: (token: string) => Promise<void>;
  updateApiKey: (token: string, data: UpdateApiKeyRequest) => Promise<void>;
  getUsageStats: (token: string) => Promise<ApiKeyUsage>;
}

export const useApiKeys = (): UseApiKeysReturn => {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { success, error: showError } = useToast();

  const loadApiKeys = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await hcmApi.getApiKeys();
      setApiKeys(data);
    } catch (err) {
      const errorMessage = 'Failed to load API keys';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const createApiKey = useCallback(async (data: CreateApiKeyRequest): Promise<ApiKey> => {
    setLoading(true);
    
    try {
      const newApiKey = await hcmApi.createApiKey(data);
      success('API key created successfully');
      await loadApiKeys(); // Refresh list
      return newApiKey;
    } catch (err) {
      showError('Failed to create API key');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadApiKeys, success, showError]);

  const deleteApiKey = useCallback(async (token: string) => {
    setLoading(true);
    
    try {
      await hcmApi.deleteApiKey(token);
      success('API key deleted successfully');
      await loadApiKeys(); // Refresh list
    } catch (err) {
      showError('Failed to delete API key');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadApiKeys, success, showError]);

  // Load API keys on mount
  useEffect(() => {
    loadApiKeys();
  }, [loadApiKeys]);

  return {
    apiKeys,
    loading,
    error,
    createApiKey,
    deleteApiKey,
    updateApiKey,
    getUsageStats
  };
};
```

## Authentication Hooks

### useAuth.ts
**Purpose**: Main authentication hook with user state and actions.

**Implementation**:
```typescript
interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  permissions: string[];
  
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
}

export const useAuth = (): UseAuthReturn => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within HcmAuthProvider');
  }
  
  return context;
};
```

### usePermissions.ts
**Purpose**: Permission checking and role-based access control.

**Implementation**:
```typescript
interface UsePermissionsReturn {
  permissions: string[];
  roles: string[];
  
  checkPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  canAccess: (resource: string, action: string) => boolean;
}

export const usePermissions = (): UsePermissionsReturn => {
  const { user, permissions } = useAuth();
  
  const checkPermission = useCallback((permission: string): boolean => {
    return permissions.includes(permission);
  }, [permissions]);

  const hasRole = useCallback((role: string): boolean => {
    return user?.roles?.includes(role) || false;
  }, [user?.roles]);

  const hasAnyPermission = useCallback((perms: string[]): boolean => {
    return perms.some(permission => checkPermission(permission));
  }, [checkPermission]);

  const hasAllPermissions = useCallback((perms: string[]): boolean => {
    return perms.every(permission => checkPermission(permission));
  }, [checkPermission]);

  const canAccess = useCallback((resource: string, action: string): boolean => {
    const permission = `${resource}.${action}`;
    return checkPermission(permission);
  }, [checkPermission]);

  return {
    permissions,
    roles: user?.roles || [],
    checkPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    canAccess
  };
};

// Usage example
const ServerManagementPanel = () => {
  const { canAccess, hasRole } = usePermissions();

  if (!hasRole('admin') && !canAccess('server', 'manage')) {
    return <div>Access denied</div>;
  }

  return (
    <div>
      {canAccess('server', 'create') && (
        <CreateServerButton />
      )}
      {canAccess('server', 'delete') && (
        <DeleteServerButton />
      )}
    </div>
  );
};
```

## UI Hooks

### useModal.ts
**Purpose**: Modal state management with multiple modal support.

**Implementation**:
```typescript
interface UseModalReturn {
  isOpen: boolean;
  data: any;
  
  openModal: (data?: any) => void;
  closeModal: () => void;
  toggleModal: () => void;
}

export const useModal = (initialState = false): UseModalReturn => {
  const [isOpen, setIsOpen] = useState(initialState);
  const [data, setData] = useState<any>(null);

  const openModal = useCallback((modalData?: any) => {
    setData(modalData);
    setIsOpen(true);
  }, []);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setData(null);
  }, []);

  const toggleModal = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        closeModal();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeModal]);

  return {
    isOpen,
    data,
    openModal,
    closeModal,
    toggleModal
  };
};

// Usage example
const ServerListPage = () => {
  const { isOpen, data, openModal, closeModal } = useModal();

  const handleEditServer = (server: Server) => {
    openModal(server);
  };

  return (
    <div>
      <ServerList onEdit={handleEditServer} />
      
      <Modal isOpen={isOpen} onClose={closeModal} title="Edit Server">
        {data && <EditServerForm server={data} onSave={closeModal} />}
      </Modal>
    </div>
  );
};
```

### useToast.ts
**Purpose**: Toast notification management with queue support.

**Implementation**:
```typescript
interface UseToastReturn {
  success: (message: string, options?: ToastOptions) => string;
  error: (message: string, options?: ToastOptions) => string;
  warning: (message: string, options?: ToastOptions) => string;
  info: (message: string, options?: ToastOptions) => string;
  dismiss: (id: string) => void;
  dismissAll: () => void;
}

export const useToast = (): UseToastReturn => {
  const context = useContext(NotificationContext);
  
  if (!context) {
    throw new Error('useToast must be used within NotificationProvider');
  }
  
  return {
    success: context.success,
    error: context.error,
    warning: context.warning,
    info: context.info,
    dismiss: context.removeNotification,
    dismissAll: context.clearAll
  };
};
```

## Data Hooks

### useLocalStorage.ts
**Purpose**: Local storage management with type safety and encryption.

**Implementation**:
```typescript
interface UseLocalStorageReturn<T> {
  value: T;
  setValue: (value: T | ((prev: T) => T)) => void;
  removeValue: () => void;
}

export const useLocalStorage = <T>(
  key: string,
  initialValue: T,
  options: {
    encrypt?: boolean;
    serialize?: (value: T) => string;
    deserialize?: (value: string) => T;
  } = {}
): UseLocalStorageReturn<T> => {
  const {
    encrypt = false,
    serialize = JSON.stringify,
    deserialize = JSON.parse
  } = options;

  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = encrypt 
        ? storageService.getSecure(key)
        : storageService.get(key);
      
      return item ? deserialize(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      
      const serializedValue = serialize(valueToStore);
      
      if (encrypt) {
        storageService.setSecure(key, serializedValue);
      } else {
        storageService.set(key, serializedValue);
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue, serialize, encrypt]);

  const removeValue = useCallback(() => {
    try {
      storageService.remove(key);
      setStoredValue(initialValue);
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  return {
    value: storedValue,
    setValue,
    removeValue
  };
};

// Usage example
const UserPreferences = () => {
  const [preferences, setPreferences] = useLocalStorage('user-preferences', {
    theme: 'light',
    language: 'en',
    notifications: true
  }, { encrypt: true });

  const updateTheme = (theme: string) => {
    setPreferences(prev => ({ ...prev, theme }));
  };

  return (
    <div>
      <ThemeSelector value={preferences.theme} onChange={updateTheme} />
    </div>
  );
};
```

### useDebounce.ts
**Purpose**: Input debouncing for search and API calls.

**Implementation**:
```typescript
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Usage example
const SearchComponent = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [results, setResults] = useState([]);

  useEffect(() => {
    if (debouncedSearchTerm) {
      searchServers(debouncedSearchTerm).then(setResults);
    } else {
      setResults([]);
    }
  }, [debouncedSearchTerm]);

  return (
    <div>
      <input
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder="Search servers..."
      />
      <SearchResults results={results} />
    </div>
  );
};
```

## Utility Hooks

### useAsync.ts
**Purpose**: Async operation handling with loading and error states.

**Implementation**:
```typescript
interface UseAsyncReturn<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  
  execute: (...args: any[]) => Promise<T>;
  reset: () => void;
}

export const useAsync = <T>(
  asyncFunction: (...args: any[]) => Promise<T>,
  immediate = false
): UseAsyncReturn<T> => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(immediate);
  const [error, setError] = useState<Error | null>(null);

  const execute = useCallback(async (...args: any[]): Promise<T> => {
    setLoading(true);
    setError(null);

    try {
      const result = await asyncFunction(...args);
      setData(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [asyncFunction]);

  const reset = useCallback(() => {
    setData(null);
    setLoading(false);
    setError(null);
  }, []);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);

  return {
    data,
    loading,
    error,
    execute,
    reset
  };
};

// Usage example
const ServerDetailsPage = ({ serverId }: { serverId: string }) => {
  const {
    data: server,
    loading,
    error,
    execute: loadServer
  } = useAsync(() => hcmApi.getServer(serverId), true);

  if (loading) return <Skeleton className="w-full h-64" />;
  if (error) return <div>Error: {error.message}</div>;
  if (!server) return <div>Server not found</div>;

  return <ServerDetails server={server} onRefresh={loadServer} />;
};
```

## Hook Testing

### Testing Custom Hooks
```typescript
// Hook testing example
import { renderHook, act } from '@testing-library/react';
import { useServerAdmins } from './useServerAdmins';

describe('useServerAdmins', () => {
  it('loads server admins on mount', async () => {
    const { result } = renderHook(() => useServerAdmins());

    expect(result.current.loading).toBe(true);

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.admins).toHaveLength(3);
  });

  it('creates new admin successfully', async () => {
    const { result } = renderHook(() => useServerAdmins());

    await act(async () => {
      await result.current.createAdmin({
        name: 'New Admin',
        email: '<EMAIL>',
        role: 'admin'
      });
    });

    expect(result.current.admins).toHaveLength(4);
  });
});
```

## Related Documentation
- [React Hooks Documentation](https://reactjs.org/docs/hooks-intro.html)
- [Services Documentation](../services/README.md)
- [Components Documentation](../components/README.md)
- [H-CareManager Project Documentation](../../docs.md)

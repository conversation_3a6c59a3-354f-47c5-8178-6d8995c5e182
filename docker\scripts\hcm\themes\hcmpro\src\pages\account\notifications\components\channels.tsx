/**
 * H‑CareCloud Project – Notification Channels Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { ReactNode, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { hcmApi } from '@/services/hcm-api';
import { CardNotification } from '@/partials/cards';
import { LucideIcon } from 'lucide-react';
import { Link } from 'react-router';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';

interface IChannelsItem {
  id: string;
  icon: LucideIcon;
  title: string;
  description: string;
  enabled: boolean;
  connected: boolean;
  endpoint?: string;
  button?: boolean;
  actions?: ReactNode;
}
type IChannelsItems = Array<IChannelsItem>;

const Channels = () => {
  const [channels, setChannels] = useState<IChannelsItems>([]);
  const [teamAlertsEnabled, setTeamAlertsEnabled] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadNotificationChannels();
  }, []);

  const loadNotificationChannels = async () => {
    try {
      const channelsData = await hcmApi.getNotificationChannels();
      setChannels(channelsData.channels);
      setTeamAlertsEnabled(channelsData.team_alerts_enabled);
    } catch (error) {
      toast.error('Failed to load notification channels');
      if (import.meta.env.VITE_DEBUG) {
        console.error('[NotificationChannels] Load failed:', error);
      }
    } finally {
      setLoading(false);
    }
  };

  const toggleChannel = async (id: string, enabled: boolean) => {
    try {
      await hcmApi.updateNotificationChannel(id, { enabled });
      setChannels(prev => 
        prev.map(channel => 
          channel.id === id ? { ...channel, enabled } : channel
        )
      );
      toast.success(`${enabled ? 'Enabled' : 'Disabled'} notifications for channel`);
    } catch (error) {
      toast.error('Failed to update channel');
      if (import.meta.env.VITE_DEBUG) {
        console.error('[NotificationChannels] Toggle failed:', error);
      }
    }
  };

  const toggleTeamAlerts = async (enabled: boolean) => {
    try {
      await hcmApi.updateNotificationSettings({ team_alerts_enabled: enabled });
      setTeamAlertsEnabled(enabled);
      toast.success(`Team-wide alerts ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      toast.error('Failed to update team alerts');
      if (import.meta.env.VITE_DEBUG) {
        console.error('[NotificationChannels] Team alerts toggle failed:', error);
      }
    }
  };

  const getChannelActions = (channel: IChannelsItem) => {
    if (channel.title === 'Slack' && !channel.connected) {
      return (
        <Button variant="outline">
          <Link to="/account/integrations">Connect Slack</Link>
        </Button>
      );
    }

    return (
      <Switch 
        id={`channel-${channel.id}`}
        size="sm" 
        checked={channel.enabled}
        onCheckedChange={(checked) => toggleChannel(channel.id, checked)}
      />
    );
  };

  const renderItem = (item: IChannelsItem, index: number) => {
    return (
      <CardNotification
        icon={item.icon}
        title={item.title}
        description={item.description || item.endpoint || 'Not configured'}
        button={item.button}
        actions={getChannelActions(item)}
        key={item.id}
      />
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="gap-2">
          <CardTitle>Notification Channels</CardTitle>
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-6 w-10" />
          </div>
        </CardHeader>
        <div id="notifications_cards" className="space-y-4 p-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Skeleton className="h-5 w-5" />
                <div>
                  <Skeleton className="h-4 w-16 mb-1" />
                  <Skeleton className="h-3 w-32" />
                </div>
              </div>
              <Skeleton className="h-6 w-10" />
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="gap-2">
        <CardTitle>Notification Channels</CardTitle>
        <div className="flex items-center gap-2">
          <Label htmlFor="team-alerts" className="text-sm">
            Team-Wide Alerts
          </Label>
          <Switch 
            id="team-alerts" 
            size="sm" 
            checked={teamAlertsEnabled}
            onCheckedChange={toggleTeamAlerts}
          />
        </div>
      </CardHeader>
      <div id="notifications_cards">
        {channels.length > 0 ? (
          channels.map((item, index) => {
            return renderItem(item, index);
          })
        ) : (
          <div className="text-center py-8 px-4 text-secondary-foreground">
            No notification channels configured. Set up your preferred notification methods.
          </div>
        )}
      </div>
    </Card>
  );
};

export { Channels, type IChannelsItem, type IChannelsItems };

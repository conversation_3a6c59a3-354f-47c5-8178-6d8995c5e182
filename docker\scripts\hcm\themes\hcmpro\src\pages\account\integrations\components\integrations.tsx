/**
 * H‑CareCloud Project – Server Integrations Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { ReactNode, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { CardIntegration } from '@/partials/cards';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';

interface IIntegrationsItem {
  id: string;
  logo: string;
  path: string;
  name: string;
  description: string;
  enabled: boolean;
  category: 'infrastructure' | 'monitoring' | 'backup' | 'communication' | 'documentation';
  actions?: ReactNode;
}
type IIntegrationsItems = Array<IIntegrationsItem>;

const Integrations = () => {
  const [integrations, setIntegrations] = useState<IIntegrationsItems>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadIntegrations();
  }, []);

  const loadIntegrations = async () => {
    try {
      // Get user settings to check for integration preferences
      const settings = await hcmApi.getSettings();
      
      const serverIntegrations: IIntegrationsItems = [
        {
          id: 'docker',
          logo: '/media/brand-logos/docker.svg',
          path: '/integrations/docker',
          name: 'Docker',
          description: 'Container management and orchestration for H-CareCloud services.',
          enabled: settings.integrations?.docker ?? true,
          category: 'infrastructure',
        },
        {
          id: 'mysql',
          logo: '/media/brand-logos/mysql.svg', 
          path: '/integrations/mysql',
          name: 'MySQL',
          description: 'Database management and monitoring for healthcare data storage.',
          enabled: settings.integrations?.mysql ?? true,
          category: 'infrastructure',
        },
        {
          id: 'nginx',
          logo: '/media/brand-logos/nginx.svg',
          path: '/integrations/nginx',
          name: 'Nginx',
          description: 'Web server configuration and load balancing management.',
          enabled: settings.integrations?.nginx ?? true,
          category: 'infrastructure',
        },
        {
          id: 'prometheus',
          logo: '/media/brand-logos/prometheus.svg',
          path: '/integrations/prometheus',
          name: 'Prometheus',
          description: 'Server monitoring, metrics collection, and alerting system.',
          enabled: settings.integrations?.prometheus ?? false,
          category: 'monitoring',
        },
        {
          id: 'grafana',
          logo: '/media/brand-logos/grafana.svg',
          path: '/integrations/grafana',
          name: 'Grafana',
          description: 'Data visualization and dashboard creation for system metrics.',
          enabled: settings.integrations?.grafana ?? false,
          category: 'monitoring',
        },
        {
          id: 'slack',
          logo: '/media/brand-logos/slack.svg',
          path: '/integrations/slack',
          name: 'Slack',
          description: 'Real-time notifications and alerts for system administrators.',
          enabled: settings.integrations?.slack ?? false,
          category: 'communication',
        },
        {
          id: 'backup-service',
          logo: '/media/brand-logos/cloud-backup.svg',
          path: '/integrations/backup',
          name: 'Backup Service',
          description: 'Automated backup and disaster recovery for healthcare data.',
          enabled: settings.integrations?.backup ?? true,
          category: 'backup',
        },
        {
          id: 'documentation',
          logo: '/media/brand-logos/gitbook.svg',
          path: '/integrations/docs',
          name: 'Documentation',
          description: 'API documentation and system guides for H-CareManager.',
          enabled: settings.integrations?.documentation ?? true,
          category: 'documentation',
        },
      ];
      
      setIntegrations(serverIntegrations);
    } catch (error) {
      toast.error('Failed to load integrations');
      logger.error('Failed to load server integrations', {
        component: 'Integrations',
        action: 'loadIntegrations'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const toggleIntegration = async (id: string, enabled: boolean) => {
    try {
      // Update integration preferences using existing settings API
      const currentSettings = await hcmApi.getSettings();
      const updatedIntegrations = {
        ...currentSettings.integrations,
        [id]: enabled,
      };
      
      await hcmApi.updateSettings({ 
        integrations: updatedIntegrations 
      });
      
      setIntegrations(prev => 
        prev.map(item => 
          item.id === id ? { ...item, enabled } : item
        )
      );
      toast.success(`Integration ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      toast.error('Failed to update integration');
      logger.error('Failed to toggle integration', {
        component: 'Integrations',
        action: 'toggleIntegration',
        data: { id, enabled }
      }, error as Error);
    }
  };

  const renderItem = (item: IIntegrationsItem) => {
    const switchActions = (
      <Switch 
        id={`integration-${item.id}`}
        size="sm" 
        checked={item.enabled}
        onCheckedChange={(checked) => toggleIntegration(item.id, checked)}
      />
    );

    return (
      <CardIntegration
        logo={item.logo}
        path={item.path}
        name={item.name}
        description={item.description}
        actions={switchActions}
        key={item.id}
      />
    );
  };

  if (loading) {
    return (
      <div id="integrations_cards">
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-5 lg:gap-7.5">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="border border-border rounded-xl p-4">
              <div className="flex items-center gap-3 mb-3">
                <Skeleton className="h-10 w-10" />
                <div className="flex-1">
                  <Skeleton className="h-4 w-24 mb-1" />
                  <Skeleton className="h-3 w-32" />
                </div>
                <Skeleton className="h-6 w-10" />
              </div>
              <Skeleton className="h-12 w-full" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div id="integrations_cards">
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-5 lg:gap-7.5">
        {integrations.length > 0 ? (
          integrations.map((item) => {
            return renderItem(item);
          })
        ) : (
          <div className="col-span-full text-center py-8 text-secondary-foreground">
            No integrations available. Contact support to set up server integrations.
          </div>
        )}
      </div>
    </div>
  );
};

export { Integrations, type IIntegrationsItem, type IIntegrationsItems };

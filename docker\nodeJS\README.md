# H-CareCloud Node.js Container Configuration

## Overview
This directory contains the Node.js container configuration for H-CareCloud development and build processes. The Node.js container handles frontend asset compilation, development server, package management, and build optimization for both the main HMS frontend assets and the H-CareManager React dashboard.

## Architecture

### Container Structure
```
/docker/nodeJS/
├── docker-entrypoint.sh    # Container initialization script
└── [Runtime directories]
    ├── node_modules/       # Node.js dependencies
    ├── dist/              # Build output
    ├── cache/             # Build cache
    └── logs/              # Build and runtime logs
```

### Container Purpose
- **Asset Compilation**: Compile SCSS, TypeScript, and modern JavaScript
- **Development Server**: Hot-reload development environment
- **Package Management**: npm/yarn dependency management
- **Build Optimization**: Production asset optimization and bundling

## Container Configuration

### docker-entrypoint.sh
```bash
#!/bin/bash
set -e

# Environment setup
export NODE_ENV=${NODE_ENV:-development}
export NODE_OPTIONS="--max-old-space-size=4096"

# Create necessary directories
mkdir -p /app/node_modules
mkdir -p /app/dist
mkdir -p /app/cache
mkdir -p /app/logs

# Set proper permissions
chown -R node:node /app

# Install dependencies if package.json changed
if [ -f "/app/package.json" ]; then
    echo "Installing Node.js dependencies..."
    
    # Use yarn if yarn.lock exists, otherwise use npm
    if [ -f "/app/yarn.lock" ]; then
        yarn install --frozen-lockfile --cache-folder /app/cache
    else
        npm ci --cache /app/cache
    fi
fi

# Development mode
if [ "$NODE_ENV" = "development" ]; then
    echo "Starting Node.js in development mode..."
    exec yarn dev
fi

# Production build
if [ "$NODE_ENV" = "production" ]; then
    echo "Building assets for production..."
    exec yarn build
fi

# Watch mode for asset compilation
if [ "$1" = "watch" ]; then
    echo "Starting asset watch mode..."
    exec yarn watch
fi

# Default command
exec "$@"
```

## Development Configuration

### Package Management
```json
{
  "name": "hcarecloud-assets",
  "version": "1.0.8",
  "scripts": {
    "dev": "vite --host 0.0.0.0",
    "build": "vite build",
    "watch": "vite build --watch",
    "preview": "vite preview",
    "type-check": "tsc --noEmit",
    "lint": "eslint src --ext .ts,.tsx",
    "lint:fix": "eslint src --ext .ts,.tsx --fix"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^5.0.0"
  },
  "devDependencies": {
    "@vitejs/plugin-react": "^4.0.0",
    "vite": "^4.4.0",
    "eslint": "^8.45.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0"
  }
}
```

### Development Environment
```bash
# Environment variables
NODE_ENV=development
NODE_OPTIONS=--max-old-space-size=4096
VITE_HOST=0.0.0.0
VITE_PORT=3000

# Development server settings
VITE_HMR_PORT=3001
VITE_OPEN=false
VITE_CORS=true
```

### Asset Compilation
```javascript
// vite.config.js
export default defineConfig({
  plugins: [react()],
  server: {
    host: '0.0.0.0',
    port: 3000,
    hmr: {
      port: 3001
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          utils: ['lodash', 'axios']
        }
      }
    }
  }
});
```

## Production Configuration

### Build Optimization
```bash
# Production environment
NODE_ENV=production
NODE_OPTIONS=--max-old-space-size=8192

# Build settings
VITE_BUILD_TARGET=es2020
VITE_BUILD_MINIFY=terser
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_REPORT=true
```

### Asset Pipeline
```javascript
// Production build configuration
export default defineConfig({
  build: {
    target: 'es2020',
    minify: 'terser',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('node_modules')) {
            return 'vendor';
          }
          if (id.includes('src/components')) {
            return 'components';
          }
        }
      }
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
});
```

## Performance Optimization

### Build Performance
```bash
# Parallel processing
NODE_OPTIONS="--max-old-space-size=8192 --max-semi-space-size=512"

# Cache optimization
YARN_CACHE_FOLDER=/app/cache/yarn
NPM_CONFIG_CACHE=/app/cache/npm

# Build parallelization
VITE_BUILD_PARALLEL=true
VITE_BUILD_WORKERS=4
```

### Memory Management
```bash
# Memory allocation for large projects
NODE_OPTIONS="--max-old-space-size=8192"

# Garbage collection optimization
NODE_OPTIONS="--gc-interval=100 --max-old-space-size=8192"

# V8 optimization
NODE_OPTIONS="--optimize-for-size --max-old-space-size=8192"
```

## Docker Integration

### Docker Compose Configuration
```yaml
# docker-compose.yml
nodejs:
  image: node:18-alpine
  working_dir: /app
  environment:
    - NODE_ENV=development
    - NODE_OPTIONS=--max-old-space-size=4096
  volumes:
    - ./docker/scripts/hcm/themes/hcmpro:/app
    - /app/node_modules
    - ./docker/nodeJS/docker-entrypoint.sh:/docker-entrypoint.sh
  ports:
    - "3000:3000"
    - "3001:3001"
  command: ["sh", "/docker-entrypoint.sh"]
```

### Multi-stage Build
```dockerfile
# Dockerfile for production builds
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Build assets
RUN yarn build

# Production stage
FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
```

## Asset Management

### Frontend Assets
- **React Components**: TypeScript React components for H-CareManager
- **SCSS Compilation**: Modern CSS with Sass preprocessing
- **JavaScript Bundling**: ES6+ JavaScript with tree shaking
- **Image Optimization**: Automatic image compression and format conversion

### Build Outputs
```
/dist/
├── assets/
│   ├── css/           # Compiled and minified CSS
│   ├── js/            # Bundled and minified JavaScript
│   ├── images/        # Optimized images
│   └── fonts/         # Web fonts
├── index.html         # Main HTML entry point
└── manifest.json      # Asset manifest for cache busting
```

## Development Tools

### Hot Module Replacement
```javascript
// HMR configuration
if (import.meta.hot) {
  import.meta.hot.accept('./App.tsx', () => {
    // Handle hot updates
  });
}
```

### Development Server
```bash
# Start development server
docker exec nodejs yarn dev

# Watch mode for assets
docker exec nodejs yarn watch

# Type checking
docker exec nodejs yarn type-check
```

## Monitoring and Logging

### Build Monitoring
```bash
# Monitor build progress
docker logs nodejs -f

# Check build output
docker exec nodejs ls -la /app/dist

# Monitor memory usage
docker stats nodejs --no-stream
```

### Performance Analysis
```bash
# Bundle analysis
docker exec nodejs yarn build --analyze

# Build time analysis
time docker exec nodejs yarn build

# Memory usage monitoring
docker exec nodejs node --trace-gc app.js
```

## Troubleshooting

### Common Issues
1. **Out of Memory**: Increase NODE_OPTIONS max-old-space-size
2. **Slow Builds**: Enable build cache and parallel processing
3. **Module Resolution**: Check node_modules volume mounting
4. **Hot Reload Issues**: Verify port configuration and file watching

### Debug Commands
```bash
# Check Node.js version
docker exec nodejs node --version

# Verify dependencies
docker exec nodejs yarn list

# Check build configuration
docker exec nodejs yarn config list

# Monitor build process
docker exec nodejs yarn build --verbose
```

## Related Documentation
- [H-CareManager Frontend Documentation](../scripts/hcm/themes/hcmpro/docs.md)
- [Vite Build Configuration Documentation](../scripts/hcm/themes/hcmpro/vite.config.ts)
- [Package Management Documentation](../scripts/hcm/themes/hcmpro/package.json)
- [Docker Infrastructure Documentation](../README.md)

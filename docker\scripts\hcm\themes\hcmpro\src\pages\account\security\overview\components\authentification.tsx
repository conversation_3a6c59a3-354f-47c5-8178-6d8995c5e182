/**
 * H‑CareCloud Project – Security Authentication Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { SquarePen } from 'lucide-react';
import { useState, useEffect } from 'react';
import { hcmApi } from '@/services/hcm-api';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { Link } from 'react-router-dom';
import { toAbsoluteUrl } from '@/lib/helpers';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';

const Authentification = () => {
  const [securityData, setSecurityData] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    try {
      const data = await hcmApi.getSecurityOverview();
      setSecurityData(data);
    } catch (error) {
      toast.error('Failed to load security data');
      logger.error('Failed to load security authentication data', {
        component: 'Authentification',
        action: 'loadSecurityData'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card className="min-w-full">
        <CardHeader>
          <CardTitle>Authentication</CardTitle>
        </CardHeader>
        <CardContent className="kt-scrollable-x-auto pb-3 p-0">
          <Table className="align-middle text-sm text-muted-foreground">
            <TableBody>
              {[1, 2, 3].map((i) => (
                <TableRow key={i}>
                  <TableCell>
                    <Skeleton className="h-4 w-20" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-32" />
                  </TableCell>
                  <TableCell className="text-end">
                    <Skeleton className="h-6 w-16" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="min-w-full">
      <CardHeader>
        <CardTitle>Authentication</CardTitle>
      </CardHeader>
      <CardContent className="kt-scrollable-x-auto pb-3 p-0">
        <Table className="align-middle text-sm text-muted-foreground">
          <TableBody>
            <TableRow>
              <TableCell className="text-secondary-foreground font-normal">
                Password
              </TableCell>
              <TableCell className="text-secondary-foreground font-normal">
                {securityData.password_last_changed 
                  ? `Last changed ${new Date(securityData.password_last_changed).toLocaleDateString()}`
                  : 'Password set during account creation'
                }
              </TableCell>
              <TableCell className="text-end">
                <Button variant="ghost" mode="icon" asChild>
                  <Link to="/account/security/overview">
                    <SquarePen size={16} className="text-blue-500" />
                  </Link>
                </Button>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="text-secondary-foreground font-normal">
                2FA
              </TableCell>
              <TableCell className="text-secondary-foreground font-normal">
                {securityData.two_factor_enabled ? 'Enabled' : 'Not configured'}
              </TableCell>
              <TableCell className="text-end">
                <Button mode="link" underlined="dashed" asChild>
                  <Link to="/account/security/overview">
                    {securityData.two_factor_enabled ? 'Manage' : 'Setup'}
                  </Link>
                </Button>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="text-secondary-foreground font-normal">
                SSO Providers
              </TableCell>
              <TableCell className="text-secondary-foreground font-normal">
                <div className="flex items-center gap-2.5">
                  <span className="text-sm">
                    {securityData.sso_providers?.length > 0 
                      ? `${securityData.sso_providers.length} provider(s) configured`
                      : 'Not configured'
                    }
                  </span>
                  {securityData.sso_providers?.includes('google') && (
                    <img
                      src={toAbsoluteUrl('/media/brand-logos/google.svg')}
                      className="size-4"
                      alt="Google SSO"
                      title="Google SSO enabled"
                    />
                  )}
                  {securityData.sso_providers?.includes('microsoft') && (
                    <img
                      src={toAbsoluteUrl('/media/brand-logos/microsoft-5.svg')}
                      className="size-4"
                      alt="Microsoft SSO"
                      title="Microsoft SSO enabled"
                    />
                  )}
                  {securityData.sso_providers?.includes('azure') && (
                    <img
                      src={toAbsoluteUrl('/media/brand-logos/azure.svg')}
                      className="size-4"
                      alt="Azure AD SSO"
                      title="Azure AD SSO enabled"
                    />
                  )}
                </div>
              </TableCell>
              <TableCell className="text-end">
                <Button mode="link" underlined="dashed" asChild>
                  <Link to="/account/security/overview">
                    Configure
                  </Link>
                </Button>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export { Authentification };

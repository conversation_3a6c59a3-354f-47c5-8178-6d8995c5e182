/**
 * H‑CareCloud Project – Device Management Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { ReactElement, useEffect, useMemo, useState } from 'react';
import { hcmApi } from '@/services/hcm-api';
import {
  Column,
  ColumnDef,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  PaginationState,
  RowSelectionState,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import {
  Info,
  Laptop,
  Monitor,
  Search,
  Settings2,
  Smartphone,
  SquarePen,
  Tablet,
  TabletSmartphone,
  Trash2,
  X,
} from 'lucide-react';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardFooter,
  CardHeader,
  CardTable,
  CardToolbar,
} from '@/components/ui/card';
import { DataGrid, useDataGrid } from '@/components/ui/data-grid';
import { DataGridColumnHeader } from '@/components/ui/data-grid-column-header';
import { DataGridColumnVisibility } from '@/components/ui/data-grid-column-visibility';
import { DataGridPagination } from '@/components/ui/data-grid-pagination';
import {
  DataGridTable,
  DataGridTableRowSelect,
  DataGridTableRowSelectAll,
} from '@/components/ui/data-grid-table';
import { Input } from '@/components/ui/input';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface IColumnFilterProps<TData, TValue> {
  column: Column<TData, TValue>;
}

interface IData {
  id: string;
  device: {
    icon: ReactElement;
    name: string;
    browser: string;
  };
  ipAddress: string;
  location: string;
  added: string;
  lastSession: string;
}

const Device = () => {
  const [data, setData] = useState<IData[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [sorting, setSorting] = useState<SortingState>([]);

  const getDeviceIcon = (deviceType: string, browser: string): ReactElement => {
    const lowerDevice = deviceType.toLowerCase();
    const lowerBrowser = browser.toLowerCase();

    if (lowerDevice.includes('iphone') || lowerDevice.includes('android') || lowerDevice.includes('mobile')) {
      return <Smartphone className="text-xl text-muted-foreground" />;
    }
    if (lowerDevice.includes('ipad') || lowerDevice.includes('tablet')) {
      return <TabletSmartphone className="text-xl text-muted-foreground" />;
    }
    if (lowerDevice.includes('mac') || lowerBrowser.includes('safari')) {
      return <Monitor className="text-xl text-muted-foreground" />;
    }
    return <Laptop className="text-xl text-muted-foreground" />;
  };

  const loadDevices = async () => {
    try {
      setLoading(true);
      const devices = await hcmApi.getSecurityDevices();
      const mappedDevices: IData[] = devices.map((device: any) => ({
        id: device.id.toString(),
        device: {
          icon: getDeviceIcon(device.device_name, device.browser),
          name: device.device_name,
          browser: device.browser,
        },
        ipAddress: device.ip_address,
        location: device.location || 'Unknown',
        lastSession: device.last_session,
        added: device.created_at,
      }));
      setData(mappedDevices);

      logger.debug('Security devices loaded successfully', {
        component: 'DeviceManagement',
        action: 'loadDevices',
        data: { count: devices.length }
      });
    } catch (error) {
      toast.error('Failed to load security devices');
      logger.error('Failed to load security devices', {
        component: 'DeviceManagement',
        action: 'loadDevices'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDevices();
  }, []);


  const [searchQuery, setSearchQuery] = useState('');

  const filteredData = useMemo(() => {
    return data.filter((item) => {
      // Filter by search query (case-insensitive)
      const searchLower = searchQuery.toLowerCase();
      const matchesSearch =
        !searchQuery ||
        item.device.name.toLowerCase().includes(searchLower) ||
        item.device.browser.toLowerCase().includes(searchLower) ||
        item.ipAddress.toLowerCase().includes(searchLower) ||
        item.location.toLowerCase().includes(searchLower);

      return matchesSearch;
    });
  }, [searchQuery]);

  const ColumnInputFilter = <TData, TValue>({
    column,
  }: IColumnFilterProps<TData, TValue>) => {
    return (
      <Input
        placeholder="Filter..."
        value={(column.getFilterValue() as string) ?? ''}
        onChange={(event) => column.setFilterValue(event.target.value)}
        size="sm"
        className="max-w-40"
      />
    );
  };

  const columns = useMemo<ColumnDef<IData>[]>(
    () => [
      {
        accessorKey: 'id',
        accessorFn: (row) => row.id,
        header: () => <DataGridTableRowSelectAll />,
        cell: ({ row }) => <DataGridTableRowSelect row={row} />,
        enableSorting: false,
        enableHiding: false,
        enableResizing: false,
        size: 51,
        meta: {
          cellClassName: '',
        },
      },
      {
        id: 'device',
        accessorFn: (row) => row.device,
        header: ({ column }) => (
          <DataGridColumnHeader
            title="Device"
            filter={<ColumnInputFilter column={column} />}
            column={column}
          />
        ),
        cell: (info) => (
          <div className="flex items-center gap-4">
            {info.row.original.device.icon}
            <div className="flex flex-col gap-0.5">
              <span className="leading-none font-medium text-sm text-mono">
                {info.row.original.device.name}
              </span>
              <span className="text-sm text-secondary-foreground font-normal">
                {info.row.original.device.browser}
              </span>
            </div>
          </div>
        ),
        enableSorting: true,
        size: 250,
        meta: {
          cellClassName: '',
        },
        filterFn: (row, columnId, filterValue) => {
          const device = row.getValue(columnId) as {
            name: string;
            browser: string;
          };
          const filterLower = filterValue.toLowerCase();
          return (
            device.name.toLowerCase().includes(filterLower) ||
            device.browser.toLowerCase().includes(filterLower)
          );
        },
      },
      {
        id: 'ipAddress',
        accessorFn: (row) => row.ipAddress,
        header: ({ column }) => (
          <DataGridColumnHeader title="IP Address" column={column} />
        ),
        cell: (info) => (
          <span className="text-sm text-foreground font-normal">
            {info.row.original.ipAddress}
          </span>
        ),
        enableSorting: true,
        size: 165,
        meta: {
          cellClassName: '',
        },
      },
      {
        id: 'location',
        accessorFn: (row) => row.location,
        header: ({ column }) => (
          <DataGridColumnHeader title="Location" column={column} />
        ),
        cell: (info) => (
          <span className="text-sm text-foreground font-normal">
            {info.row.original.location}
          </span>
        ),
        enableSorting: true,
        size: 165,
        meta: {
          cellClassName: '',
        },
      },
      {
        id: 'added',
        accessorFn: (row) => row.added,
        header: ({ column }) => (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4" />
                  <DataGridColumnHeader
                    title="Added"
                    visibility={true}
                    column={column}
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Time is based on your local timezone.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ),
        cell: (info) => (
          <span className="text-sm text-foreground font-normal">
            {info.row.original.added}
          </span>
        ),
        enableSorting: true,
        size: 165,
        meta: {
          cellClassName: '',
        },
      },
      {
        id: 'lastSession',
        accessorFn: (row) => row.lastSession,
        header: ({ column }) => (
          <DataGridColumnHeader title="Last Session" column={column} />
        ),
        cell: (info) => (
          <span className="text-sm text-foreground font-normal">
            {info.row.original.lastSession}
          </span>
        ),
        enableSorting: true,
        size: 165,
        meta: {
          cellClassName: '',
        },
      },
      {
        id: 'edit',
        header: () => '',
        cell: () => (
          <Button variant="ghost">
            <SquarePen />
          </Button>
        ),
        enableSorting: false,
        size: 70,
        meta: {
          headerClassName: '',
        },
      },
      {
        id: 'trash',
        header: () => '',
        cell: () => (
          <Button variant="ghost">
            <Trash2 />
          </Button>
        ),
        enableSorting: false,
        size: 70,
        meta: {
          headerClassName: '',
        },
      },
    ],
    [],
  );

  useEffect(() => {
    const selectedRowIds = Object.keys(rowSelection);

    if (selectedRowIds.length > 0) {
      toast(`Total ${selectedRowIds.length} are selected.`, {
        description: `Selected row IDs: ${selectedRowIds}`,
        action: {
          label: 'Undo',
          onClick: () => {
            logger.debug('Device selection undo requested', {
              component: 'DeviceManagement',
              action: 'undoSelection',
              data: { selectedRowIds }
            });
            setRowSelection({});
          },
        },
      });
    }
  }, [rowSelection]);

  const table = useReactTable({
    columns,
    data: filteredData,
    pageCount: Math.ceil((filteredData?.length || 0) / pagination.pageSize),
    getRowId: (row: IData) => row.id,
    state: {
      pagination,
      sorting,
    },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  const Toolbar = () => {
    const { table } = useDataGrid();

    return (
      <CardToolbar>
        <div className="flex flex-wrap items-center gap-2.5">
          <Button>Add Device</Button>
        </div>
        <DataGridColumnVisibility
          table={table}
          trigger={
            <Button variant="outline">
              <Settings2 />
              Columns
            </Button>
          }
        />
      </CardToolbar>
    );
  };

  return (
    <DataGrid
      table={table}
      recordCount={filteredData?.length || 0}
      tableLayout={{
        columnsPinnable: true,
        columnsMovable: true,
        columnsVisibility: true,
        cellBorder: true,
      }}
    >
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2.5">
            <div className="relative">
              <Search className="size-4 text-muted-foreground absolute start-3 top-1/2 -translate-y-1/2" />
              <Input
                placeholder="Search Devices..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="ps-9 w-40"
              />
              {searchQuery.length > 0 && (
                <Button
                  mode="icon"
                  variant="ghost"
                  className="absolute end-1.5 top-1/2 -translate-y-1/2 h-6 w-6"
                  onClick={() => setSearchQuery('')}
                >
                  <X />
                </Button>
              )}
            </div>
          </div>
          <Toolbar />
        </CardHeader>
        <CardTable>
          <ScrollArea>
            <DataGridTable />
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </CardTable>
        <CardFooter>
          <DataGridPagination />
        </CardFooter>
      </Card>
    </DataGrid>
  );
};

export { Device };

/**
 * H‑CareCloud Project – Account Settings Modal Page
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { Fragment, useState } from 'react';
import { PageNavbar } from '@/pages/account';
import {
  Toolbar,
  ToolbarActions,
  ToolbarDescription,
  ToolbarHeading,
  ToolbarPageTitle,
} from '@/partials/common/toolbar';
import { Settings, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Container } from '@/components/common/container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AccountSettingsModal } from '.';

export function AccountSettingsModalPage() {
  const [settingsModalOpen, setSettingsModalOpen] = useState(false);
  
  const handleSettingsModalToggle = () => {
    setSettingsModalOpen(!settingsModalOpen);
  };

  return (
    <Fragment>
      <PageNavbar />
      <Container>
        <Toolbar>
          <ToolbarHeading>
            <ToolbarPageTitle>
              <Settings className="text-primary" />
              Settings Modal
            </ToolbarPageTitle>
            <ToolbarDescription text="H-CareManager settings interface with modal dialog" />
          </ToolbarHeading>
          <ToolbarActions>
            <Button onClick={handleSettingsModalToggle} variant="primary">
              <Settings size={16} />
              Open Settings
            </Button>
          </ToolbarActions>
        </Toolbar>
      </Container>
      
      <Container>
        <div className="grid gap-5 lg:gap-7.5">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User size={20} />
                Settings Modal Interface
              </CardTitle>
              <Badge variant="secondary">H-CareManager Pro Feature</Badge>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                The settings modal provides a centralized interface for managing all H-CareManager 
                configuration options. Access profile settings, security configuration, API management, 
                notifications, appearance customization, and server management tools.
              </p>
              <div className="flex gap-2">
                <Button onClick={handleSettingsModalToggle}>
                  <Settings size={16} />
                  Launch Settings Modal
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <AccountSettingsModal
          open={settingsModalOpen}
          onOpenChange={handleSettingsModalToggle}
        />
      </Container>
    </Fragment>
  );
}
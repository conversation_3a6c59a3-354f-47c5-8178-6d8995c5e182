# H-CareManager Network Pages

## Overview
This directory contains network management pages for H-CareManager Project, providing team organization, department management, and network administration for server management teams. The network pages focus on organizing server administration teams and managing access to H-CareCloud infrastructure.

## Architecture

### Network Pages Structure
```
/src/pages/network/
├── teams/                  # Team management pages
│   ├── TeamsPage.tsx      # Team listing and management
│   ├── TeamDetails.tsx    # Individual team details
│   └── CreateTeam.tsx     # Team creation form
├── departments/           # Department management pages
│   ├── DepartmentsPage.tsx # Department listing
│   ├── DepartmentDetails.tsx # Department details
│   └── CreateDepartment.tsx # Department creation
└── components/            # Network-specific components
    ├── TeamCard.tsx       # Team display card
    ├── MemberList.tsx     # Team member listing
    └── OrgChart.tsx       # Organization chart
```

### Network Categories
- **Teams**: Server management teams and groups
- **Departments**: Organizational departments with server access
- **Members**: Team members and their roles
- **Permissions**: Network-based access control

## Network Pages

### teams/TeamsPage.tsx
**Purpose**: Team management for server administration groups.

**Key Features**:
- Server management team listing
- Team creation and editing
- Member management
- Permission assignment
- Team performance metrics

**Implementation**:
```typescript
export const TeamsPage: React.FC = () => {
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  
  const { checkPermission } = usePermissions();
  const { success, error } = useToast();

  useEffect(() => {
    loadTeams();
  }, []);

  const loadTeams = async () => {
    try {
      setLoading(true);
      const data = await hcmApi.getTeams();
      setTeams(data);
    } catch (err) {
      error('Failed to load teams');
    } finally {
      setLoading(false);
    }
  };

  const filteredTeams = useMemo(() => {
    return teams.filter(team =>
      team.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      team.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [teams, searchTerm]);

  if (loading) {
    return (
      <div className="row g-6 g-xl-9">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="col-md-6 col-xl-4">
            <Skeleton className="w-100 h-64" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="content d-flex flex-column flex-column-fluid">
      <div className="container-xxl">
        {/* Page Header */}
        <div className="page-title d-flex flex-column justify-content-center flex-wrap me-3 mb-5">
          <h1 className="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
            Server Management Teams
          </h1>
          <span className="text-muted fw-semibold fs-7">
            Organize and manage server administration teams
          </span>
        </div>

        {/* Toolbar */}
        <div className="d-flex flex-wrap flex-stack mb-6">
          <div className="d-flex flex-wrap align-items-center">
            <div className="position-relative me-4">
              <i className="fas fa-search fs-3 text-gray-500 position-absolute top-50 translate-middle ms-6"></i>
              <input
                type="text"
                className="form-control form-control-solid w-250px ps-13"
                placeholder="Search teams..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          
          {checkPermission('team.create') && (
            <div className="d-flex flex-wrap">
              <Button
                variant="primary"
                onClick={() => navigate('/network/teams/create')}
              >
                <i className="fas fa-plus fs-2"></i>
                Create Team
              </Button>
            </div>
          )}
        </div>

        {/* Teams Grid */}
        <div className="row g-6 g-xl-9">
          {filteredTeams.map((team) => (
            <div key={team.id} className="col-md-6 col-xl-4">
              <TeamCard
                team={team}
                onEdit={() => navigate(`/network/teams/${team.id}/edit`)}
                onView={() => navigate(`/network/teams/${team.id}`)}
                onDelete={() => handleDeleteTeam(team.id)}
              />
            </div>
          ))}
        </div>

        {filteredTeams.length === 0 && (
          <div className="text-center py-10">
            <div className="mb-5">
              <i className="fas fa-users fs-4x text-muted"></i>
            </div>
            <h3 className="text-gray-800 fw-bold mb-3">No Teams Found</h3>
            <p className="text-muted fs-6">
              {searchTerm ? 'No teams match your search criteria.' : 'Create your first server management team.'}
            </p>
            {checkPermission('team.create') && !searchTerm && (
              <Button
                variant="primary"
                onClick={() => navigate('/network/teams/create')}
              >
                Create Team
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
```

### departments/DepartmentsPage.tsx
**Purpose**: Department management for organizational structure.

**Key Features**:
- Department hierarchy management
- Server access by department
- Department member management
- Resource allocation
- Performance tracking

## Network Components

### TeamCard.tsx
**Purpose**: Team display card with member count and actions.

**Implementation**:
```typescript
interface TeamCardProps {
  team: Team;
  onEdit: () => void;
  onView: () => void;
  onDelete: () => void;
}

export const TeamCard: React.FC<TeamCardProps> = ({
  team,
  onEdit,
  onView,
  onDelete
}) => {
  const { checkPermission } = usePermissions();

  return (
    <div className="card">
      <div className="card-body d-flex flex-center flex-column pt-12 p-9">
        {/* Team Avatar */}
        <div className="symbol symbol-65px symbol-circle mb-5">
          <span className="symbol-label fs-2x fw-semibold text-primary bg-light-primary">
            {team.name.charAt(0).toUpperCase()}
          </span>
        </div>

        {/* Team Info */}
        <div className="text-center mb-5">
          <h3 className="fs-4 fw-bold text-gray-900 mb-2">{team.name}</h3>
          <div className="fs-6 fw-semibold text-gray-400 mb-3">
            {team.description}
          </div>
          
          {/* Team Stats */}
          <div className="d-flex flex-center flex-wrap mb-5">
            <div className="border border-gray-300 border-dashed rounded min-w-80px py-3 px-4 mx-2 mb-3">
              <div className="fs-6 fw-bold text-gray-700">{team.memberCount}</div>
              <div className="fw-semibold text-gray-400">Members</div>
            </div>
            <div className="border border-gray-300 border-dashed rounded min-w-80px py-3 px-4 mx-2 mb-3">
              <div className="fs-6 fw-bold text-gray-700">{team.serverCount}</div>
              <div className="fw-semibold text-gray-400">Servers</div>
            </div>
          </div>
        </div>

        {/* Team Actions */}
        <div className="d-flex flex-center">
          <Button
            variant="light"
            size="sm"
            className="me-2"
            onClick={onView}
          >
            View Details
          </Button>
          
          {checkPermission('team.update') && (
            <Button
              variant="light-primary"
              size="sm"
              className="me-2"
              onClick={onEdit}
            >
              Edit
            </Button>
          )}
          
          {checkPermission('team.delete') && (
            <Button
              variant="light-danger"
              size="sm"
              onClick={onDelete}
            >
              Delete
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
```

### MemberList.tsx
**Purpose**: Team member listing with roles and permissions.

**Implementation**:
```typescript
interface MemberListProps {
  teamId: string;
  members: TeamMember[];
  onAddMember: () => void;
  onRemoveMember: (memberId: string) => void;
  onUpdateRole: (memberId: string, role: string) => void;
}

export const MemberList: React.FC<MemberListProps> = ({
  teamId,
  members,
  onAddMember,
  onRemoveMember,
  onUpdateRole
}) => {
  const { checkPermission } = usePermissions();

  return (
    <div className="card">
      <div className="card-header border-0 pt-6">
        <div className="card-title">
          <h3 className="fw-bold m-0">Team Members</h3>
        </div>
        <div className="card-toolbar">
          {checkPermission('team.manage_members') && (
            <Button
              variant="primary"
              size="sm"
              onClick={onAddMember}
            >
              <i className="fas fa-plus fs-2"></i>
              Add Member
            </Button>
          )}
        </div>
      </div>
      
      <div className="card-body py-4">
        <div className="table-responsive">
          <table className="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
            <thead>
              <tr className="fw-bold text-muted">
                <th className="min-w-150px">Member</th>
                <th className="min-w-100px">Role</th>
                <th className="min-w-100px">Joined</th>
                <th className="min-w-100px">Status</th>
                <th className="min-w-100px text-end">Actions</th>
              </tr>
            </thead>
            <tbody>
              {members.map((member) => (
                <tr key={member.id}>
                  <td>
                    <div className="d-flex align-items-center">
                      <div className="symbol symbol-45px me-5">
                        <img
                          src={member.avatar || '/default-avatar.png'}
                          alt={member.name}
                          className="symbol-label"
                        />
                      </div>
                      <div className="d-flex justify-content-start flex-column">
                        <span className="text-dark fw-bold text-hover-primary fs-6">
                          {member.name}
                        </span>
                        <span className="text-muted fw-semibold text-muted d-block fs-7">
                          {member.email}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td>
                    {checkPermission('team.manage_members') ? (
                      <select
                        className="form-select form-select-sm"
                        value={member.role}
                        onChange={(e) => onUpdateRole(member.id, e.target.value)}
                      >
                        <option value="member">Member</option>
                        <option value="lead">Team Lead</option>
                        <option value="admin">Admin</option>
                      </select>
                    ) : (
                      <span className="badge badge-light-primary fw-bold">
                        {member.role}
                      </span>
                    )}
                  </td>
                  <td>
                    <span className="text-dark fw-bold d-block fs-7">
                      {formatDate(member.joinedAt, 'MM/DD/YYYY')}
                    </span>
                  </td>
                  <td>
                    <span className={`badge badge-light-${member.status === 'active' ? 'success' : 'warning'} fw-bold`}>
                      {member.status.toUpperCase()}
                    </span>
                  </td>
                  <td className="text-end">
                    {checkPermission('team.manage_members') && (
                      <Button
                        variant="light-danger"
                        size="sm"
                        onClick={() => onRemoveMember(member.id)}
                      >
                        Remove
                      </Button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {members.length === 0 && (
          <div className="text-center py-10">
            <div className="mb-5">
              <i className="fas fa-user-friends fs-4x text-muted"></i>
            </div>
            <h3 className="text-gray-800 fw-bold mb-3">No Members</h3>
            <p className="text-muted fs-6">
              Add members to start building your server management team.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
```

## Network Management Features

### Team Organization
- Server management team creation and management
- Team member roles and permissions
- Team-based server access control
- Performance tracking and metrics

### Department Structure
- Organizational department hierarchy
- Department-based resource allocation
- Cross-department collaboration
- Reporting and analytics

### Access Control
- Role-based team permissions
- Server access by team membership
- Department-level access control
- Audit logging for team actions

## Related Documentation
- [Account Pages Documentation](../account/README.md)
- [Components Documentation](../../components/README.md)
- [Services Documentation](../../services/README.md)
- [H-CareManager Project Documentation](../../../docs.md)

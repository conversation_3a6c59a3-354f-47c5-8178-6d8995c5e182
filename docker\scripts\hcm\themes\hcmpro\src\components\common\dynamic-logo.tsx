/**
 * H‑CareCloud Project – Dynamic Logo Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { useState, useEffect } from 'react';
import { appearanceService } from '@/services/appearance-service';
import { logger } from '@/lib/logger';

interface DynamicLogoProps {
  className?: string;
  alt?: string;
  width?: number;
  height?: number;
  fallbackSrc?: string;
}

export function DynamicLogo({ 
  className = '', 
  alt = 'H-CareManager Logo',
  width,
  height,
  fallbackSrc = '/media/logos/hcaremanager-logo.svg'
}: DynamicLogoProps) {
  const [logoUrl, setLogoUrl] = useState<string>(fallbackSrc);
  const [companyName, setCompanyName] = useState<string>('H-CareManager');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    loadLogo();
  }, []);

  const loadLogo = async () => {
    try {
      setIsLoading(true);
      setHasError(false);
      
      const [logoUrl, companyName] = await Promise.all([
        appearanceService.getLogoUrl(),
        appearanceService.getCompanyName()
      ]);
      
      setLogoUrl(logoUrl);
      setCompanyName(companyName);
      
      logger.debug('Dynamic logo loaded', {
        component: 'DynamicLogo',
        action: 'loadLogo',
        data: { logoUrl, companyName }
      });
    } catch (error) {
      logger.error('Failed to load dynamic logo', {
        component: 'DynamicLogo',
        action: 'loadLogo'
      }, error as Error);
      
      setHasError(true);
      setLogoUrl(fallbackSrc);
      setCompanyName('H-CareManager');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageError = () => {
    if (!hasError) {
      setHasError(true);
      setLogoUrl(fallbackSrc);
      
      logger.warn('Logo image failed to load, using fallback', {
        component: 'DynamicLogo',
        action: 'handleImageError',
        data: { originalUrl: logoUrl, fallbackSrc }
      });
    }
  };

  if (isLoading) {
    return (
      <div 
        className={`animate-pulse bg-gray-200 dark:bg-gray-700 rounded ${className}`}
        style={{ width: width || 'auto', height: height || '32px' }}
      />
    );
  }

  return (
    <img
      src={logoUrl}
      alt={`${companyName} ${alt}`}
      className={className}
      width={width}
      height={height}
      onError={handleImageError}
      loading="lazy"
    />
  );
}

/**
 * Dynamic Logo Text Component - shows company name with fallback
 */
interface DynamicLogoTextProps {
  className?: string;
  fallbackText?: string;
}

export function DynamicLogoText({ 
  className = '', 
  fallbackText = 'H-CareManager' 
}: DynamicLogoTextProps) {
  const [companyName, setCompanyName] = useState<string>(fallbackText);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadCompanyName();
  }, []);

  const loadCompanyName = async () => {
    try {
      setIsLoading(true);
      const name = await appearanceService.getCompanyName();
      setCompanyName(name);
      
      logger.debug('Dynamic company name loaded', {
        component: 'DynamicLogoText',
        action: 'loadCompanyName',
        data: { companyName: name }
      });
    } catch (error) {
      logger.error('Failed to load company name', {
        component: 'DynamicLogoText',
        action: 'loadCompanyName'
      }, error as Error);
      
      setCompanyName(fallbackText);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse bg-gray-200 dark:bg-gray-700 rounded h-6 w-32 ${className}`} />
    );
  }

  return (
    <span className={className}>
      {companyName}
    </span>
  );
}

/**
 * Combined Logo and Text Component
 */
interface DynamicBrandProps {
  logoClassName?: string;
  textClassName?: string;
  containerClassName?: string;
  showText?: boolean;
  logoWidth?: number;
  logoHeight?: number;
}

export function DynamicBrand({
  logoClassName = 'h-8 w-auto',
  textClassName = 'text-xl font-bold text-foreground',
  containerClassName = 'flex items-center gap-3',
  showText = true,
  logoWidth,
  logoHeight
}: DynamicBrandProps) {
  return (
    <div className={containerClassName}>
      <DynamicLogo 
        className={logoClassName}
        width={logoWidth}
        height={logoHeight}
      />
      {showText && (
        <DynamicLogoText className={textClassName} />
      )}
    </div>
  );
}

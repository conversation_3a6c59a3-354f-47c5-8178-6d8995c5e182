/**
 * H‑CareCloud Project – Reset Password Check Email Page
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { useEffect, useState } from 'react';
import { Link, useSearchParams } from 'react-router';
import { toAbsoluteUrl } from '@/lib/helpers';
import { Button } from '@/components/ui/button';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { toast } from 'sonner';

const ResetPasswordCheckEmail = () => {
  const [searchParams] = useSearchParams();
  const [email, setEmail] = useState<string | null>(null);
  const [isResending, setIsResending] = useState(false);

  useEffect(() => {
    const emailParam = searchParams.get('email');
    setEmail(emailParam);

    if (emailParam) {
      logger.debug('Password reset email check page loaded', {
        component: 'ResetPasswordCheckEmail',
        action: 'pageLoad',
        data: { email: emailParam }
      });
    }
  }, [searchParams]);

  const handleResendEmail = async () => {
    if (!email) return;

    try {
      setIsResending(true);
      await hcmApi.requestPasswordReset(email);
      toast.success('Password reset email sent successfully');
      logger.debug('Password reset email resent', {
        component: 'ResetPasswordCheckEmail',
        action: 'resendEmail',
        data: { email }
      });
    } catch (error) {
      toast.error('Failed to resend password reset email');
      logger.error('Failed to resend password reset email', {
        component: 'ResetPasswordCheckEmail',
        action: 'resendEmail',
        data: { email }
      }, error as Error);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="card max-w-[440px] w-full">
      <div className="card-body p-10">
        <div className="flex justify-center py-10">
          <img
            src={toAbsoluteUrl('/media/illustrations/30.svg')}
            className="dark:hidden max-h-[130px]"
            alt=""
          />
          <img
            src={toAbsoluteUrl('/media/illustrations/30-dark.svg')}
            className="light:hidden max-h-[130px]"
            alt=""
          />
        </div>

        <h3 className="text-lg font-medium text-mono text-center mb-3">
          Check your email
        </h3>
        <div className="text-sm text-center text-secondary-foreground mb-7.5">
          Please click the link sent to your email{' '}
          <span className="text-sm text-foreground font-medium">
            {email || 'your email address'}
          </span>
          <br />
          to reset your password. Thank you
        </div>

        <div className="flex justify-center mb-5">
          <Link
            to={
              settings?.layout === 'auth-branded'
                ? '/auth/reset-password/changed'
                : '/auth/classic/reset-password/changed'
            }
            className="btn btn-primary flex justify-center"
          >
            Skip for now
          </Link>
        </div>

        <div className="flex items-center justify-center gap-1">
          <span className="text-xs text-secondary-foreground">
            Didn’t receive an email?
          </span>
          <Link
            to={
              settings?.layout === 'auth-branded'
                ? '/auth/reset-password/enter-email'
                : '/auth/classic/reset-password/enter-email'
            }
            className="text-xs font-medium link"
          >
            Resend
          </Link>
        </div>
      </div>
    </div>
  );
};

export { ResetPasswordCheckEmail };

# H-CareManager Providers

## Overview
This directory contains React context providers for H-CareManager Project, managing global application state, theme configuration, and cross-component data sharing. The providers system ensures consistent state management and provides a clean architecture for data flow throughout the server management dashboard.

## Architecture

### Providers Structure
```
/src/providers/
├── index.tsx               # Combined providers wrapper
├── ThemeProvider.tsx       # Metronic theme and styling provider
├── NotificationProvider.tsx # Toast notifications and alerts
├── LoadingProvider.tsx     # Global loading state management
├── WebSocketProvider.tsx   # Real-time communication provider
└── SettingsProvider.tsx    # Application settings and preferences
```

### Provider Hierarchy
```typescript
// Provider composition in main.tsx
<HcmAuthProvider>
  <ThemeProvider>
    <NotificationProvider>
      <LoadingProvider>
        <WebSocketProvider>
          <SettingsProvider>
            <App />
          </SettingsProvider>
        </WebSocketProvider>
      </LoadingProvider>
    </NotificationProvider>
  </ThemeProvider>
</HcmAuthProvider>
```

## Core Providers

### index.tsx
**Purpose**: Combined providers wrapper for clean application setup.

**Implementation**:
```typescript
interface ProvidersProps {
  children: React.ReactNode;
}

export const Providers: React.FC<ProvidersProps> = ({ children }) => {
  return (
    <HcmAuthProvider>
      <ThemeProvider>
        <NotificationProvider>
          <LoadingProvider>
            <WebSocketProvider>
              <SettingsProvider>
                {children}
              </SettingsProvider>
            </WebSocketProvider>
          </LoadingProvider>
        </NotificationProvider>
      </ThemeProvider>
    </HcmAuthProvider>
  );
};

// Usage in main.tsx
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Providers>
      <App />
    </Providers>
  </React.StrictMode>
);
```

### ThemeProvider.tsx
**Purpose**: Metronic theme management and styling configuration.

**Key Features**:
- Metronic HCCManagerPro theme integration
- Dark/light mode switching
- Theme customization and branding
- CSS variable management
- Responsive design configuration

**Implementation**:
```typescript
interface ThemeState {
  mode: 'light' | 'dark';
  primaryColor: string;
  layout: 'default' | 'compact';
  sidebar: 'expanded' | 'collapsed';
  customizations: ThemeCustomizations;
}

interface ThemeContextType {
  theme: ThemeState;
  setTheme: (theme: Partial<ThemeState>) => void;
  toggleMode: () => void;
  resetTheme: () => void;
  applyCustomizations: (customizations: ThemeCustomizations) => void;
}

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setThemeState] = useState<ThemeState>(() => {
    // Load theme from storage
    const savedTheme = storageService.get('hcm_theme');
    return savedTheme || defaultTheme;
  });

  const setTheme = useCallback((newTheme: Partial<ThemeState>) => {
    setThemeState(prev => {
      const updated = { ...prev, ...newTheme };
      
      // Save to storage
      storageService.set('hcm_theme', updated);
      
      // Apply CSS variables
      applyThemeVariables(updated);
      
      return updated;
    });
  }, []);

  const toggleMode = useCallback(() => {
    setTheme({ mode: theme.mode === 'light' ? 'dark' : 'light' });
  }, [theme.mode, setTheme]);

  // Apply theme on mount
  useEffect(() => {
    applyThemeVariables(theme);
  }, []);

  return (
    <ThemeContext.Provider value={{ theme, setTheme, toggleMode, resetTheme, applyCustomizations }}>
      <div className={`theme-${theme.mode} layout-${theme.layout}`}>
        {children}
      </div>
    </ThemeContext.Provider>
  );
};
```

### NotificationProvider.tsx
**Purpose**: Global notification and toast message management.

**Key Features**:
- Toast notifications (success, error, warning, info)
- Notification queue management
- Auto-dismiss and manual dismiss
- Position and styling configuration
- Accessibility support

**Implementation**:
```typescript
interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  persistent?: boolean;
  actions?: NotificationAction[];
}

interface NotificationContextType {
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id'>) => string;
  removeNotification: (id: string) => void;
  clearAll: () => void;
  
  // Convenience methods
  success: (message: string, options?: NotificationOptions) => string;
  error: (message: string, options?: NotificationOptions) => string;
  warning: (message: string, options?: NotificationOptions) => string;
  info: (message: string, options?: NotificationOptions) => string;
}

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const addNotification = useCallback((notification: Omit<Notification, 'id'>) => {
    const id = generateId();
    const newNotification: Notification = {
      id,
      duration: 5000,
      ...notification
    };

    setNotifications(prev => [...prev, newNotification]);

    // Auto-dismiss if not persistent
    if (!newNotification.persistent && newNotification.duration) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  // Convenience methods
  const success = useCallback((message: string, options?: NotificationOptions) => {
    return addNotification({ type: 'success', message, ...options });
  }, [addNotification]);

  const error = useCallback((message: string, options?: NotificationOptions) => {
    return addNotification({ type: 'error', message, persistent: true, ...options });
  }, [addNotification]);

  return (
    <NotificationContext.Provider value={{
      notifications,
      addNotification,
      removeNotification,
      clearAll,
      success,
      error,
      warning,
      info
    }}>
      {children}
      <NotificationContainer notifications={notifications} onRemove={removeNotification} />
    </NotificationContext.Provider>
  );
};
```

### LoadingProvider.tsx
**Purpose**: Global loading state management for async operations.

**Key Features**:
- Global loading overlay
- Component-specific loading states
- Loading queue management
- Progress indication
- Cancellation support

**Implementation**:
```typescript
interface LoadingState {
  isLoading: boolean;
  loadingTasks: Map<string, LoadingTask>;
  progress?: number;
}

interface LoadingTask {
  id: string;
  label: string;
  progress?: number;
  cancellable?: boolean;
  onCancel?: () => void;
}

interface LoadingContextType {
  isLoading: boolean;
  progress?: number;
  activeTasks: LoadingTask[];
  
  startLoading: (taskId: string, label: string, options?: LoadingOptions) => void;
  stopLoading: (taskId: string) => void;
  updateProgress: (taskId: string, progress: number) => void;
  cancelTask: (taskId: string) => void;
  clearAll: () => void;
}

export const LoadingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    loadingTasks: new Map()
  });

  const startLoading = useCallback((taskId: string, label: string, options?: LoadingOptions) => {
    setLoadingState(prev => {
      const newTasks = new Map(prev.loadingTasks);
      newTasks.set(taskId, {
        id: taskId,
        label,
        progress: options?.progress,
        cancellable: options?.cancellable,
        onCancel: options?.onCancel
      });

      return {
        isLoading: true,
        loadingTasks: newTasks,
        progress: calculateOverallProgress(newTasks)
      };
    });
  }, []);

  const stopLoading = useCallback((taskId: string) => {
    setLoadingState(prev => {
      const newTasks = new Map(prev.loadingTasks);
      newTasks.delete(taskId);

      return {
        isLoading: newTasks.size > 0,
        loadingTasks: newTasks,
        progress: calculateOverallProgress(newTasks)
      };
    });
  }, []);

  return (
    <LoadingContext.Provider value={{
      isLoading: loadingState.isLoading,
      progress: loadingState.progress,
      activeTasks: Array.from(loadingState.loadingTasks.values()),
      startLoading,
      stopLoading,
      updateProgress,
      cancelTask,
      clearAll
    }}>
      {children}
      {loadingState.isLoading && (
        <LoadingOverlay 
          tasks={Array.from(loadingState.loadingTasks.values())}
          progress={loadingState.progress}
        />
      )}
    </LoadingContext.Provider>
  );
};
```

### WebSocketProvider.tsx
**Purpose**: Real-time communication and live updates management.

**Key Features**:
- WebSocket connection management
- Real-time notifications
- Live data updates
- Connection status monitoring
- Automatic reconnection

**Implementation**:
```typescript
interface WebSocketState {
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  lastMessage?: any;
  subscriptions: Map<string, WebSocketSubscription>;
}

interface WebSocketContextType {
  isConnected: boolean;
  connectionStatus: string;
  
  subscribe: (channel: string, callback: (data: any) => void) => () => void;
  unsubscribe: (channel: string) => void;
  send: (message: any) => void;
  reconnect: () => void;
}

export const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [wsState, setWsState] = useState<WebSocketState>({
    isConnected: false,
    connectionStatus: 'disconnected',
    subscriptions: new Map()
  });
  
  const wsRef = useRef<WebSocket | null>(null);
  const { user } = useAuth();

  const connect = useCallback(() => {
    if (!user) return;

    const wsUrl = `${import.meta.env.VITE_WEBSOCKET_URL}?token=${authService.getToken()}`;
    wsRef.current = new WebSocket(wsUrl);

    wsRef.current.onopen = () => {
      setWsState(prev => ({
        ...prev,
        isConnected: true,
        connectionStatus: 'connected'
      }));
    };

    wsRef.current.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      // Handle subscriptions
      wsState.subscriptions.forEach((subscription, channel) => {
        if (data.channel === channel) {
          subscription.callback(data.payload);
        }
      });

      setWsState(prev => ({
        ...prev,
        lastMessage: data
      }));
    };

    wsRef.current.onclose = () => {
      setWsState(prev => ({
        ...prev,
        isConnected: false,
        connectionStatus: 'disconnected'
      }));

      // Attempt reconnection
      setTimeout(connect, 5000);
    };
  }, [user, wsState.subscriptions]);

  const subscribe = useCallback((channel: string, callback: (data: any) => void) => {
    setWsState(prev => {
      const newSubscriptions = new Map(prev.subscriptions);
      newSubscriptions.set(channel, { callback });
      return { ...prev, subscriptions: newSubscriptions };
    });

    // Send subscription message
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'subscribe',
        channel
      }));
    }

    // Return unsubscribe function
    return () => unsubscribe(channel);
  }, []);

  return (
    <WebSocketContext.Provider value={{
      isConnected: wsState.isConnected,
      connectionStatus: wsState.connectionStatus,
      subscribe,
      unsubscribe,
      send,
      reconnect: connect
    }}>
      {children}
    </WebSocketContext.Provider>
  );
};
```

### SettingsProvider.tsx
**Purpose**: Application settings and user preferences management.

**Key Features**:
- User preferences storage
- Application configuration
- Feature flags management
- Localization settings
- Performance preferences

**Implementation**:
```typescript
interface AppSettings {
  language: string;
  timezone: string;
  dateFormat: string;
  notifications: NotificationSettings;
  performance: PerformanceSettings;
  accessibility: AccessibilitySettings;
  features: FeatureFlags;
}

interface SettingsContextType {
  settings: AppSettings;
  updateSettings: (settings: Partial<AppSettings>) => void;
  resetSettings: () => void;
  exportSettings: () => string;
  importSettings: (settingsJson: string) => void;
}

export const SettingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [settings, setSettings] = useState<AppSettings>(() => {
    // Load settings from storage
    const savedSettings = storageService.get('hcm_settings');
    return { ...defaultSettings, ...savedSettings };
  });

  const updateSettings = useCallback((newSettings: Partial<AppSettings>) => {
    setSettings(prev => {
      const updated = { ...prev, ...newSettings };
      
      // Save to storage
      storageService.set('hcm_settings', updated);
      
      // Apply settings
      applySettings(updated);
      
      return updated;
    });
  }, []);

  const resetSettings = useCallback(() => {
    setSettings(defaultSettings);
    storageService.set('hcm_settings', defaultSettings);
    applySettings(defaultSettings);
  }, []);

  return (
    <SettingsContext.Provider value={{
      settings,
      updateSettings,
      resetSettings,
      exportSettings,
      importSettings
    }}>
      {children}
    </SettingsContext.Provider>
  );
};
```

## Provider Hooks

### Custom Hooks for Each Provider
```typescript
// Theme hook
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within ThemeProvider');
  }
  return context;
};

// Notification hook
export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within NotificationProvider');
  }
  return context;
};

// Loading hook
export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within LoadingProvider');
  }
  return context;
};

// WebSocket hook
export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within WebSocketProvider');
  }
  return context;
};

// Settings hook
export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within SettingsProvider');
  }
  return context;
};
```

## Usage Examples

### Component Integration
```typescript
const ServerDashboard = () => {
  const { success, error } = useNotification();
  const { startLoading, stopLoading } = useLoading();
  const { subscribe } = useWebSocket();
  const { theme, toggleMode } = useTheme();

  useEffect(() => {
    // Subscribe to real-time updates
    const unsubscribe = subscribe('server-status', (data) => {
      success(`Server ${data.name} status updated`);
    });

    return unsubscribe;
  }, [subscribe, success]);

  const handleServerAction = async () => {
    startLoading('server-action', 'Restarting server...');
    
    try {
      await hcmApi.restartServer();
      success('Server restarted successfully');
    } catch (error) {
      error('Failed to restart server');
    } finally {
      stopLoading('server-action');
    }
  };

  return (
    <div className={`dashboard theme-${theme.mode}`}>
      <button onClick={toggleMode}>Toggle Theme</button>
      <button onClick={handleServerAction}>Restart Server</button>
    </div>
  );
};
```

## Related Documentation
- [Authentication Documentation](../auth/README.md)
- [Components Documentation](../components/README.md)
- [Hooks Documentation](../hooks/README.md)
- [H-CareManager Project Documentation](../../docs.md)

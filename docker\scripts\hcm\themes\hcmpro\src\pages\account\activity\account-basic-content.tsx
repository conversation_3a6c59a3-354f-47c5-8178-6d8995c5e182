/**
 * H‑CareCloud Project – Account Activity Content
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { Fragment, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { hcmApi, type ActivityLog } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Engage } from '@/partials/common/engage';
import { toAbsoluteUrl } from '@/lib/helpers';

export function AccountActivityContent() {
  const [activities, setActivities] = useState<ActivityLog[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadActivities();
  }, []);

  const loadActivities = async () => {
    try {
      const data = await hcmApi.getActivityLog();
      setActivities(data);
    } catch (error) {
      toast.error('Failed to load activity data');
      logger.error('Failed to load activity data', {
        component: 'AccountActivity',
        action: 'loadActivities'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getActionType = (action: string) => {
    if (action.includes('login') || action.includes('success')) return 'success';
    if (action.includes('error') || action.includes('failed')) return 'destructive';
    if (action.includes('warning') || action.includes('attempt')) return 'warning';
    return 'secondary';
  };

  return (
    <div className="grid gap-5 lg:gap-7.5">
      <Card>
        <CardHeader>
          <CardTitle>System Activity Log</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="flex items-center gap-4">
                  <Skeleton className="h-4 w-4" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-3 w-24" />
                </div>
              ))}
            </div>
          ) : activities.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">No activity found</p>
          ) : (
            <div className="space-y-4">
              {activities.map((activity) => (
                <div key={activity.id} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium">{activity.action}</div>
                    <div className="text-sm text-muted-foreground">{activity.description}</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {activity.ip_address && `IP: ${activity.ip_address} • `}{formatDate(activity.created_at)}
                    </div>
                  </div>
                  <Badge variant={getActionType(activity.action)}>
                    {activity.action}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid lg:grid-cols-2 gap-5 lg:gap-7.5">
        <Engage
          title="Activity Monitoring"
          description="Your security activities are being monitored and logged for your protection. All actions are recorded with timestamps and IP details."
          image={
            <Fragment>
              <img
                src={toAbsoluteUrl('/media/illustrations/31.svg')}
                className="dark:hidden max-h-[150px]"
                alt="Activity monitoring"
              />
              <img
                src={toAbsoluteUrl('/media/illustrations/31-dark.svg')}
                className="light:hidden max-h-[150px]"
                alt="Activity monitoring"
              />
            </Fragment>
          }
          more={{
            title: 'View Security Settings',
            url: '/account/security/overview',
          }}
        />
        <Engage
          title="Security Tips"
          description="For your security, regularly review your activity log. Report any suspicious activities to your system administrator immediately."
          image={
            <Fragment>
              <img
                src={toAbsoluteUrl('/media/illustrations/29.svg')}
                className="dark:hidden max-h-[150px]"
                alt="Security tips"
              />
              <img
                src={toAbsoluteUrl('/media/illustrations/29-dark.svg')}
                className="light:hidden max-h-[150px]"
                alt="Security tips"
              />
            </Fragment>
          }
          more={{
            title: 'View API Keys',
            url: '/account/api-keys',
          }}
        />
      </div>
    </div>
  );
}

/**
 * H‑CareCloud Project – Server Administrators Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { useMemo, useState, useEffect } from 'react';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import {
  ColumnDef,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  PaginationState,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import {
  EllipsisVertical,
  Filter,
  Search,
  UserRoundPlus,
} from 'lucide-react';
import { toast } from 'sonner';
import { toAbsoluteUrl } from '@/lib/helpers';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardFooter,
  CardHeader,
  CardHeading,
  CardTable,
  CardToolbar,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Skeleton } from '@/components/ui/skeleton';
import { DataGrid, useDataGrid } from '@/components/ui/data-grid';
import { DataGridColumnHeader } from '@/components/ui/data-grid-column-header';
import { DataGridPagination } from '@/components/ui/data-grid-pagination';
import {
  DataGridTable,
  DataGridTableRowSelect,
  DataGridTableRowSelectAll,
} from '@/components/ui/data-grid-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface IServerAdmin {
  avatar: string;
  name: string;
  tasks: string;
}

interface IServerLocation {
  name: string;
  flag: string;
}

interface IAdminStatus {
  label: string;
  variant:
    | 'secondary'
    | 'primary'
    | 'destructive'
    | 'success'
    | 'info'
    | 'mono'
    | 'warning'
    | null
    | undefined;
}

interface IServerAdminData {
  id: string;
  admin: IServerAdmin;
  roles: string[];
  location: IServerLocation;
  status: IAdminStatus;
  recentlyActivity: string;
}

// Status options for filtering
const statusOptions = [
  { label: 'Active', value: 'Active' },
  { label: 'Inactive', value: 'Inactive' },
  { label: 'Suspended', value: 'Suspended' },
  { label: 'Pending', value: 'Pending' },
];

const ServerAdmins = () => {
  const [serverAdmins, setServerAdmins] = useState<IServerAdminData[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'recentlyActivity', desc: true },
  ]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);

  useEffect(() => {
    loadServerAdmins();
  }, []);

  const loadServerAdmins = async () => {
    try {
      const admins = await hcmApi.getServerAdmins();
      // Transform API data to match component interface
      const transformedAdmins: IServerAdminData[] = admins.map((admin: any) => ({
        id: admin.id.toString(),
        admin: {
          avatar: admin.avatar || `300-${(admin.id % 34) + 1}.png`,
          name: admin.name,
          tasks: admin.task_count?.toString() || '0',
        },
        roles: admin.roles ? admin.roles.split(',') : [admin.role || 'No Role'],
        location: {
          name: admin.location || 'Server Location',
          flag: 'flag.svg', // Default flag
        },
        status: {
          label: admin.status === 'active' ? 'Active' : admin.status === 'inactive' ? 'Inactive' : 'Suspended',
          variant: admin.status === 'active' ? 'success' : admin.status === 'inactive' ? 'secondary' : 'warning',
        },
        recentlyActivity: admin.last_login_at ?
          `Last login: ${new Date(admin.last_login_at).toLocaleDateString()}` :
          'Never logged in',
      }));
      setServerAdmins(transformedAdmins);
    } catch (error) {
      toast.error('Failed to load server administrators');
      logger.error('Failed to load server administrators', {
        component: 'ServerAdmins',
        action: 'loadServerAdmins'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const filteredData = useMemo(() => {
    return staffMembers.filter((item) => {
      // Filter by status
      const matchesStatus =
        !selectedStatuses?.length ||
        selectedStatuses.includes(item.status.label);

      // Filter by search query
      const matchesSearch =
        !searchQuery ||
        item.member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.roles.some(role => role.toLowerCase().includes(searchQuery.toLowerCase()));

      return matchesStatus && matchesSearch;
    });
  }, [staffMembers, selectedStatuses, searchQuery]);

  const handleToggleStatus = (value: string, checked: boolean) => {
    setSelectedStatuses((prev) =>
      checked ? [...prev, value] : prev.filter((v) => v !== value),
    );
  };

  const columns = useMemo<ColumnDef<IData>[]>(() => [
    {
      id: 'select',
      header: ({ table }) => (
        <DataGridTableRowSelectAll
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value: boolean) => table.toggleAllPageRowsSelected(value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <DataGridTableRowSelect
          checked={row.getIsSelected()}
          onCheckedChange={(value: boolean) => row.toggleSelected(value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'member',
      header: ({ column }) => (
        <DataGridColumnHeader column={column} title="Member" />
      ),
      cell: ({ row }) => {
        const member = row.getValue('member') as IMember;
        return (
          <div className="flex items-center gap-3">
            <img
              src={toAbsoluteUrl(`/media/avatars/${member.avatar}`)}
              className="size-9 rounded-full"
              alt={member.name}
            />
            <div className="flex flex-col">
              <span className="font-medium text-sm">{member.name}</span>
              <span className="text-xs text-muted-foreground">{member.tasks} tasks</span>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'roles',
      header: ({ column }) => (
        <DataGridColumnHeader column={column} title="Roles" />
      ),
      cell: ({ row }) => {
        const roles = row.getValue('roles') as string[];
        return (
          <div className="flex flex-wrap gap-1">
            {roles.slice(0, 2).map((role, index) => (
              <Badge key={index} variant="secondary" size="sm">
                {role}
              </Badge>
            ))}
            {roles.length > 2 && (
              <Badge variant="secondary" size="sm">
                +{roles.length - 2}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'location',
      header: ({ column }) => (
        <DataGridColumnHeader column={column} title="Location" />
      ),
      cell: ({ row }) => {
        const location = row.getValue('location') as ILocation;
        return (
          <div className="flex items-center gap-2">
            <img
              src={toAbsoluteUrl(`/media/flags/${location.flag}`)}
              className="h-4 rounded"
              alt={location.name}
            />
            <span className="text-sm">{location.name}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataGridColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as IStatus;
        return (
          <Badge variant={status.variant} size="sm">
            {status.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'recentlyActivity',
      header: ({ column }) => (
        <DataGridColumnHeader column={column} title="Recent Activity" />
      ),
      cell: ({ row }) => (
        <span className="text-sm text-muted-foreground">
          {row.getValue('recentlyActivity')}
        </span>
      ),
    },
    {
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        const member = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" mode="icon">
                <EllipsisVertical size={16} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => console.log('View details for:', member.member.name)}>
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => console.log('Edit member:', member.member.name)}>
                Edit Member
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className="text-destructive"
                onClick={() => console.log('Remove member:', member.member.name)}
              >
                Remove Member
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], []);

  const table = useReactTable({
    data: filteredData,
    columns,
    pageCount: Math.ceil((filteredData?.length || 0) / pagination.pageSize),
    state: {
      sorting,
      pagination,
    },
    enableRowSelection: true,
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    manualPagination: false,
    manualSorting: false,
    manualFiltering: false,
  });

  const dataGrid = useDataGrid({
    table,
    pagination,
    sorting,
    setPagination,
    setSorting,
  });

  if (loading) {
    return (
      <Card className="min-w-full">
        <CardHeader>
          <CardHeading>Staff Members</CardHeading>
          <CardToolbar>
            <Skeleton className="h-9 w-64" />
            <Skeleton className="h-9 w-32" />
          </CardToolbar>
        </CardHeader>
        <CardTable>
          <DataGridTable table={table}>
            {[...Array(5)].map((_, i) => (
              <tr key={i}>
                <td className="p-4">
                  <Skeleton className="h-4 w-4" />
                </td>
                <td className="p-4">
                  <div className="flex items-center gap-3">
                    <Skeleton className="size-9 rounded-full" />
                    <div className="flex flex-col gap-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                </td>
                <td className="p-4">
                  <Skeleton className="h-6 w-20" />
                </td>
                <td className="p-4">
                  <Skeleton className="h-4 w-24" />
                </td>
                <td className="p-4">
                  <Skeleton className="h-6 w-16" />
                </td>
                <td className="p-4">
                  <Skeleton className="h-4 w-20" />
                </td>
                <td className="p-4">
                  <Skeleton className="h-6 w-6" />
                </td>
              </tr>
            ))}
          </DataGridTable>
        </CardTable>
      </Card>
    );
  }

  return (
    <Card className="min-w-full">
      <CardHeader>
        <CardHeading>Staff Members</CardHeading>
        <CardToolbar>
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search members..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 w-64"
              />
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter size={16} />
                  Filter
                  {selectedStatuses.length > 0 && (
                    <Badge variant="secondary" size="sm" className="ml-2">
                      {selectedStatuses.length}
                    </Badge>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-64" align="end">
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Status</Label>
                  <div className="space-y-2">
                    {statusOptions.map((status) => (
                      <div key={status.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={status.value}
                          checked={selectedStatuses.includes(status.value)}
                          onCheckedChange={(checked) =>
                            handleToggleStatus(status.value, !!checked)
                          }
                        />
                        <Label htmlFor={status.value} className="text-sm">
                          {status.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                  {selectedStatuses.length > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedStatuses([])}
                      className="w-full"
                    >
                      Clear Filters
                    </Button>
                  )}
                </div>
              </PopoverContent>
            </Popover>
            <Button size="sm">
              <UserRoundPlus size={16} />
              Add Member
            </Button>
          </div>
        </CardToolbar>
      </CardHeader>
      <CardTable>
        <DataGrid dataGrid={dataGrid} table={table} />
      </CardTable>
      <CardFooter>
        <DataGridPagination dataGrid={dataGrid} recordCount={filteredData?.length || 0} />
      </CardFooter>
    </Card>
  );
};

export { Members };
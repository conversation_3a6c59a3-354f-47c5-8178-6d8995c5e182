/**
 * H‑CareCloud Project – HCM API Service
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { logger } from '@/lib/logger';

// API Response Types
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: Record<string, string[]>;
}

interface UserProfile {
  id: number;
  user_id: number;
  first_name: string | null;
  last_name: string | null;
  avatar: string | null;
  bio: string | null;
  company: string | null;
  website: string | null;
  location: string | null;
  timezone: string;
  language: string;
  date_of_birth: string | null;
  created_at: string;
  updated_at: string;
}

interface ApiToken {
  id: number;
  user_id: number;
  name: string;
  token: string;
  abilities: string[] | null;
  last_used_at: string | null;
  status: 'active' | 'inactive' | 'revoked';
  created_at: string;
  updated_at: string;
  expires_at: string | null;
}

interface Notification {
  id: number;
  user_id: number;
  type: string;
  title: string;
  message: string;
  data: Record<string, any> | null;
  read_at: string | null;
  created_at: string;
}

interface ActivityLog {
  id: number;
  user_id: number;
  action: string;
  description: string;
  ip_address: string | null;
  user_agent: string | null;
  created_at: string;
}

interface BillingPlan {
  id: number;
  name: string;
  description: string | null;
  price: number;
  billing_cycle: 'monthly' | 'yearly';
  features: string[] | null;
  max_users: number | null;
  max_storage: number | null;
  status: 'active' | 'inactive';
}

interface UserSubscription {
  id: number;
  user_id: number;
  plan_id: number;
  status: 'active' | 'cancelled' | 'expired' | 'trial';
  trial_ends_at: string | null;
  current_period_start: string | null;
  current_period_end: string | null;
  cancelled_at: string | null;
  created_at: string;
  updated_at: string;
  plan: BillingPlan;
}

interface BillingHistory {
  id: number;
  user_id: number;
  subscription_id: number;
  amount: number;
  currency: string;
  status: 'pending' | 'paid' | 'failed' | 'refunded';
  invoice_number: string | null;
  payment_method: string | null;
  transaction_id: string | null;
  billing_date: string;
  due_date: string | null;
  paid_at: string | null;
  created_at: string;
}

interface NotificationChannel {
  id: string;
  user_id: number;
  title: string;
  description: string | null;
  enabled: boolean;
  connected: boolean;
  endpoint: string | null;
  created_at: string;
  updated_at: string;
}

interface NotificationChannelsResponse {
  channels: NotificationChannel[];
  team_alerts_enabled: boolean;
}

interface NotificationSettings {
  team_alerts_enabled?: boolean;
  email_notifications?: boolean;
  browser_notifications?: boolean;
  sound_notifications?: boolean;
  notification_frequency?: 'immediate' | 'hourly' | 'daily' | 'weekly';
}

class HcmApiService {
  private baseUrl: string;
  private tenantId: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_APP_API_URL;
    this.tenantId = import.meta.env.VITE_TENANT_ID;
    
    if (!this.baseUrl || !this.tenantId) {
      throw new Error('HCM API configuration missing');
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const token = localStorage.getItem('hcm_auth_token');

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-Tenant-ID': this.tenantId,
      'X-Request-ID': crypto.randomUUID(),
      ...(options.headers as Record<string, string> || {}),
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...options,
        headers,
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }

      return data;
    } catch (error) {
      logger.error('API request failed', {
        component: 'HcmApi',
        action: 'request',
        data: { endpoint, options }
      }, error as Error);
      throw error;
    }
  }

  // User Profile API
  async getUserProfile(): Promise<UserProfile | null> {
    const response = await this.request<UserProfile>('/api/user/profile');
    return response.data || null;
  }

  async updateUserProfile(profile: Partial<UserProfile>): Promise<UserProfile> {
    const response = await this.request<UserProfile>('/api/user/profile', {
      method: 'PUT',
      body: JSON.stringify(profile),
    });
    return response.data!;
  }

  async uploadAvatar(file: File): Promise<{ avatar_url: string }> {
    const formData = new FormData();
    formData.append('avatar', file);

    const response = await this.request<{ avatar_url: string }>('/api/user/avatar', {
      method: 'POST',
      body: formData,
      headers: {}, // Let browser set Content-Type for FormData
    });
    return response.data!;
  }

  // API Tokens
  async getApiTokens(): Promise<ApiToken[]> {
    const response = await this.request<{ api_keys: ApiToken[] }>('/api/user/api-keys');
    return response.data?.api_keys || [];
  }

  async createApiToken(name: string, abilities: string[] = []): Promise<ApiToken> {
    const response = await this.request<{ api_key: ApiToken }>('/api/user/api-keys', {
      method: 'POST',
      body: JSON.stringify({ name, abilities }),
    });
    return response.data!.api_key;
  }

  async updateApiToken(token: string, updates: { name?: string; abilities?: string[]; status?: string }): Promise<void> {
    await this.request(`/api/user/api-keys/${token}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async revokeApiToken(token: string): Promise<void> {
    await this.request(`/api/user/api-keys/${token}`, {
      method: 'DELETE',
    });
  }

  async regenerateApiToken(token: string): Promise<ApiToken> {
    const response = await this.request<{ token: string }>(`/api/user/api-keys/regenerate/${token}`, {
      method: 'POST',
    });
    // Return a minimal ApiToken object since we only get the new token back
    return { token: response.data!.token } as ApiToken;
  }

  // Notifications
  async getNotifications(): Promise<Notification[]> {
    const response = await this.request<Notification[]>('/api/user/notifications');
    return response.data || [];
  }

  async markNotificationRead(notificationId: number): Promise<void> {
    await this.request(`/api/user/notifications/${notificationId}/read`, {
      method: 'POST',
    });
  }

  async markAllNotificationsRead(): Promise<void> {
    await this.request('/api/user/notifications/read-all', {
      method: 'POST',
    });
  }

  // Notification Channels
  async getNotificationChannels(): Promise<NotificationChannelsResponse> {
    const response = await this.request<NotificationChannelsResponse>('/api/user/notifications/channels');
    return response.data || { channels: [], team_alerts_enabled: false };
  }

  async updateNotificationChannel(channelId: string, updates: { enabled: boolean }): Promise<void> {
    await this.request(`/api/user/notifications/channels/${channelId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async updateNotificationSettings(settings: NotificationSettings): Promise<void> {
    await this.request('/api/user/notifications/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  // Activity Log
  async getActivityLog(): Promise<ActivityLog[]> {
    const response = await this.request<ActivityLog[]>('/api/user/activity');
    return response.data || [];
  }

  // Billing
  async getBillingPlans(): Promise<BillingPlan[]> {
    const response = await this.request<{ plans: BillingPlan[] }>('/api/user/billing/plans');
    return response.data?.plans || [];
  }

  async getUserSubscription(): Promise<UserSubscription | null> {
    const response = await this.request<{ subscription: UserSubscription }>('/api/user/billing/subscription');
    return response.data?.subscription || null;
  }

  async subscribeToPlan(planId: number): Promise<void> {
    await this.request('/api/user/billing/subscribe', {
      method: 'POST',
      body: JSON.stringify({ plan_id: planId }),
    });
  }

  async getBillingHistory(): Promise<BillingHistory[]> {
    const response = await this.request<{ history: BillingHistory[] }>('/api/user/billing/history');
    return response.data?.history || [];
  }

  async cancelSubscription(): Promise<void> {
    await this.request('/api/user/billing/cancel', {
      method: 'POST',
    });
  }

  // Settings
  async getSettings(): Promise<Record<string, any>> {
    const response = await this.request<Record<string, any>>('/api/user/settings');
    return response.data || {};
  }

  async updateSettings(settings: Record<string, any>): Promise<void> {
    await this.request('/api/user/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  // Security API
  async getSecurityOverview(): Promise<any> {
    const response = await this.request('/api/user/security/overview');
    return response.data || {};
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    await this.request('/api/user/security/change-password', {
      method: 'POST',
      body: JSON.stringify({ current_password: currentPassword, new_password: newPassword }),
    });
  }

  async getUserSessions(): Promise<any[]> {
    const response = await this.request<any[]>('/api/user/security/sessions');
    return response.data || [];
  }

  async terminateSession(sessionId: string): Promise<void> {
    await this.request(`/api/user/security/sessions/${sessionId}`, {
      method: 'DELETE',
    });
  }

  async getSecuritySettings(): Promise<any> {
    const response = await this.request('/api/user/security/settings');
    return response.data || {};
  }

  async updateSecuritySettings(settings: any): Promise<void> {
    await this.request('/api/user/security/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  async getAuditLog(): Promise<any[]> {
    const response = await this.request<any[]>('/api/user/security/audit-log');
    return response.data || [];
  }

  // Staff Management API
  async getStaffMembers(): Promise<any[]> {
    const response = await this.request<any[]>('/api/user/staffs/members');
    return response.data || [];
  }

  async getStaffOverview(): Promise<any> {
    const response = await this.request('/api/user/staffs/overview');
    return response.data || {};
  }

  async getDepartments(): Promise<any[]> {
    const response = await this.request<any[]>('/api/user/staffs/departments');
    return response.data || [];
  }

  async getRoles(): Promise<any[]> {
    const response = await this.request<any[]>('/api/user/staffs/roles');
    return response.data || [];
  }

  async createStaffMember(memberData: any): Promise<any> {
    const response = await this.request('/api/user/staffs/members', {
      method: 'POST',
      body: JSON.stringify(memberData),
    });
    return response.data;
  }

  async updateStaffMember(memberId: number, memberData: any): Promise<any> {
    const response = await this.request(`/api/user/staffs/members/${memberId}`, {
      method: 'PUT',
      body: JSON.stringify(memberData),
    });
    return response.data;
  }

  async deleteStaffMember(memberId: number): Promise<void> {
    await this.request(`/api/user/staffs/members/${memberId}`, {
      method: 'DELETE',
    });
  }

  // Security - Allowed IP Addresses API
  async getAllowedIPAddresses(): Promise<any[]> {
    const response = await this.request<any[]>('/api/user/security/allowed-ips');
    return response.data || [];
  }

  async addAllowedIPAddress(ipData: any): Promise<any> {
    const response = await this.request('/api/user/security/allowed-ips', {
      method: 'POST',
      body: JSON.stringify(ipData),
    });
    return response.data;
  }

  async updateAllowedIPAddress(ipId: string, ipData: any): Promise<any> {
    const response = await this.request(`/api/user/security/allowed-ips/${ipId}`, {
      method: 'PUT',
      body: JSON.stringify(ipData),
    });
    return response.data;
  }

  async deleteAllowedIPAddress(ipId: string): Promise<void> {
    await this.request(`/api/user/security/allowed-ips/${ipId}`, {
      method: 'DELETE',
    });
  }

  // Billing Details API
  async getBillingDetails(): Promise<any> {
    const response = await this.request('/api/user/billing/details');
    return response.data || {};
  }

  // Payment Methods API
  async getPaymentMethods(): Promise<any[]> {
    const response = await this.request<any[]>('/api/user/billing/payment-methods');
    return response.data || [];
  }

  async addPaymentMethod(methodData: any): Promise<any> {
    const response = await this.request('/api/user/billing/payment-methods', {
      method: 'POST',
      body: JSON.stringify(methodData),
    });
    return response.data;
  }

  async deletePaymentMethod(methodId: string): Promise<void> {
    await this.request(`/api/user/billing/payment-methods/${methodId}`, {
      method: 'DELETE',
    });
  }

  // Staff Connections API (for user profile)
  async getStaffConnections(): Promise<any[]> {
    const response = await this.request<any[]>('/api/user/profile/connections');
    return response.data || [];
  }

  async updateStaffConnection(connectionId: string, updates: any): Promise<any> {
    const response = await this.request(`/api/user/profile/connections/${connectionId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
    return response.data;
  }

  // Permissions API (for server access control)
  async getPermissions(): Promise<any> {
    const response = await this.request('/api/user/staffs/permissions');
    return response.data || {};
  }

  async updatePermissions(permissions: any): Promise<void> {
    await this.request('/api/user/staffs/permissions', {
      method: 'PUT',
      body: JSON.stringify(permissions),
    });
  }

  // Two-Factor Authentication API
  async verifyTwoFactorCode(code: string): Promise<any> {
    const response = await this.request('/api/auth/verify-2fa', {
      method: 'POST',
      body: JSON.stringify({ code }),
    });
    return response.data;
  }

  async resendTwoFactorCode(): Promise<void> {
    await this.request('/api/auth/resend-2fa', {
      method: 'POST',
    });
  }

  async enableTwoFactor(): Promise<any> {
    const response = await this.request('/api/auth/enable-2fa', {
      method: 'POST',
    });
    return response.data;
  }

  async disableTwoFactor(): Promise<void> {
    await this.request('/api/auth/disable-2fa', {
      method: 'POST',
    });
  }

  // Email Verification API
  async resendVerificationEmail(email: string): Promise<void> {
    await this.request('/api/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async requestPasswordReset(email: string): Promise<void> {
    await this.request('/api/auth/request-password-reset', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  // Appearance Settings API
  async getAppearanceSettings(): Promise<any> {
    const response = await this.request('/api/user/appearance/settings');
    return response.data || {};
  }

  async updateAppearanceSettings(settings: any): Promise<void> {
    await this.request('/api/user/appearance/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  async getThemeSettings(): Promise<any> {
    const response = await this.request('/api/system/theme/settings');
    return response.data || {};
  }

  async updateThemeSettings(settings: any): Promise<void> {
    await this.request('/api/system/theme/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  // Webhooks API
  async getWebhooks(): Promise<any[]> {
    const response = await this.request<any[]>('/api/user/webhooks');
    return response.data || [];
  }

  async createWebhook(webhookData: any): Promise<any> {
    const response = await this.request('/api/user/webhooks', {
      method: 'POST',
      body: JSON.stringify(webhookData),
    });
    return response.data;
  }

  async updateWebhook(webhookId: string, webhookData: any): Promise<any> {
    const response = await this.request(`/api/user/webhooks/${webhookId}`, {
      method: 'PUT',
      body: JSON.stringify(webhookData),
    });
    return response.data;
  }

  async deleteWebhook(webhookId: string): Promise<void> {
    await this.request(`/api/user/webhooks/${webhookId}`, {
      method: 'DELETE',
    });
  }

  async testWebhook(webhookId: string): Promise<any> {
    const response = await this.request(`/api/user/webhooks/${webhookId}/test`, {
      method: 'POST',
    });
    return response.data;
  }

  async uploadLogo(file: File): Promise<any> {
    const formData = new FormData();
    formData.append('logo', file);

    const response = await this.request('/api/user/appearance/upload-logo', {
      method: 'POST',
      body: formData,
    });
    return response.data;
  }

  async getInvoices(): Promise<any[]> {
    const response = await this.request<any[]>('/api/user/billing/invoices');
    return response.data || [];
  }

  async downloadInvoice(invoiceId: string): Promise<Blob> {
    const response = await this.request(`/api/user/billing/invoices/${invoiceId}/download`, {
      method: 'GET',
    });
    return response as unknown as Blob;
  }

}

export const hcmApi = new HcmApiService();
export type {
  UserProfile,
  ApiToken,
  Notification,
  ActivityLog,
  BillingPlan,
  UserSubscription,
  BillingHistory,
  NotificationChannel,
  NotificationChannelsResponse,
  NotificationSettings
};

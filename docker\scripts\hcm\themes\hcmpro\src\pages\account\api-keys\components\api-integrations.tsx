/**
 * H‑CareCloud Project – API Integrations Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { useEffect, useMemo, useState } from 'react';
import {
  Column,
  ColumnDef,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  PaginationState,
  RowSelectionState,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { Copy, Settings2, Plus, Trash2, Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardFooter,
  CardHeader,
  CardTable,
  CardTitle,
  CardToolbar,
} from '@/components/ui/card';
import { DataGrid, useDataGrid } from '@/components/ui/data-grid';
import { DataGridColumnHeader } from '@/components/ui/data-grid-column-header';
import { DataGridColumnVisibility } from '@/components/ui/data-grid-column-visibility';
import { DataGridPagination } from '@/components/ui/data-grid-pagination';
import {
  DataGridTable,
  DataGridTableRowSelect,
  DataGridTableRowSelectAll,
} from '@/components/ui/data-grid-table';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface IColumnFilterProps<TData, TValue> {
  column: Column<TData, TValue>;
}

interface IData {
  id: number;
  name: string;
  token: string;
  abilities: string[] | null;
  last_used_at: string | null;
  status: 'active' | 'inactive' | 'revoked';
  created_at: string;
  expires_at: string | null;
}

const ApiIntegrations = () => {
  const [tokens, setTokens] = useState<IData[]>([]);
  const [, setLoading] = useState(true);
  const [newTokenName, setNewTokenName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [showTokens, setShowTokens] = useState<Record<number, boolean>>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'name', desc: false },
  ]);

  useEffect(() => {
    loadTokens();
  }, []);

  const loadTokens = async () => {
    try {
      const apiTokens = await hcmApi.getApiTokens();
      setTokens(apiTokens);
    } catch (error) {
      toast.error('Failed to load API tokens');
      logger.error('Failed to load API tokens', {
        component: 'ApiIntegrations',
        action: 'loadTokens'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateToken = async () => {
    if (!newTokenName.trim()) return;

    try {
      setIsCreating(true);
      await hcmApi.createApiToken(newTokenName.trim(), ['server:read', 'server:write']);
      setNewTokenName('');
      await loadTokens(); // Refresh the list
      toast.success('API token created successfully');
    } catch (error) {
      toast.error('Failed to create API token');
      logger.error('Failed to create API token', {
        component: 'ApiIntegrations',
        action: 'createToken',
        data: { name: newTokenName, abilities: newTokenAbilities }
      }, error as Error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleRevokeToken = async (token: string) => {
    try {
      await hcmApi.revokeApiToken(token);
      await loadTokens(); // Refresh the list
      toast.success('API token revoked successfully');
    } catch (error) {
      toast.error('Failed to revoke API token');
      logger.error('Failed to revoke API token', {
        component: 'ApiIntegrations',
        action: 'revokeToken',
        data: { token }
      }, error as Error);
    }
  };

  const toggleTokenVisibility = (tokenId: number) => {
    setShowTokens(prev => ({
      ...prev,
      [tokenId]: !prev[tokenId]
    }));
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Token copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy token');
      logger.error('Failed to copy token', {
        component: 'ApiIntegrations',
        action: 'copyToClipboard'
      }, error as Error);
    }
  };

  const maskToken = (token: string) => {
    if (!token || token.length < 16) return token;
    return `${token.substring(0, 8)}${'*'.repeat(32)}${token.substring(token.length - 8)}`;
  };

  const ColumnInputFilter = <TData, TValue>({
    column,
  }: IColumnFilterProps<TData, TValue>) => {
    return (
      <Input
        placeholder="Filter..."
        value={(column.getFilterValue() as string) ?? ''}
        onChange={(event) => column.setFilterValue(event.target.value)}
        className="h-9 w-full max-w-40"
      />
    );
  };

  const columns = useMemo<ColumnDef<IData>[]>(
    () => [
      {
        accessorKey: 'id',
        accessorFn: (row) => row.id,
        header: () => <DataGridTableRowSelectAll />,
        cell: ({ row }) => <DataGridTableRowSelect row={row} />,
        enableSorting: false,
        enableHiding: false,
        enableResizing: false,
        size: 51,
        meta: {
          cellClassName: '',
        },
      },
      {
        id: 'name',
        accessorFn: (row) => row.name,
        header: ({ column }) => (
          <DataGridColumnHeader
            title="Token Name"
            filter={<ColumnInputFilter column={column} />}
            column={column}
          />
        ),
        cell: (info) => info.getValue(),
        enableSorting: true,
        size: 200,
        meta: {
          cellClassName: '',
        },
      },
      {
        id: 'token',
        accessorFn: (row) => row.token,
        header: ({ column }) => (
          <DataGridColumnHeader title="API Token" column={column} />
        ),
        cell: (info) => (
          <div className="flex items-center text-foreground font-normal gap-2">
            <span className="font-mono text-sm">
              {showTokens[info.row.original.id]
                ? info.row.original.token
                : maskToken(info.row.original.token)
              }
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleTokenVisibility(info.row.original.id)}
              className="text-muted-foreground hover:text-primary-active"
            >
              {showTokens[info.row.original.id] ? <EyeOff size={16} /> : <Eye size={16} />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(info.row.original.token)}
              className="text-muted-foreground hover:text-primary-active"
            >
              <Copy size={16} />
            </Button>
          </div>
        ),
        enableSorting: false,
        size: 300,
        meta: {
          cellClassName: '',
        },
      },
      {
        id: 'last_used_at',
        accessorFn: (row) => row.last_used_at,
        header: ({ column }) => (
          <DataGridColumnHeader title="Last Used" column={column} />
        ),
        cell: (info) => {
          const lastUsed = info.getValue() as string | null;
          return lastUsed ? new Date(lastUsed).toLocaleDateString() : 'Never';
        },
        enableSorting: true,
        size: 130,
        meta: {
          cellClassName: '',
        },
      },
      {
        id: 'status',
        accessorFn: (row) => row.status,
        header: ({ column }) => (
          <DataGridColumnHeader title="Status" column={column} />
        ),
        cell: (info) => {
          const status = info.getValue() as string;
          return (
            <Badge
              variant={status === 'active' ? 'success' : status === 'revoked' ? 'destructive' : 'secondary'}
            >
              {status}
            </Badge>
          );
        },
        enableSorting: true,
        size: 100,
        meta: {
          cellClassName: '',
        },
      },
      {
        id: 'actions',
        header: () => '',
        cell: ({ row }) => (
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleRevokeToken(row.original.token)}
              className="text-red-500 hover:text-red-700"
            >
              <Trash2 size={16} />
            </Button>
          </div>
        ),
        enableSorting: false,
        size: 70,
        meta: {
          headerClassName: '',
        },
      },
    ],
    [showTokens],
  );

  const filteredData: IData[] = useMemo(() => tokens, [tokens]);

  useEffect(() => {
    const selectedRowIds = Object.keys(rowSelection);

    if (selectedRowIds.length > 0) {
      toast(`Total ${selectedRowIds.length} are selected.`, {
        description: `Selected row IDs: ${selectedRowIds}`,
        action: {
          label: 'Undo',
          onClick: () => {
            logger.debug('Selection undo requested', {
              component: 'ApiIntegrations',
              action: 'undoSelection',
              data: { selectedRowIds }
            });
            setRowSelection({});
          },
        },
      });
    }
  }, [rowSelection]);

  const table = useReactTable({
    columns,
    data: filteredData,
    pageCount: Math.ceil((filteredData?.length || 0) / pagination.pageSize),
    getRowId: (row: IData) => row.id.toString(),
    state: {
      pagination,
      sorting,
      rowSelection,
    },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  const Toolbar = () => {
    const { table } = useDataGrid();

    return (
      <CardToolbar>
        <div className="flex flex-wrap items-center gap-2.5">
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <Plus size={16} />
                Create API Token
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New API Token</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="token-name">Token Name</Label>
                  <Input
                    id="token-name"
                    value={newTokenName}
                    onChange={(e) => setNewTokenName(e.target.value)}
                    placeholder="Enter token name"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={handleCreateToken}
                    disabled={isCreating || !newTokenName.trim()}
                  >
                    {isCreating ? 'Creating...' : 'Create Token'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          <DataGridColumnVisibility
            table={table}
            trigger={
              <Button variant="outline">
                <Settings2 />
                Columns
              </Button>
            }
          />
        </div>
      </CardToolbar>
    );
  };

  return (
    <DataGrid
      table={table}
      recordCount={filteredData?.length || 0}
      tableLayout={{
        columnsPinnable: true,
        columnsMovable: true,
        columnsVisibility: true,
        cellBorder: true,
      }}
    >
      <Card>
        <CardHeader>
          <CardTitle>H-CareManager API Tokens</CardTitle>
          <Toolbar />
        </CardHeader>
        <CardTable>
          <ScrollArea>
            <DataGridTable />
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </CardTable>
        <CardFooter>
          <DataGridPagination />
        </CardFooter>
      </Card>
    </DataGrid>
  );
};

export { ApiIntegrations };

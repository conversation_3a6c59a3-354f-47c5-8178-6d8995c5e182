# H-CareCloud phpMyAdmin Configuration

## Overview
This directory contains the phpMyAdmin configuration for H-CareCloud Hospital Management System database administration. phpMyAdmin provides a web-based interface for managing MySQL databases, including the main HMS database and H-CareManager database, with HIPAA-compliant security measures.

## Architecture

### Configuration Structure
```
/docker/phpmyadmin/
├── config.user.inc.php     # Custom phpMyAdmin configuration
└── [Runtime directories]
    ├── tmp/                # Temporary files and uploads
    ├── sessions/           # User session storage
    └── logs/               # Access and error logs
```

### Database Access
- **hcarecloud_manager**: H-CareManager authentication and settings
- **${MYSQL_DATABASE}**: Main H-CareCloud HMS patient data
- **phpmyadmin**: phpMyAdmin configuration storage

## Configuration

### config.user.inc.php
```php
<?php
/**
 * H‑CareCloud phpMyAdmin Configuration
 * HIPAA-compliant database administration interface
 */

// Security settings
$cfg['LoginCookieValidity'] = 3600; // 1 hour session timeout
$cfg['LoginCookieStore'] = 0; // Don't remember login
$cfg['LoginCookieDeleteAll'] = true;

// Server configuration
$cfg['Servers'][1]['host'] = 'mysql';
$cfg['Servers'][1]['port'] = '3306';
$cfg['Servers'][1]['auth_type'] = 'cookie';
$cfg['Servers'][1]['user'] = '';
$cfg['Servers'][1]['password'] = '';
$cfg['Servers'][1]['AllowNoPassword'] = false;

// Security restrictions
$cfg['Servers'][1]['AllowDeny']['order'] = 'deny,allow';
$cfg['Servers'][1]['AllowDeny']['rules'] = array(
    'allow root from localhost',
    'allow hcarecloud_user from %',
    'deny % from all'
);

// HIPAA compliance settings
$cfg['ForceSSL'] = true;
$cfg['CheckConfigurationPermissions'] = true;
$cfg['ProtectBinary'] = 'blob';
$cfg['ShowPhpInfo'] = false;
$cfg['ShowServerInfo'] = false;
$cfg['ShowDbStructureCreation'] = false;
$cfg['ShowDbStructureLastUpdate'] = false;
$cfg['ShowDbStructureLastCheck'] = false;

// Upload and import restrictions
$cfg['UploadDir'] = '/tmp/phpmyadmin/uploads/';
$cfg['SaveDir'] = '/tmp/phpmyadmin/save/';
$cfg['MaxSizeForInputField'] = 50 * 1024 * 1024; // 50MB
$cfg['ExecTimeLimit'] = 300; // 5 minutes

// Logging for audit trails
$cfg['QueryHistoryDB'] = true;
$cfg['QueryHistoryMax'] = 1000;

// Theme and interface
$cfg['ThemeDefault'] = 'pmahomme';
$cfg['DefaultTabServer'] = 'main.php';
$cfg['DefaultTabDatabase'] = 'structure.php';
$cfg['DefaultTabTable'] = 'browse.php';

// Export settings
$cfg['Export']['method'] = 'quick';
$cfg['Export']['format'] = 'sql';
$cfg['Export']['compression'] = 'gzip';
$cfg['Export']['charset'] = 'utf-8';

// Import settings
$cfg['Import']['charset'] = 'utf-8';
$cfg['Import']['allow_interrupt'] = true;
$cfg['Import']['skip_queries'] = 0;

// Disable dangerous operations in production
if (getenv('ENVIRONMENT') === 'production') {
    $cfg['ShowCreateDb'] = false;
    $cfg['AllowUserDropDatabase'] = false;
    $cfg['Confirm'] = true;
}
?>
```

## Security Configuration

### HIPAA Compliance
```php
// Audit logging
$cfg['QueryHistoryDB'] = true;
$cfg['QueryHistoryMax'] = 1000;

// Session security
$cfg['LoginCookieValidity'] = 3600;
$cfg['LoginCookieStore'] = 0;
$cfg['SessionSavePath'] = '/tmp/phpmyadmin/sessions/';

// Access restrictions
$cfg['Servers'][1]['AllowDeny']['order'] = 'deny,allow';
$cfg['Servers'][1]['AllowDeny']['rules'] = array(
    'allow hcarecloud_user from %',
    'deny % from all'
);

// SSL enforcement
$cfg['ForceSSL'] = true;
$cfg['PmaAbsoluteUri'] = 'https://phpmyadmin.hcarecloud.local/';
```

### User Access Control
```php
// Role-based access
$cfg['Servers'][1]['users']['admin'] = array(
    'password' => '',
    'privileges' => array('ALL PRIVILEGES')
);

$cfg['Servers'][1]['users']['developer'] = array(
    'password' => '',
    'privileges' => array('SELECT', 'INSERT', 'UPDATE', 'DELETE')
);

$cfg['Servers'][1]['users']['readonly'] = array(
    'password' => '',
    'privileges' => array('SELECT')
);
```

## Environment Configuration

### Environment Variables
```bash
# phpMyAdmin configuration
PMA_HOST=mysql
PMA_PORT=3306
PMA_USER=hcarecloud_user
PMA_PASSWORD=secure_password

# Security settings
PMA_ABSOLUTE_URI=https://phpmyadmin.hcarecloud.local/
UPLOAD_LIMIT=50M
MEMORY_LIMIT=512M
MAX_EXECUTION_TIME=300

# Session configuration
SESSION_TIMEOUT=3600
SESSION_SAVE_PATH=/tmp/phpmyadmin/sessions/

# SSL configuration
FORCE_SSL=true
SSL_VERIFY=true
```

### Docker Integration
```yaml
# docker-compose.yml
phpmyadmin:
  image: phpmyadmin/phpmyadmin:latest
  environment:
    - PMA_HOST=mysql
    - PMA_PORT=3306
    - PMA_USER=hcarecloud_user
    - PMA_PASSWORD=${MYSQL_PASSWORD}
    - UPLOAD_LIMIT=50M
    - MEMORY_LIMIT=512M
  volumes:
    - ./docker/phpmyadmin/config.user.inc.php:/etc/phpmyadmin/config.user.inc.php
    - ./docker/logs/phpmyadmin:/var/log/phpmyadmin
  ports:
    - "8080:80"
  depends_on:
    - mysql
```

## Database Management

### Backup Operations
```sql
-- Export database structure and data
SELECT * INTO OUTFILE '/tmp/backup_hcarecloud_manager.sql'
FROM information_schema.tables 
WHERE table_schema = 'hcarecloud_manager';

-- Import database backup
LOAD DATA INFILE '/tmp/backup_hcarecloud_manager.sql'
INTO TABLE backup_table;
```

### User Management
```sql
-- Create database user
CREATE USER 'new_user'@'%' IDENTIFIED BY 'secure_password';

-- Grant specific privileges
GRANT SELECT, INSERT, UPDATE ON hcarecloud_manager.* TO 'new_user'@'%';

-- Revoke privileges
REVOKE ALL PRIVILEGES ON *.* FROM 'old_user'@'%';
```

### Query Optimization
```sql
-- Analyze table performance
ANALYZE TABLE users;

-- Check table status
SHOW TABLE STATUS FROM hcarecloud_manager;

-- Optimize tables
OPTIMIZE TABLE users, user_sessions, audit_logs;
```

## Monitoring and Logging

### Access Logging
```php
// Custom logging function
function logDatabaseAccess($user, $action, $table) {
    $log = date('Y-m-d H:i:s') . " - User: $user, Action: $action, Table: $table\n";
    file_put_contents('/var/log/phpmyadmin/access.log', $log, FILE_APPEND);
}
```

### Performance Monitoring
```sql
-- Monitor slow queries
SELECT * FROM mysql.slow_log 
WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR);

-- Check connection status
SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Threads_connected';

-- Monitor table locks
SHOW STATUS LIKE 'Table_locks%';
```

## Security Best Practices

### Access Restrictions
- **IP Whitelisting**: Restrict access to authorized IP addresses
- **User Authentication**: Strong password requirements
- **Session Management**: Short session timeouts and secure cookies
- **SSL Encryption**: Force HTTPS for all connections

### Audit Requirements
- **Query Logging**: Log all database operations
- **User Tracking**: Track user access and actions
- **Change Monitoring**: Monitor schema and data changes
- **Export Control**: Log all data exports and downloads

## Troubleshooting

### Common Issues
1. **Connection Refused**: Check MySQL service and network connectivity
2. **Access Denied**: Verify user credentials and permissions
3. **Upload Failures**: Check file size limits and permissions
4. **Session Timeouts**: Adjust session timeout settings

### Debug Commands
```bash
# Check phpMyAdmin logs
docker logs phpmyadmin -f

# Verify configuration
docker exec phpmyadmin cat /etc/phpmyadmin/config.user.inc.php

# Test database connection
docker exec phpmyadmin mysql -h mysql -u hcarecloud_user -p

# Monitor resource usage
docker stats phpmyadmin --no-stream
```

## Related Documentation
- [MySQL Database Documentation](../mysql/README.md)
- [Security and Compliance Documentation](../../docs/security.md)
- [User Management Documentation](../../docs/user-management.md)
- [Main Project Documentation](../scripts/hcm/themes/hcmpro/docs.md)

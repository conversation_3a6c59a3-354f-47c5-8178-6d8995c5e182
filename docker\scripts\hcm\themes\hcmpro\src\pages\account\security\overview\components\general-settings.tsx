/**
 * H‑CareCloud Project – Security General Settings Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { Fragment, ReactNode, useState, useEffect } from 'react';
import { hcmApi } from '@/services/hcm-api';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { HexagonBadge } from '@/partials/common/hexagon-badge';
import {
  BadgeCheck,
  CheckCircle,
  LocateFixed,
  LucideIcon,
  ShieldCheck,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';

interface IGeneralSettingsItem {
  icon: LucideIcon;
  title: ReactNode;
  description: string;
  actions: ReactNode;
}
type IGeneralSettingsItems = Array<IGeneralSettingsItem>;

const GeneralSettings = () => {
  const [securitySettings, setSecuritySettings] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSecuritySettings();
  }, []);

  const loadSecuritySettings = async () => {
    try {
      const settings = await hcmApi.getSecuritySettings();
      setSecuritySettings(settings);
    } catch (error) {
      toast.error('Failed to load security settings');
      logger.error('Failed to load security settings', {
        component: 'GeneralSettings',
        action: 'loadSecuritySettings'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = async (key: string, value: any) => {
    try {
      const updatedSettings = { ...securitySettings, [key]: value };
      await hcmApi.updateSecuritySettings(updatedSettings);
      setSecuritySettings(updatedSettings);
      toast.success('Security setting updated');
    } catch (error) {
      toast.error('Failed to update security setting');
      logger.error('Failed to update security setting', {
        component: 'GeneralSettings',
        action: 'updateSecuritySetting',
        data: { key, value }
      }, error as Error);
    }
  };

  const items: IGeneralSettingsItems = [
    {
      icon: ShieldCheck,
      title: (
        <Fragment>
          Two-Factor Authentication
          <Badge size="sm" variant="primary" appearance="outline">
            Required
          </Badge>
        </Fragment>
      ),
      description:
        'Add extra security layer to your H-CareManager server access.',
      actions: (
        <Switch 
          size="sm" 
          checked={securitySettings.two_factor_enabled || false}
          onCheckedChange={(checked) => updateSetting('two_factor_enabled', checked)}
          disabled={loading}
        />
      ),
    },
    {
      icon: BadgeCheck,
      title: (
        <Fragment>
          Login Notifications
          <Badge size="sm" variant="info" appearance="outline">
            Recommended
          </Badge>
        </Fragment>
      ),
      description:
        'Get notified when someone logs into your account from new device.',
      actions: (
        <Switch 
          size="sm" 
          checked={securitySettings.login_notifications || true}
          onCheckedChange={(checked) => updateSetting('login_notifications', checked)}
          disabled={loading}
        />
      ),
    },
    {
      icon: LocateFixed,
      title: 'IP Address Whitelist',
      description: "Restrict server access to specific IP addresses only.",
      actions: (
        <Switch 
          size="sm" 
          checked={securitySettings.ip_whitelist_enabled || false}
          onCheckedChange={(checked) => updateSetting('ip_whitelist_enabled', checked)}
          disabled={loading}
        />
      ),
    },
    {
      icon: CheckCircle,
      title: (
        <Fragment>
          Password Expiration
          <Badge variant="warning" appearance="outline">
            Policy
          </Badge>
        </Fragment>
      ),
      description: 'Require password changes every 90 days for security.',
      actions: (
        <Switch 
          size="sm" 
          checked={securitySettings.password_expires_days > 0}
          onCheckedChange={(checked) => updateSetting('password_expires_days', checked ? 90 : 0)}
          disabled={loading}
        />
      ),
    },
  ];

  const renderItem = (item: IGeneralSettingsItem, index: number) => {
    if (loading) {
      return (
        <CardContent
          key={index}
          className="border-b border-border flex items-center flex-wrap sm:flex-nowrap justify-between py-4 gap-2.5"
        >
          <div className="flex items-center gap-3.5">
            <Skeleton className="size-[50px] rounded-lg" />
            <div className="flex flex-col gap-0.5">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-48 mt-1" />
            </div>
          </div>
          <Skeleton className="h-6 w-12" />
        </CardContent>
      );
    }

    return (
      <CardContent
        key={index}
        className="border-b border-border flex items-center flex-wrap sm:flex-nowrap justify-between py-4 gap-2.5"
      >
        <div className="flex items-center gap-3.5">
          <HexagonBadge
            stroke="stroke-input"
            fill="fill-muted/30"
            size="size-[50px]"
            badge={<item.icon className="text-xl text-muted-foreground" />}
          />
          <div className="flex flex-col gap-0.5">
            <span className="flex items-center gap-1.5 leading-none font-medium text-sm text-mono">
              {item.title}
            </span>
            <span className="text-sm text-gray700">{item.description}</span>
          </div>
        </div>
        <div className="flex items-center gap-2.5">{item.actions}</div>
      </CardContent>
    );
  };

  return (
    <Card>
      {items.map((item, index) => {
        return renderItem(item, index);
      })}
    </Card>
  );
};

export {
  GeneralSettings,
  type IGeneralSettingsItem,
  type IGeneralSettingsItems,
};

# H-CareManager Container Configuration

## Overview
This directory contains the Docker container configuration for H-CareManager, the React TypeScript dashboard that serves as the control panel for H-CareCloud Hospital Management System. The manager container handles the frontend build process, development server, and production deployment of the server management interface.

## Architecture

### Container Structure
```
/docker/manager/
├── Dockerfile.minimal      # Minimal production container configuration
├── entrypoint.sh           # Container initialization and startup script
└── [Generated at runtime]
    ├── node_modules/       # Node.js dependencies
    ├── dist/              # Production build output
    └── logs/              # Container operation logs
```

### Container Purpose
- **Development**: Hot-reload development server for React TypeScript
- **Build Process**: Vite-based build system for production optimization
- **Production**: Optimized static file serving for H-CareManager dashboard
- **Integration**: Seamless integration with H-CareCloud HMS backend

## Container Configuration

### Dockerfile.minimal
```dockerfile
# Multi-stage build for optimized production container
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Build production assets
RUN yarn build --mode production

# Production stage
FROM nginx:alpine

# Copy built assets
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

CMD ["nginx", "-g", "daemon off;"]
```

### entrypoint.sh
```bash
#!/bin/bash
set -e

# Environment setup
export NODE_ENV=${NODE_ENV:-production}
export VITE_APP_API_URL=${VITE_APP_API_URL:-http://localhost:5000}
export VITE_HMS_API_URL=${VITE_HMS_API_URL:-http://localhost:8000}

# Create necessary directories
mkdir -p /app/logs
mkdir -p /app/dist
mkdir -p /app/node_modules

# Set permissions
chown -R node:node /app

# Development mode
if [ "$NODE_ENV" = "development" ]; then
    echo "Starting H-CareManager in development mode..."
    exec yarn dev --host 0.0.0.0 --port 3000
fi

# Production mode
if [ "$NODE_ENV" = "production" ]; then
    echo "Starting H-CareManager in production mode..."
    
    # Build if not already built
    if [ ! -d "/app/dist" ] || [ -z "$(ls -A /app/dist)" ]; then
        echo "Building H-CareManager for production..."
        yarn build --mode production
    fi
    
    # Start nginx
    exec nginx -g "daemon off;"
fi

# Default fallback
echo "Starting H-CareManager with default configuration..."
exec "$@"
```

## Development Configuration

### Development Environment
```bash
# Environment variables for development
NODE_ENV=development
VITE_APP_API_URL=http://localhost:5000
VITE_HMS_API_URL=http://localhost:8000
VITE_DEBUG=true
VITE_DEV=true

# Development server settings
VITE_HOST=0.0.0.0
VITE_PORT=3000
VITE_OPEN=false
```

### Hot Reload Setup
```javascript
// vite.config.ts
export default defineConfig({
  server: {
    host: '0.0.0.0',
    port: 3000,
    hmr: {
      port: 3001,
      host: 'localhost'
    },
    watch: {
      usePolling: true,
      interval: 1000
    }
  }
});
```

### Development Commands
```bash
# Start development server
docker exec manager yarn dev

# Install new dependencies
docker exec manager yarn add package-name

# Run TypeScript checks
docker exec manager yarn type-check

# Run linting
docker exec manager yarn lint
```

## Production Configuration

### Production Build
```bash
# Production environment variables
NODE_ENV=production
VITE_APP_API_URL=https://api.hcarecloud.com
VITE_HMS_API_URL=https://hms.hcarecloud.com
VITE_DEBUG=false
VITE_DEV=false

# Build optimization
VITE_BUILD_TARGET=es2020
VITE_BUILD_MINIFY=true
VITE_BUILD_SOURCEMAP=false
```

### Build Process
```bash
# Production build steps
1. Install dependencies (yarn install --frozen-lockfile)
2. TypeScript compilation check (yarn type-check)
3. Linting and code quality (yarn lint)
4. Vite production build (yarn build --mode production)
5. Asset optimization and compression
6. Static file generation for nginx serving
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_types text/css application/javascript application/json;

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API proxy
    location /api/ {
        proxy_pass http://hcm-api:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Environment Configuration

### Required Environment Variables
```bash
# Core Configuration
NODE_ENV=production|development
VITE_APP_API_URL=http://localhost:5000
VITE_HMS_API_URL=http://localhost:8000

# Development Settings
VITE_DEBUG=true|false
VITE_DEV=true|false
VITE_HOST=0.0.0.0
VITE_PORT=3000

# Production Settings
VITE_BUILD_TARGET=es2020
VITE_BUILD_MINIFY=true
VITE_BUILD_SOURCEMAP=false

# Security Settings
VITE_TENANT_ID=default
VITE_API_TIMEOUT=30000
VITE_MAX_FILE_SIZE=********
```

### Docker Compose Integration
```yaml
# docker-compose.yml
manager:
  build:
    context: ./docker/scripts/hcm/themes/hcmpro
    dockerfile: ../../manager/Dockerfile.minimal
  environment:
    - NODE_ENV=development
    - VITE_APP_API_URL=http://hcm-api:5000
    - VITE_HMS_API_URL=http://hcarecloud-app:8000
  volumes:
    - ./docker/scripts/hcm/themes/hcmpro:/app
    - /app/node_modules
  ports:
    - "3000:3000"
    - "3001:3001"
  depends_on:
    - hcm-api
    - mysql
```

## Performance Optimization

### Build Optimization
```javascript
// vite.config.ts production settings
export default defineConfig({
  build: {
    target: 'es2020',
    minify: 'terser',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu']
        }
      }
    }
  }
});
```

### Asset Optimization
- **Code Splitting**: Automatic route-based code splitting
- **Tree Shaking**: Remove unused code from bundles
- **Asset Compression**: Gzip compression for static assets
- **Caching**: Long-term caching for immutable assets

### Performance Monitoring
```bash
# Bundle analysis
docker exec manager yarn build --analyze

# Performance audit
docker exec manager yarn lighthouse

# Memory usage monitoring
docker stats manager
```

## Security Configuration

### Container Security
```dockerfile
# Security best practices
USER node
WORKDIR /app

# Remove unnecessary packages
RUN apk del .build-deps

# Set proper file permissions
COPY --chown=node:node . .

# Health checks
HEALTHCHECK --interval=30s --timeout=3s CMD curl -f http://localhost:3000/ || exit 1
```

### Environment Security
```bash
# Secure environment variables
VITE_APP_API_URL=https://api.hcarecloud.com
VITE_SECURE_COOKIES=true
VITE_CSRF_PROTECTION=true

# Remove development tools in production
NODE_ENV=production
VITE_DEBUG=false
```

## Monitoring and Logging

### Container Health Monitoring
```bash
# Health check endpoint
curl http://localhost:3000/health

# Container status
docker ps | grep manager

# Resource usage
docker stats manager --no-stream
```

### Application Logging
```javascript
// Logger configuration
const logger = {
  level: process.env.VITE_DEBUG ? 'debug' : 'error',
  format: 'json',
  transports: [
    new FileTransport('/app/logs/manager.log'),
    new ConsoleTransport()
  ]
};
```

## Troubleshooting

### Common Issues
1. **Build Failures**: Check TypeScript errors and dependency conflicts
2. **Hot Reload Issues**: Verify file watching and port configuration
3. **API Connection**: Check network connectivity and environment variables
4. **Memory Issues**: Monitor container memory usage and optimize build

### Debug Commands
```bash
# Check container logs
docker logs manager -f

# Access container shell
docker exec -it manager sh

# Check build output
docker exec manager ls -la /app/dist

# Verify environment variables
docker exec manager env | grep VITE
```

## Related Documentation
- [Frontend Documentation](../scripts/hcm/themes/hcmpro/docs.md)
- [API Integration Documentation](../scripts/hcm/api/README.md)
- [Nginx Configuration Documentation](../nginx/README.md)
- [Docker Infrastructure Documentation](../README.md)

# H-CareManager Authentication Pages

## Overview
This directory contains authentication pages for H-CareManager Project, providing secure user authentication, password management, and session handling. The authentication pages are designed with HIPAA compliance, professional UI using Metronic components, and comprehensive security measures.

## Architecture

### Authentication Pages Structure
```
/src/pages/auth/
├── signin-page.tsx         # Main login page
├── reset-password-page.tsx # Password reset request
├── change-password-page.tsx # Password change with token
├── callback-page.tsx       # Authentication callback handling
└── components/             # Auth-specific components
    ├── LoginForm.tsx       # Login form component
    ├── ResetForm.tsx       # Password reset form
    ├── ChangeForm.tsx      # Password change form
    └── AuthLayout.tsx      # Authentication page layout
```

### Authentication Flow
1. **Sign In**: Email/password authentication with H-CareManager backend
2. **Password Reset**: Secure password reset via email token
3. **Password Change**: Token-based password update
4. **Session Management**: Secure session creation and validation
5. **Redirect Handling**: Post-authentication navigation

## Authentication Pages

### signin-page.tsx
**Purpose**: Main login page with HCM authentication integration.

**Key Features**:
- HCM backend authentication (NO Supabase)
- Professional Metronic styling
- Form validation and error handling
- Remember me functionality
- Secure token management
- HIPAA-compliant audit logging

**Implementation**:
```typescript
export const SignInPage: React.FC = () => {
  const [credentials, setCredentials] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { login } = useAuth();
  const navigate = useNavigate();
  const { success, error: showError } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate form
      const validation = validateData(credentials, loginSchema);
      if (!validation.isValid) {
        setError(Object.values(validation.errors)[0]);
        return;
      }

      // Authenticate with HCM backend
      await login(credentials);
      
      // Log successful authentication
      logger.audit({
        action: 'user_login',
        details: { 
          email: credentials.email,
          rememberMe: credentials.rememberMe 
        }
      });

      success('Welcome to H-CareManager');

      // Redirect to intended page or dashboard
      const redirectPath = sessionStorage.getItem('redirect_after_login') || '/dashboard';
      sessionStorage.removeItem('redirect_after_login');
      navigate(redirectPath);

    } catch (err: any) {
      const errorMessage = err.message || 'Authentication failed';
      setError(errorMessage);
      showError(errorMessage);
      
      // Log failed authentication
      logger.audit({
        action: 'user_login_failed',
        details: { 
          email: credentials.email,
          error: errorMessage 
        }
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="d-flex flex-column flex-root">
      <div className="d-flex flex-column flex-lg-row flex-column-fluid">
        {/* Left side - Branding */}
        <div className="d-flex flex-column flex-lg-row-auto bg-primary w-xl-600px positon-xl-relative">
          <div className="d-flex flex-column position-xl-fixed top-0 bottom-0 w-xl-600px scroll-y">
            <div className="d-flex flex-row-fluid flex-column text-center p-10 pt-lg-20">
              <h1 className="fw-bolder text-white fs-2qx pb-5 pb-md-10">
                H-CareManager
              </h1>
              <p className="fw-bold fs-2 text-white">
                Server Management Dashboard
                <br />
                for H-CareCloud HMS
              </p>
            </div>
            <div className="d-flex flex-row-auto bgi-no-repeat bgi-position-x-center bgi-size-contain bgi-position-y-bottom min-h-100px min-h-lg-350px"></div>
          </div>
        </div>

        {/* Right side - Login Form */}
        <div className="d-flex flex-column flex-lg-row-fluid py-10">
          <div className="d-flex flex-center flex-column flex-column-fluid">
            <div className="w-lg-500px p-10 p-lg-15 mx-auto">
              <form className="form w-100" onSubmit={handleSubmit}>
                <div className="text-center mb-10">
                  <h1 className="text-dark mb-3">Sign In</h1>
                  <div className="text-gray-400 fw-bold fs-4">
                    Access your server management dashboard
                  </div>
                </div>

                {error && (
                  <div className="alert alert-danger d-flex align-items-center p-5 mb-10">
                    <i className="fas fa-exclamation-triangle fs-2hx text-danger me-4"></i>
                    <div className="d-flex flex-column">
                      <h5 className="mb-1">Authentication Error</h5>
                      <span>{error}</span>
                    </div>
                  </div>
                )}

                <div className="fv-row mb-10">
                  <label className="form-label fs-6 fw-bolder text-dark">Email</label>
                  <input
                    className="form-control form-control-lg form-control-solid"
                    type="email"
                    name="email"
                    autoComplete="email"
                    value={credentials.email}
                    onChange={(e) => setCredentials(prev => ({ ...prev, email: e.target.value }))}
                    required
                  />
                </div>

                <div className="fv-row mb-10">
                  <div className="d-flex flex-stack mb-2">
                    <label className="form-label fw-bolder text-dark fs-6 mb-0">Password</label>
                    <Link
                      to="/auth/reset-password"
                      className="link-primary fs-6 fw-bolder"
                    >
                      Forgot Password?
                    </Link>
                  </div>
                  <input
                    className="form-control form-control-lg form-control-solid"
                    type="password"
                    name="password"
                    autoComplete="current-password"
                    value={credentials.password}
                    onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                    required
                  />
                </div>

                <div className="fv-row mb-10">
                  <label className="form-check form-check-custom form-check-solid form-check-sm">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={credentials.rememberMe}
                      onChange={(e) => setCredentials(prev => ({ ...prev, rememberMe: e.target.checked }))}
                    />
                    <span className="form-check-label fw-bold text-gray-700 fs-6">
                      Remember me
                    </span>
                  </label>
                </div>

                <div className="text-center">
                  <button
                    type="submit"
                    className="btn btn-lg btn-primary w-100 mb-5"
                    disabled={loading}
                  >
                    {loading && (
                      <span className="spinner-border spinner-border-sm me-2"></span>
                    )}
                    {loading ? 'Signing In...' : 'Sign In'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
```

### reset-password-page.tsx
**Purpose**: Password reset request page with email validation.

**Key Features**:
- Email-based password reset
- Professional form validation
- Rate limiting protection
- Clear user feedback
- Secure token generation

**Implementation**:
```typescript
export const ResetPasswordPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [sent, setSent] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { success, error: showError } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate email
      if (!email || !isValidEmail(email)) {
        setError('Please enter a valid email address');
        return;
      }

      // Send reset request to HCM backend
      await hcmApi.requestPasswordReset(email);
      
      setSent(true);
      success('Password reset instructions sent to your email');
      
      // Log password reset request
      logger.audit({
        action: 'password_reset_requested',
        details: { email }
      });

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to send reset email';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (sent) {
    return (
      <div className="d-flex flex-column flex-root">
        <div className="d-flex flex-center flex-column flex-column-fluid p-10 pb-lg-20">
          <div className="w-lg-500px bg-body rounded shadow-sm p-10 p-lg-15 mx-auto">
            <div className="text-center mb-10">
              <i className="fas fa-envelope-check fs-4x text-success mb-5"></i>
              <h1 className="text-dark mb-3">Check Your Email</h1>
              <div className="text-gray-400 fw-bold fs-4">
                We've sent password reset instructions to:
                <br />
                <strong className="text-dark">{email}</strong>
              </div>
            </div>

            <div className="text-center">
              <Link
                to="/auth/signin"
                className="btn btn-lg btn-primary"
              >
                Back to Sign In
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="d-flex flex-column flex-root">
      <div className="d-flex flex-center flex-column flex-column-fluid p-10 pb-lg-20">
        <div className="w-lg-500px bg-body rounded shadow-sm p-10 p-lg-15 mx-auto">
          <form className="form w-100" onSubmit={handleSubmit}>
            <div className="text-center mb-10">
              <h1 className="text-dark mb-3">Reset Password</h1>
              <div className="text-gray-400 fw-bold fs-4">
                Enter your email to receive reset instructions
              </div>
            </div>

            {error && (
              <div className="alert alert-danger mb-10">
                {error}
              </div>
            )}

            <div className="fv-row mb-10">
              <label className="form-label fs-6 fw-bolder text-dark">Email Address</label>
              <input
                className="form-control form-control-lg form-control-solid"
                type="email"
                name="email"
                autoComplete="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                required
              />
            </div>

            <div className="d-flex flex-wrap justify-content-center pb-lg-0">
              <button
                type="submit"
                className="btn btn-lg btn-primary fw-bolder me-4"
                disabled={loading}
              >
                {loading && (
                  <span className="spinner-border spinner-border-sm me-2"></span>
                )}
                {loading ? 'Sending...' : 'Send Reset Instructions'}
              </button>

              <Link
                to="/auth/signin"
                className="btn btn-lg btn-light-primary fw-bolder"
              >
                Cancel
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
```

### change-password-page.tsx
**Purpose**: Password change page with token validation.

**Key Features**:
- Token-based password reset
- Password strength validation
- Confirmation matching
- Secure password update
- Auto-redirect after success

**Implementation**:
```typescript
export const ChangePasswordPage: React.FC = () => {
  const [passwords, setPasswords] = useState({
    password: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const { success, error: showError } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate passwords
      const validation = validateData(passwords, passwordChangeSchema);
      if (!validation.isValid) {
        setError(Object.values(validation.errors)[0]);
        return;
      }

      if (passwords.password !== passwords.confirmPassword) {
        setError('Passwords do not match');
        return;
      }

      if (!token) {
        setError('Invalid reset token');
        return;
      }

      // Change password with HCM backend
      await hcmApi.changePassword(token, passwords.password);
      
      success('Password changed successfully');
      
      // Log password change
      logger.audit({
        action: 'password_changed',
        details: { method: 'reset_token' }
      });

      // Redirect to login
      setTimeout(() => {
        navigate('/auth/signin');
      }, 2000);

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to change password';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="d-flex flex-column flex-root">
      <div className="d-flex flex-center flex-column flex-column-fluid p-10 pb-lg-20">
        <div className="w-lg-500px bg-body rounded shadow-sm p-10 p-lg-15 mx-auto">
          <form className="form w-100" onSubmit={handleSubmit}>
            <div className="text-center mb-10">
              <h1 className="text-dark mb-3">Set New Password</h1>
              <div className="text-gray-400 fw-bold fs-4">
                Enter your new password below
              </div>
            </div>

            {error && (
              <div className="alert alert-danger mb-10">
                {error}
              </div>
            )}

            <div className="fv-row mb-10">
              <label className="form-label fs-6 fw-bolder text-dark">New Password</label>
              <input
                className="form-control form-control-lg form-control-solid"
                type="password"
                name="password"
                autoComplete="new-password"
                value={passwords.password}
                onChange={(e) => setPasswords(prev => ({ ...prev, password: e.target.value }))}
                placeholder="Enter new password"
                required
              />
              <div className="text-muted fs-7 mt-2">
                Password must be at least 8 characters with uppercase, lowercase, number, and special character.
              </div>
            </div>

            <div className="fv-row mb-10">
              <label className="form-label fs-6 fw-bolder text-dark">Confirm Password</label>
              <input
                className="form-control form-control-lg form-control-solid"
                type="password"
                name="confirmPassword"
                autoComplete="new-password"
                value={passwords.confirmPassword}
                onChange={(e) => setPasswords(prev => ({ ...prev, confirmPassword: e.target.value }))}
                placeholder="Confirm new password"
                required
              />
            </div>

            <div className="text-center">
              <button
                type="submit"
                className="btn btn-lg btn-primary w-100"
                disabled={loading}
              >
                {loading && (
                  <span className="spinner-border spinner-border-sm me-2"></span>
                )}
                {loading ? 'Changing Password...' : 'Change Password'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
```

## Authentication Security

### Security Features
- **HIPAA Compliance**: Audit logging for all authentication events
- **Token Security**: Secure JWT token management
- **Session Management**: Secure session creation and validation
- **Rate Limiting**: Protection against brute force attacks
- **Input Validation**: Comprehensive form validation and sanitization

### Validation Schemas
```typescript
export const loginSchema: ValidationSchema = {
  email: [
    { type: 'required', message: 'Email is required' },
    { type: 'email', message: 'Please enter a valid email address' }
  ],
  password: [
    { type: 'required', message: 'Password is required' }
  ]
};

export const passwordChangeSchema: ValidationSchema = {
  password: [
    { type: 'required', message: 'Password is required' },
    { type: 'minLength', value: 8, message: 'Password must be at least 8 characters' },
    {
      type: 'pattern',
      value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      message: 'Password must contain uppercase, lowercase, number, and special character'
    }
  ]
};
```

## Related Documentation
- [Authentication System Documentation](../../auth/README.md)
- [Services Documentation](../../services/README.md)
- [Routing Documentation](../../routing/README.md)
- [H-CareManager Project Documentation](../../../docs.md)

/**
 * H‑CareCloud Project – Account Appearance Content
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { Fragment, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { Engage } from '@/partials/common/engage';
import { toAbsoluteUrl } from '@/lib/helpers';
import { Accessibility } from './components';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Upload, Palette } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ThemeSettings {
  primary_color: string;
  secondary_color: string;
  background_color: string;
  text_color: string;
  header_style: string;
  logo_url: string;
  font_family: string;
  layout_style: string;
}

export function AccountAppearanceContent() {
  const [themeSettings, setThemeSettings] = useState<ThemeSettings>({
    primary_color: '#3b82f6',
    secondary_color: '#64748b', 
    background_color: '#ffffff',
    text_color: '#1e293b',
    header_style: 'default',
    logo_url: '',
    font_family: 'Inter',
    layout_style: 'default',
  });
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    loadThemeSettings();
  }, []);

  const loadThemeSettings = async () => {
    try {
      const settings = await hcmApi.getSettings();
      if (settings.theme) {
        setThemeSettings(settings.theme);
      }
    } catch (error) {
      toast.error('Failed to load theme settings');
      logger.error('Failed to load theme settings', {
        component: 'AppearanceContent',
        action: 'loadThemeSettings'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const updateThemeSetting = async (key: keyof ThemeSettings, value: string) => {
    try {
      const newSettings = { ...themeSettings, [key]: value };
      setThemeSettings(newSettings);
      await hcmApi.updateSettings({ theme: newSettings });
      
      // Apply theme changes immediately
      applyThemeChanges(newSettings);
      toast.success('Theme updated successfully');
    } catch (error) {
      setThemeSettings(themeSettings); // Revert on error
      toast.error('Failed to update theme');
      logger.error('Failed to update theme settings', {
        component: 'AppearanceContent',
        action: 'updateThemeSetting'
      }, error as Error);
    }
  };

  const applyThemeChanges = (settings: ThemeSettings) => {
    const root = document.documentElement;
    root.style.setProperty('--primary-color', settings.primary_color);
    root.style.setProperty('--secondary-color', settings.secondary_color);
    root.style.setProperty('--background-color', settings.background_color);
    root.style.setProperty('--text-color', settings.text_color);
    root.style.setProperty('--font-family', settings.font_family);
  };

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setUploading(true);
      const result = await hcmApi.uploadLogo(file);
      updateThemeSetting('logo_url', result.logo_url);
      toast.success('Logo uploaded successfully');

      logger.debug('Logo uploaded successfully', {
        component: 'AppearanceContent',
        action: 'uploadLogo',
        data: { filename: file.name }
      });
    } catch (error) {
      toast.error('Failed to upload logo');
      logger.error('Failed to upload logo', {
        component: 'AppearanceContent',
        action: 'uploadLogo',
        data: { filename: file.name }
      }, error as Error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="grid gap-5 lg:gap-7.5">
      <div className="flex flex-col gap-5 lg:gap-7.5">
        
        {/* Theme Customization */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette size={20} />
              Theme Customization
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="grid md:grid-cols-2 gap-6">
                {Array.from({ length: 8 }).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>Primary Color</Label>
                  <div className="flex gap-2">
                    <Input
                      type="color"
                      value={themeSettings.primary_color}
                      onChange={(e) => updateThemeSetting('primary_color', e.target.value)}
                      className="w-20 h-10 p-1"
                    />
                    <Input
                      type="text"
                      value={themeSettings.primary_color}
                      onChange={(e) => updateThemeSetting('primary_color', e.target.value)}
                      placeholder="#3b82f6"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Secondary Color</Label>
                  <div className="flex gap-2">
                    <Input
                      type="color"
                      value={themeSettings.secondary_color}
                      onChange={(e) => updateThemeSetting('secondary_color', e.target.value)}
                      className="w-20 h-10 p-1"
                    />
                    <Input
                      type="text"
                      value={themeSettings.secondary_color}
                      onChange={(e) => updateThemeSetting('secondary_color', e.target.value)}
                      placeholder="#64748b"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Background Color</Label>
                  <div className="flex gap-2">
                    <Input
                      type="color"
                      value={themeSettings.background_color}
                      onChange={(e) => updateThemeSetting('background_color', e.target.value)}
                      className="w-20 h-10 p-1"
                    />
                    <Input
                      type="text"
                      value={themeSettings.background_color}
                      onChange={(e) => updateThemeSetting('background_color', e.target.value)}
                      placeholder="#ffffff"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Text Color</Label>
                  <div className="flex gap-2">
                    <Input
                      type="color"
                      value={themeSettings.text_color}
                      onChange={(e) => updateThemeSetting('text_color', e.target.value)}
                      className="w-20 h-10 p-1"
                    />
                    <Input
                      type="text"
                      value={themeSettings.text_color}
                      onChange={(e) => updateThemeSetting('text_color', e.target.value)}
                      placeholder="#1e293b"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Font Family</Label>
                  <Select value={themeSettings.font_family} onValueChange={(value) => updateThemeSetting('font_family', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Inter">Inter</SelectItem>
                      <SelectItem value="Roboto">Roboto</SelectItem>
                      <SelectItem value="Open Sans">Open Sans</SelectItem>
                      <SelectItem value="Lato">Lato</SelectItem>
                      <SelectItem value="Montserrat">Montserrat</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Header Style</Label>
                  <Select value={themeSettings.header_style} onValueChange={(value) => updateThemeSetting('header_style', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Default</SelectItem>
                      <SelectItem value="minimal">Minimal</SelectItem>
                      <SelectItem value="extended">Extended</SelectItem>
                      <SelectItem value="compact">Compact</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Layout Style</Label>
                  <Select value={themeSettings.layout_style} onValueChange={(value) => updateThemeSetting('layout_style', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Default</SelectItem>
                      <SelectItem value="wide">Wide</SelectItem>
                      <SelectItem value="narrow">Narrow</SelectItem>
                      <SelectItem value="fluid">Fluid</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Logo Upload</Label>
                  <div className="flex gap-2">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                      id="logo-upload"
                    />
                    <Button 
                      onClick={() => document.getElementById('logo-upload')?.click()}
                      disabled={uploading}
                      variant="outline"
                      className="flex-1"
                    >
                      <Upload size={16} className="mr-2" />
                      {uploading ? 'Uploading...' : 'Upload Logo'}
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Accessibility />
        
        <div className="grid lg:grid-cols-2 gap-5 lg:gap-7.5">
          <Engage
            title="Theme Documentation"
            description="Learn about H-CareManager theme customization, color schemes, and branding options for your healthcare management interface."
            image={
              <Fragment>
                <img
                  src={toAbsoluteUrl('/media/illustrations/31.svg')}
                  className="dark:hidden max-h-[150px]"
                  alt="Theme Documentation"
                />
                <img
                  src={toAbsoluteUrl('/media/illustrations/31-dark.svg')}
                  className="light:hidden max-h-[150px]"
                  alt="Theme Documentation"
                />
              </Fragment>
            }
            more={{
              title: 'View Documentation',
              url: '/docs/themes',
            }}
          />
          
          <Engage
            title="Brand Guidelines"
            description="Follow healthcare compliance guidelines for branding, color accessibility, and visual consistency in your H-CareManager interface."
            image={
              <Fragment>
                <img
                  src={toAbsoluteUrl('/media/illustrations/29.svg')}
                  className="dark:hidden max-h-[150px]"
                  alt="Brand Guidelines"
                />
                <img
                  src={toAbsoluteUrl('/media/illustrations/29-dark.svg')}
                  className="light:hidden max-h-[150px]"
                  alt="Brand Guidelines"
                />
              </Fragment>
            }
            more={{
              title: 'Learn More',
              url: '/docs/branding',
            }}
          />
        </div>
      </div>
    </div>
  );
}

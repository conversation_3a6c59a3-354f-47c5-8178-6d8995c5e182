#!/bin/bash
set -e

# H-CareManager Database Initialization Script
#
# @copyright 2025 Hostwek LTD. All rights reserved.
# <AUTHOR> <<EMAIL>>
# @scrum Majok Deng
# @version 1.0.8
#
echo "Running H-CareManager database initialization script..."

# Create databases
echo "Creating databases..."
mysql -u root -p"${MYSQL_ROOT_PASSWORD}" <<-EOSQL
    # Create the H-CareManager database
    CREATE DATABASE IF NOT EXISTS \`hcarecloud_manager\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

    # Create the main H-CareCloud database
    CREATE DATABASE IF NOT EXISTS \`${MYSQL_DATABASE}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

    # Create phpMyAdmin storage database
    CREATE DATABASE IF NOT EXISTS \`phpmyadmin\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

    # Create users
    CREATE USER IF NOT EXISTS '${MYSQL_USER}'@'%' IDENTIFIED BY '${MYSQL_PASSWORD}';
    CREATE USER IF NOT EXISTS '${MYSQL_USER}'@'localhost' IDENTIFIED BY '${MYSQL_PASSWORD}';
    CREATE USER IF NOT EXISTS 'hcm_manager'@'%' IDENTIFIED BY 'hcm_secure_2025';
    CREATE USER IF NOT EXISTS 'hcm_manager'@'localhost' IDENTIFIED BY 'hcm_secure_2025';

    # Create root user with proper access from any host
    ALTER USER 'root'@'localhost' IDENTIFIED BY '${MYSQL_ROOT_PASSWORD}';
    CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY '${MYSQL_ROOT_PASSWORD}';
    CREATE USER IF NOT EXISTS 'root'@'127.0.0.1' IDENTIFIED BY '${MYSQL_ROOT_PASSWORD}';

    # Grant privileges for main H-CareCloud database
    GRANT ALL PRIVILEGES ON \`${MYSQL_DATABASE}\`.* TO '${MYSQL_USER}'@'%';
    GRANT ALL PRIVILEGES ON \`${MYSQL_DATABASE}\`.* TO '${MYSQL_USER}'@'localhost';

    # Grant privileges for H-CareManager database
    GRANT ALL PRIVILEGES ON \`hcarecloud_manager\`.* TO 'hcm_manager'@'%';
    GRANT ALL PRIVILEGES ON \`hcarecloud_manager\`.* TO 'hcm_manager'@'localhost';
    GRANT ALL PRIVILEGES ON \`hcarecloud_manager\`.* TO '${MYSQL_USER}'@'%';
    GRANT ALL PRIVILEGES ON \`hcarecloud_manager\`.* TO '${MYSQL_USER}'@'localhost';

    # Grant privileges for phpMyAdmin
    GRANT ALL PRIVILEGES ON \`phpmyadmin\`.* TO '${MYSQL_USER}'@'%';
    GRANT ALL PRIVILEGES ON \`phpmyadmin\`.* TO '${MYSQL_USER}'@'localhost';

    # Grant root privileges
    GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
    GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
    GRANT ALL PRIVILEGES ON *.* TO 'root'@'127.0.0.1' WITH GRANT OPTION;

    FLUSH PRIVILEGES;
EOSQL

# Initialize H-CareManager database with clean tables
echo "Initializing H-CareManager database tables..."
mysql -u root -p"${MYSQL_ROOT_PASSWORD}" hcarecloud_manager <<-EOSQL
    -- H-CareManager Users table
    CREATE TABLE IF NOT EXISTS \`users\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`name\` varchar(255) NOT NULL,
      \`email\` varchar(255) NOT NULL,
      \`password\` varchar(255) NOT NULL,
      \`role\` enum('admin','manager','developer','support') NOT NULL DEFAULT 'manager',
      \`department\` varchar(100) NULL DEFAULT NULL,
      \`position\` varchar(100) NULL DEFAULT NULL,
      \`phone\` varchar(20) NULL DEFAULT NULL,
      \`status\` enum('active','inactive','suspended') NOT NULL DEFAULT 'active',
      \`last_login_at\` timestamp NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`email\` (\`email\`),
      KEY \`status\` (\`status\`),
      KEY \`role\` (\`role\`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- H-CareManager Settings table
    CREATE TABLE IF NOT EXISTS \`settings\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`key\` varchar(255) NOT NULL,
      \`value\` text,
      \`group\` varchar(100) NOT NULL DEFAULT 'general',
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`key\` (\`key\`),
      KEY \`group\` (\`group\`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- H-CareManager Theme Settings table
    CREATE TABLE IF NOT EXISTS \`theme_settings\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`key\` varchar(255) NOT NULL,
      \`value\` text,
      \`group\` varchar(100) NOT NULL DEFAULT 'theme',
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`key\` (\`key\`),
      KEY \`group\` (\`group\`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- H-CareManager User Sessions table
    CREATE TABLE IF NOT EXISTS \`user_sessions\` (
      \`id\` varchar(255) NOT NULL,
      \`user_id\` int(11) NULL DEFAULT NULL,
      \`device_name\` varchar(255) NULL DEFAULT NULL,
      \`device_type\` varchar(50) NULL DEFAULT NULL,
      \`browser\` varchar(255) NULL DEFAULT NULL,
      \`os\` varchar(255) NULL DEFAULT NULL,
      \`ip_address\` varchar(45) NULL DEFAULT NULL,
      \`location\` varchar(255) NULL DEFAULT NULL,
      \`user_agent\` text NULL DEFAULT NULL,
      \`is_current\` tinyint(1) NOT NULL DEFAULT 0,
      \`trusted\` tinyint(1) NOT NULL DEFAULT 0,
      \`login_count\` int(11) NOT NULL DEFAULT 1,
      \`first_login\` timestamp NULL DEFAULT NULL,
      \`last_activity\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      KEY \`last_activity\` (\`last_activity\`),
      KEY \`is_current\` (\`is_current\`),
      CONSTRAINT \`user_sessions_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- API Tokens table for account/api-keys
    CREATE TABLE IF NOT EXISTS \`api_tokens\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`name\` varchar(255) NOT NULL,
      \`token\` varchar(255) NOT NULL,
      \`abilities\` json NULL DEFAULT NULL,
      \`last_used_at\` timestamp NULL DEFAULT NULL,
      \`status\` enum('active','inactive','revoked') NOT NULL DEFAULT 'active',
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      \`expires_at\` timestamp NULL DEFAULT NULL,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`token\` (\`token\`),
      KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`api_tokens_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Notifications table for account/notifications
    CREATE TABLE IF NOT EXISTS \`notifications\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`type\` varchar(100) NOT NULL,
      \`title\` varchar(255) NOT NULL,
      \`message\` text NOT NULL,
      \`data\` json NULL DEFAULT NULL,
      \`read_at\` timestamp NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`notifications_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Activity Log table for account/activity
    CREATE TABLE IF NOT EXISTS \`activity_log\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`action\` varchar(255) NOT NULL,
      \`description\` text NOT NULL,
      \`ip_address\` varchar(45) NULL DEFAULT NULL,
      \`user_agent\` text NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`activity_log_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- User Profiles table for account/home/<USER>
    CREATE TABLE IF NOT EXISTS \`user_profiles\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`first_name\` varchar(100) NULL DEFAULT NULL,
      \`last_name\` varchar(100) NULL DEFAULT NULL,
      \`avatar\` varchar(500) NULL DEFAULT NULL,
      \`bio\` text NULL DEFAULT NULL,
      \`company\` varchar(200) NULL DEFAULT NULL,
      \`website\` varchar(500) NULL DEFAULT NULL,
      \`location\` varchar(255) NULL DEFAULT NULL,
      \`timezone\` varchar(50) NULL DEFAULT 'UTC',
      \`language\` varchar(10) NULL DEFAULT 'en',
      \`date_of_birth\` date NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`user_profiles_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Company Profiles table for account/home/<USER>
    CREATE TABLE IF NOT EXISTS \`company_profiles\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`company_name\` varchar(255) NOT NULL,
      \`company_logo\` varchar(500) NULL DEFAULT NULL,
      \`industry\` varchar(100) NULL DEFAULT NULL,
      \`company_size\` enum('1-10','11-50','51-200','201-500','501-1000','1000+') NULL DEFAULT NULL,
      \`website\` varchar(500) NULL DEFAULT NULL,
      \`description\` text NULL DEFAULT NULL,
      \`address\` text NULL DEFAULT NULL,
      \`city\` varchar(100) NULL DEFAULT NULL,
      \`state\` varchar(100) NULL DEFAULT NULL,
      \`country\` varchar(100) NULL DEFAULT NULL,
      \`postal_code\` varchar(20) NULL DEFAULT NULL,
      \`phone\` varchar(20) NULL DEFAULT NULL,
      \`email\` varchar(255) NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`company_profiles_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Billing Plans table for account/billing/plans
    CREATE TABLE IF NOT EXISTS \`billing_plans\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`name\` varchar(255) NOT NULL,
      \`description\` text NULL DEFAULT NULL,
      \`price\` decimal(10,2) NOT NULL,
      \`billing_cycle\` enum('monthly','yearly') NOT NULL DEFAULT 'monthly',
      \`features\` json NULL DEFAULT NULL,
      \`max_users\` int(11) NULL DEFAULT NULL,
      \`max_storage\` bigint(20) NULL DEFAULT NULL,
      \`status\` enum('active','inactive') NOT NULL DEFAULT 'active',
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- User Subscriptions table for account/billing
    CREATE TABLE IF NOT EXISTS \`user_subscriptions\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`plan_id\` int(11) NOT NULL,
      \`status\` enum('active','cancelled','expired','trial') NOT NULL DEFAULT 'trial',
      \`trial_ends_at\` timestamp NULL DEFAULT NULL,
      \`current_period_start\` timestamp NULL DEFAULT NULL,
      \`current_period_end\` timestamp NULL DEFAULT NULL,
      \`cancelled_at\` timestamp NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      KEY \`plan_id\` (\`plan_id\`),
      CONSTRAINT \`user_subscriptions_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE,
      CONSTRAINT \`user_subscriptions_ibfk_2\` FOREIGN KEY (\`plan_id\`) REFERENCES \`billing_plans\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Billing History table for account/billing/history (updated for MTN MoMo)
    CREATE TABLE IF NOT EXISTS \`billing_history\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`subscription_id\` int(11) NULL DEFAULT NULL,
      \`amount\` decimal(10,2) NOT NULL,
      \`currency\` varchar(3) NOT NULL DEFAULT 'USD',
      \`status\` enum('pending','paid','failed','refunded','successful') NOT NULL DEFAULT 'pending',
      \`invoice_number\` varchar(255) NULL DEFAULT NULL,
      \`payment_method\` varchar(100) NULL DEFAULT NULL,
      \`transaction_id\` varchar(255) NULL DEFAULT NULL,
      \`gateway_response\` json NULL DEFAULT NULL,
      \`billing_date\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`due_date\` timestamp NULL DEFAULT NULL,
      \`paid_at\` timestamp NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      KEY \`subscription_id\` (\`subscription_id\`),
      KEY \`transaction_id\` (\`transaction_id\`),
      CONSTRAINT \`billing_history_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE,
      CONSTRAINT \`billing_history_ibfk_2\` FOREIGN KEY (\`subscription_id\`) REFERENCES \`user_subscriptions\` (\`id\`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Security tables for account/security/*
    CREATE TABLE IF NOT EXISTS \`security_devices\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`device_name\` varchar(255) NOT NULL,
      \`device_type\` varchar(100) NOT NULL,
      \`browser\` varchar(100) NULL DEFAULT NULL,
      \`os\` varchar(100) NULL DEFAULT NULL,
      \`ip_address\` varchar(45) NOT NULL,
      \`location\` varchar(255) NULL DEFAULT NULL,
      \`is_trusted\` tinyint(1) NOT NULL DEFAULT 0,
      \`last_used_at\` timestamp NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`security_devices_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Allowed IP Addresses table for account/security/allowed-ip-addresses
    CREATE TABLE IF NOT EXISTS \`allowed_ip_addresses\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`ip_address\` varchar(45) NOT NULL,
      \`label\` varchar(255) NULL DEFAULT NULL,
      \`is_active\` tinyint(1) NOT NULL DEFAULT 1,
      \`status\` enum('active','inactive','blocked','pending') NOT NULL DEFAULT 'active',
      \`last_used\` timestamp NULL DEFAULT NULL,
      \`auth_method\` varchar(100) NOT NULL DEFAULT 'Basic',
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      KEY \`status\` (\`status\`),
      CONSTRAINT \`allowed_ip_addresses_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Backup and Recovery table for account/security/backup-and-recovery
    CREATE TABLE IF NOT EXISTS \`backup_recovery\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`backup_type\` enum('full','incremental','differential') NOT NULL,
      \`file_path\` varchar(500) NOT NULL,
      \`file_size\` bigint(20) NOT NULL,
      \`status\` enum('pending','completed','failed') NOT NULL DEFAULT 'pending',
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`completed_at\` timestamp NULL DEFAULT NULL,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`backup_recovery_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Privacy Settings table for account/security/privacy-settings
    CREATE TABLE IF NOT EXISTS \`privacy_settings\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`profile_visibility\` enum('public','private','contacts') NOT NULL DEFAULT 'private',
      \`show_email\` tinyint(1) NOT NULL DEFAULT 0,
      \`show_phone\` tinyint(1) NOT NULL DEFAULT 0,
      \`allow_search_engines\` tinyint(1) NOT NULL DEFAULT 0,
      \`data_processing_consent\` tinyint(1) NOT NULL DEFAULT 0,
      \`marketing_emails\` tinyint(1) NOT NULL DEFAULT 0,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`privacy_settings_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Integrations table for account/integrations
    CREATE TABLE IF NOT EXISTS \`integrations\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`service_name\` varchar(255) NOT NULL,
      \`service_type\` varchar(100) NOT NULL,
      \`api_key\` varchar(500) NULL DEFAULT NULL,
      \`webhook_url\` varchar(500) NULL DEFAULT NULL,
      \`config\` json NULL DEFAULT NULL,
      \`status\` enum('active','inactive','error') NOT NULL DEFAULT 'inactive',
      \`last_sync_at\` timestamp NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`integrations_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Appearance Settings table for account/appearance
    CREATE TABLE IF NOT EXISTS \`appearance_settings\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`theme_mode\` enum('light','dark','auto') NOT NULL DEFAULT 'dark',
      \`primary_color\` varchar(7) NOT NULL DEFAULT '#2563EB',
      \`sidebar_style\` enum('default','compact','minimal') NOT NULL DEFAULT 'default',
      \`font_size\` enum('small','medium','large') NOT NULL DEFAULT 'medium',
      \`animations_enabled\` tinyint(1) NOT NULL DEFAULT 1,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`appearance_settings_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Departments table for account/staffs/departments
    CREATE TABLE IF NOT EXISTS \`departments\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`name\` varchar(255) NOT NULL,
      \`description\` text NULL DEFAULT NULL,
      \`manager_id\` int(11) NULL DEFAULT NULL,
      \`budget\` decimal(15,2) NULL DEFAULT NULL,
      \`status\` enum('active','inactive') NOT NULL DEFAULT 'active',
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`manager_id\` (\`manager_id\`),
      CONSTRAINT \`departments_ibfk_1\` FOREIGN KEY (\`manager_id\`) REFERENCES \`users\` (\`id\`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Roles table for account/staffs/roles
    CREATE TABLE IF NOT EXISTS \`roles\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`name\` varchar(255) NOT NULL,
      \`description\` text NULL DEFAULT NULL,
      \`permissions\` json NULL DEFAULT NULL,
      \`level\` int(11) NOT NULL DEFAULT 1,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`name\` (\`name\`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- User Roles table for role assignments
    CREATE TABLE IF NOT EXISTS \`user_roles\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`role_id\` int(11) NOT NULL,
      \`assigned_by\` int(11) NOT NULL,
      \`is_primary\` tinyint(1) NOT NULL DEFAULT 0,
      \`assigned_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`user_role\` (\`user_id\`, \`role_id\`),
      KEY \`role_id\` (\`role_id\`),
      KEY \`assigned_by\` (\`assigned_by\`),
      KEY \`is_primary\` (\`is_primary\`),
      CONSTRAINT \`user_roles_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE,
      CONSTRAINT \`user_roles_ibfk_2\` FOREIGN KEY (\`role_id\`) REFERENCES \`roles\` (\`id\`) ON DELETE CASCADE,
      CONSTRAINT \`user_roles_ibfk_3\` FOREIGN KEY (\`assigned_by\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Insert default H-CareManager admin user
    INSERT INTO \`users\` (\`name\`, \`email\`, \`password\`, \`role\`, \`department\`, \`position\`, \`status\`)
    SELECT 'H-CareManager Admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'IT', 'System Administrator', 'active'
    FROM DUAL
    WHERE NOT EXISTS (SELECT 1 FROM \`users\` WHERE \`email\` = '<EMAIL>' LIMIT 1);

    -- Insert default admin profile
    INSERT INTO \`user_profiles\` (\`user_id\`, \`first_name\`, \`last_name\`, \`company\`, \`timezone\`, \`language\`)
    SELECT u.id, 'H-CareManager', 'Administrator', 'Hostwek LTD', 'UTC', 'en'
    FROM \`users\` u
    WHERE u.email = '<EMAIL>'
    AND NOT EXISTS (SELECT 1 FROM \`user_profiles\` WHERE \`user_id\` = u.id);

    -- Insert default billing plans
    INSERT INTO \`billing_plans\` (\`name\`, \`description\`, \`price\`, \`billing_cycle\`, \`max_users\`, \`max_storage\`)
    VALUES
    ('Starter', 'Perfect for small teams', 29.00, 'monthly', 5, ***********),
    ('Professional', 'For growing businesses', 79.00, 'monthly', 25, ***********0),
    ('Enterprise', 'For large organizations', 199.00, 'monthly', 100, ***********00)
    ON DUPLICATE KEY UPDATE \`updated_at\` = CURRENT_TIMESTAMP;

    -- Insert default departments
    INSERT INTO \`departments\` (\`name\`, \`description\`)
    VALUES
    ('IT Department', 'Information Technology and System Administration'),
    ('Development', 'Software Development and Engineering'),
    ('Support', 'Customer Support and Technical Assistance'),
    ('Management', 'Executive and Administrative Management')
    ON DUPLICATE KEY UPDATE \`updated_at\` = CURRENT_TIMESTAMP;

    -- Insert default roles
    INSERT INTO \`roles\` (\`name\`, \`description\`, \`permissions\`, \`level\`)
    VALUES
    ('Super Admin', 'Full system access and control', '{"docker": {"containers": true, "images": true, "volumes": true, "networks": true}, "system": {"monitoring": true, "logs": true, "dns": true}, "database": {"operations": true, "backups": true}, "environment": {"management": true}, "security": {"auth": true}, "backup": {"recovery": true}}', 10),
    ('Admin', 'Administrative access with some restrictions', '{"docker": {"containers": true, "images": true, "volumes": false, "networks": true}, "system": {"monitoring": true, "logs": true, "dns": false}, "database": {"operations": true, "backups": true}, "environment": {"management": false}, "security": {"auth": true}, "backup": {"recovery": true}}', 8),
    ('Manager', 'Management level access', '{"docker": {"containers": true, "images": false, "volumes": false, "networks": false}, "system": {"monitoring": true, "logs": true, "dns": false}, "database": {"operations": false, "backups": false}, "environment": {"management": false}, "security": {"auth": false}, "backup": {"recovery": false}}', 6),
    ('Developer', 'Development and technical access', '{"docker": {"containers": true, "images": true, "volumes": true, "networks": true}, "system": {"monitoring": true, "logs": true, "dns": true}, "database": {"operations": true, "backups": false}, "environment": {"management": true}, "security": {"auth": false}, "backup": {"recovery": false}}', 5),
    ('Support', 'Support and assistance access', '{"docker": {"containers": false, "images": false, "volumes": false, "networks": false}, "system": {"monitoring": true, "logs": true, "dns": false}, "database": {"operations": false, "backups": false}, "environment": {"management": false}, "security": {"auth": false}, "backup": {"recovery": false}}', 3),
    ('User', 'Basic user access', '{"docker": {"containers": false, "images": false, "volumes": false, "networks": false}, "system": {"monitoring": true, "logs": false, "dns": false}, "database": {"operations": false, "backups": false}, "environment": {"management": false}, "security": {"auth": false}, "backup": {"recovery": false}}', 1)
    ON DUPLICATE KEY UPDATE \`updated_at\` = CURRENT_TIMESTAMP;

    -- Insert default H-CareManager settings
    INSERT INTO \`settings\` (\`key\`, \`value\`, \`group\`)
    VALUES
    ('app_name', 'H-CareManager', 'general'),
    ('app_version', '1.0.8', 'general'),
    ('maintenance_mode', 'false', 'general')
    ON DUPLICATE KEY UPDATE \`updated_at\` = CURRENT_TIMESTAMP;

    -- Insert default H-CareManager theme settings (using actual HCMPro colors)
    INSERT INTO \`theme_settings\` (\`key\`, \`value\`, \`group\`)
    VALUES
    ('theme_name', 'hcmpro', 'theme'),
    ('theme_mode', 'dark', 'theme'),
    ('theme_layout', 'hcmlayout1', 'theme'),
    ('primary_color', '#1379f0', 'colors'),
    ('secondary_color', 'oklch(27.4% 0.006 286.033)', 'colors'),
    ('accent_color', 'oklch(21% 0.006 285.885)', 'colors'),
    ('background_color', 'oklch(14.1% 0.005 285.823)', 'colors'),
    ('foreground_color', 'oklch(98.5% 0 0)', 'colors'),
    ('border_color', 'oklch(27.4% 0.006 286.033)', 'colors'),
    ('muted_color', 'oklch(21% 0.006 285.885)', 'colors')
    ON DUPLICATE KEY UPDATE \`updated_at\` = CURRENT_TIMESTAMP;

    -- Additional Security Tables required by security.py
    CREATE TABLE IF NOT EXISTS \`user_security_settings\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`two_factor_enabled\` tinyint(1) NOT NULL DEFAULT 0,
      \`login_notifications\` tinyint(1) NOT NULL DEFAULT 1,
      \`session_timeout\` int(11) NOT NULL DEFAULT 60,
      \`ip_whitelist_enabled\` tinyint(1) NOT NULL DEFAULT 0,
      \`password_expires_days\` int(11) NOT NULL DEFAULT 90,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`user_security_settings_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Two-Factor Authentication Secrets table
    CREATE TABLE IF NOT EXISTS \`two_factor_secrets\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`secret\` varchar(32) NOT NULL,
      \`backup_codes\` json NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`two_factor_secrets_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Password Reset Tokens table
    CREATE TABLE IF NOT EXISTS \`password_reset_tokens\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`token\` varchar(255) NOT NULL,
      \`expires_at\` timestamp NOT NULL,
      \`used\` tinyint(1) NOT NULL DEFAULT 0,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`token\` (\`token\`),
      KEY \`user_id\` (\`user_id\`),
      KEY \`expires_at\` (\`expires_at\`),
      CONSTRAINT \`password_reset_tokens_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Email Verification Tokens table
    CREATE TABLE IF NOT EXISTS \`email_verification_tokens\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`token\` varchar(255) NOT NULL,
      \`email\` varchar(255) NOT NULL,
      \`expires_at\` timestamp NOT NULL,
      \`verified\` tinyint(1) NOT NULL DEFAULT 0,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`token\` (\`token\`),
      KEY \`user_id\` (\`user_id\`),
      KEY \`expires_at\` (\`expires_at\`),
      CONSTRAINT \`email_verification_tokens_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Audit Logs table required by security.py
    CREATE TABLE IF NOT EXISTS \`audit_logs\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`action\` varchar(255) NOT NULL,
      \`description\` text NOT NULL,
      \`ip_address\` varchar(45) NULL DEFAULT NULL,
      \`user_agent\` text NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      KEY \`action\` (\`action\`),
      CONSTRAINT \`audit_logs_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Notification Settings table required by notifications.py
    CREATE TABLE IF NOT EXISTS \`user_notification_settings\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT,
      \`user_id\` int(11) NOT NULL,
      \`email_notifications\` tinyint(1) NOT NULL DEFAULT 1,
      \`browser_notifications\` tinyint(1) NOT NULL DEFAULT 1,
      \`sound_notifications\` tinyint(1) NOT NULL DEFAULT 1,
      \`notification_frequency\` enum('immediate','hourly','daily','weekly') NOT NULL DEFAULT 'immediate',
      \`categories\` json NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`user_notification_settings_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Fix user_sessions table structure to match security.py expectations
    ALTER TABLE \`user_sessions\` 
    ADD COLUMN IF NOT EXISTS \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

    -- Update users table to include additional fields needed by auth.py
    ALTER TABLE \`users\` 
    ADD COLUMN IF NOT EXISTS \`first_name\` varchar(100) NULL DEFAULT NULL AFTER \`name\`,
    ADD COLUMN IF NOT EXISTS \`last_name\` varchar(100) NULL DEFAULT NULL AFTER \`first_name\`,
    ADD COLUMN IF NOT EXISTS \`profile_image\` varchar(500) NULL DEFAULT NULL AFTER \`last_name\`,
    ADD COLUMN IF NOT EXISTS \`bio\` text NULL DEFAULT NULL AFTER \`profile_image\`,
    ADD COLUMN IF NOT EXISTS \`is_active\` tinyint(1) NOT NULL DEFAULT 1 AFTER \`status\`,
    ADD COLUMN IF NOT EXISTS \`last_login\` timestamp NULL DEFAULT NULL AFTER \`last_login_at\`;

    -- Create sample notifications for testing
    INSERT INTO \`notifications\` (\`user_id\`, \`type\`, \`title\`, \`message\`, \`data\`)
    SELECT u.id, 'welcome', 'Welcome to H-CareManager', 'Thank you for joining H-CareManager. Your account has been successfully created.', 
           JSON_OBJECT('action_url', '/account/home/<USER>', 'priority', 'high')
    FROM \`users\` u
    WHERE u.email = '<EMAIL>'
    AND NOT EXISTS (SELECT 1 FROM \`notifications\` WHERE \`user_id\` = u.id AND \`type\` = 'welcome');

    -- Create sample API token for admin user
    INSERT INTO \`api_tokens\` (\`user_id\`, \`name\`, \`token\`, \`abilities\`, \`status\`, \`expires_at\`)
    SELECT u.id, 'Default Admin Token', CONCAT('hcc_', MD5(CONCAT(u.id, NOW()))), 
           JSON_ARRAY('*'), 'active', DATE_ADD(NOW(), INTERVAL 1 YEAR)
    FROM \`users\` u
    WHERE u.email = '<EMAIL>'
    AND NOT EXISTS (SELECT 1 FROM \`api_tokens\` WHERE \`user_id\` = u.id AND \`name\` = 'Default Admin Token');

    -- Create default security settings for admin user
    INSERT INTO \`user_security_settings\` (\`user_id\`, \`two_factor_enabled\`, \`login_notifications\`, \`session_timeout\`)
    SELECT u.id, 0, 1, 60
    FROM \`users\` u
    WHERE u.email = '<EMAIL>'
    AND NOT EXISTS (SELECT 1 FROM \`user_security_settings\` WHERE \`user_id\` = u.id);

    -- Create default notification settings for admin user
    INSERT INTO \`user_notification_settings\` (\`user_id\`, \`email_notifications\`, \`browser_notifications\`, \`categories\`)
    SELECT u.id, 1, 1, JSON_OBJECT('system', true, 'security', true, 'updates', true, 'backups', true)
    FROM \`users\` u
    WHERE u.email = '<EMAIL>'
    AND NOT EXISTS (SELECT 1 FROM \`user_notification_settings\` WHERE \`user_id\` = u.id);

    -- Additional tables for completed account pages
    
    -- Payment Methods table for billing (updated for MTN MoMo South Sudan)
    CREATE TABLE IF NOT EXISTS \`payment_methods\` (
      \`id\` varchar(36) NOT NULL DEFAULT (UUID()),
      \`user_id\` int(11) NOT NULL,
      \`type\` enum('card','paypal','bank','mtn_momo') NOT NULL,
      \`provider\` varchar(50) NOT NULL,
      \`last_four\` varchar(4) NULL DEFAULT NULL,
      \`expiry_date\` varchar(7) NULL DEFAULT NULL,
      \`email\` varchar(255) NULL DEFAULT NULL,
      \`phone_number\` varchar(20) NULL DEFAULT NULL,
      \`is_primary\` tinyint(1) NOT NULL DEFAULT 0,
      \`status\` enum('active','inactive','expired') NOT NULL DEFAULT 'active',
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`payment_methods_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Billing Details table
    CREATE TABLE IF NOT EXISTS \`billing_details\` (
      \`id\` varchar(36) NOT NULL DEFAULT (UUID()),
      \`user_id\` int(11) NOT NULL,
      \`company_name\` varchar(255) NULL DEFAULT NULL,
      \`address\` text NULL DEFAULT NULL,
      \`contact_person\` varchar(255) NULL DEFAULT NULL,
      \`vat_id\` varchar(50) NULL DEFAULT NULL,
      \`phone\` varchar(20) NULL DEFAULT NULL,
      \`email\` varchar(255) NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`billing_details_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Staff Connections table
    CREATE TABLE IF NOT EXISTS \`staff_connections\` (
      \`id\` varchar(36) NOT NULL DEFAULT (UUID()),
      \`user_id\` int(11) NOT NULL,
      \`staff_id\` varchar(36) NOT NULL,
      \`name\` varchar(255) NOT NULL,
      \`email\` varchar(255) NULL DEFAULT NULL,
      \`avatar\` varchar(255) NULL DEFAULT NULL,
      \`department\` varchar(100) NULL DEFAULT NULL,
      \`connections\` int(11) NOT NULL DEFAULT 0,
      \`joint_links\` int(11) NOT NULL DEFAULT 0,
      \`connected\` tinyint(1) NOT NULL DEFAULT 0,
      \`status\` enum('active','inactive','away') NOT NULL DEFAULT 'active',
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`unique_connection\` (\`user_id\`, \`staff_id\`),
      KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`staff_connections_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Server Integrations table (enhanced)
    CREATE TABLE IF NOT EXISTS \`server_integrations\` (
      \`id\` varchar(36) NOT NULL DEFAULT (UUID()),
      \`user_id\` int(11) NOT NULL,
      \`name\` varchar(100) NOT NULL,
      \`description\` text NULL DEFAULT NULL,
      \`logo\` varchar(255) NULL DEFAULT NULL,
      \`enabled\` tinyint(1) NOT NULL DEFAULT 0,
      \`category\` enum('infrastructure','monitoring','backup','communication','documentation') NOT NULL,
      \`config\` json NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`server_integrations_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Notification Channels table
    CREATE TABLE IF NOT EXISTS \`notification_channels\` (
      \`id\` varchar(36) NOT NULL DEFAULT (UUID()),
      \`user_id\` int(11) NOT NULL,
      \`title\` varchar(100) NOT NULL,
      \`description\` text NULL DEFAULT NULL,
      \`enabled\` tinyint(1) NOT NULL DEFAULT 0,
      \`connected\` tinyint(1) NOT NULL DEFAULT 0,
      \`endpoint\` varchar(255) NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`notification_channels_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- System Notifications table
    CREATE TABLE IF NOT EXISTS \`system_notifications\` (
      \`id\` varchar(36) NOT NULL DEFAULT (UUID()),
      \`user_id\` int(11) NOT NULL,
      \`title\` varchar(100) NOT NULL,
      \`description\` text NULL DEFAULT NULL,
      \`enabled\` tinyint(1) NOT NULL DEFAULT 0,
      \`category\` varchar(50) NULL DEFAULT NULL,
      \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (\`id\`),
      KEY \`user_id\` (\`user_id\`),
      CONSTRAINT \`system_notifications_ibfk_1\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    -- Insert default server integrations
    INSERT INTO \`server_integrations\` (\`id\`, \`user_id\`, \`name\`, \`description\`, \`logo\`, \`category\`, \`enabled\`)
    SELECT UUID(), u.id, 'Docker Hub', 'Container registry integration for automated image pulls and deployments.', 'docker.svg', 'infrastructure', 1
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'H-CareCloud HMS', 'Main hospital management system API connection and sync.', 'aws.svg', 'infrastructure', 0
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'System Monitoring', 'Health checks and performance monitoring for server infrastructure.', 'google-webdev.svg', 'monitoring', 1
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Database Backup', 'Automated MySQL backup and recovery system integration.', 'gitlab.svg', 'backup', 1
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Log Analytics', 'Centralized logging and analytics for system troubleshooting.', 'google-analytics-2.svg', 'monitoring', 0
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Alert Notifications', 'Slack integration for server alerts and maintenance notifications.', 'slack.svg', 'communication', 0
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Issue Tracking', 'Server maintenance and incident tracking integration.', 'jira.svg', 'documentation', 0
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Documentation', 'Server configuration and procedure documentation system.', 'evernote.svg', 'documentation', 1
    FROM \`users\` u WHERE u.email = '<EMAIL>';

    -- Insert default notification channels
    INSERT INTO \`notification_channels\` (\`id\`, \`user_id\`, \`title\`, \`description\`, \`enabled\`, \`connected\`, \`endpoint\`)
    SELECT UUID(), u.id, 'Email', u.email, 1, 1, u.email
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Mobile', '(*************', 0, 0, '(*************'
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Slack', 'Receive instant alerts for messages and updates directly in Slack.', 0, 0, NULL
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Desktop', 'Enable notifications for real-time desktop alerts.', 1, 1, NULL
    FROM \`users\` u WHERE u.email = '<EMAIL>';

    -- Insert default system notifications
    INSERT INTO \`system_notifications\` (\`id\`, \`user_id\`, \`title\`, \`description\`, \`enabled\`, \`category\`)
    SELECT UUID(), u.id, 'Server Alert', 'Notification when server status changes or issues occur.', 1, 'server'
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Backup Status', 'Get notified about backup completion and failures.', 1, 'backup'
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Security Alert', 'Alert for suspicious login attempts and security events.', 1, 'security'
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'System Updates', 'Notifications about system updates and maintenance.', 1, 'system'
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Resource Usage', 'Alert when server resources exceed thresholds.', 1, 'monitoring'
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Database Status', 'Notifications about database health and performance.', 1, 'database'
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Integration Status', 'Status updates for external service integrations.', 0, 'integration'
    FROM \`users\` u WHERE u.email = '<EMAIL>';

    -- Insert sample staff connections for user profile API
    INSERT INTO \`staff_connections\` (\`id\`, \`user_id\`, \`name\`, \`avatar\`, \`department\`, \`position\`, \`status\`, \`connected\`)
    SELECT UUID(), u.id, 'Docker Manager', 'blank.png', 'DevOps', 'Container Operations', 'active', 1
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Database Admin', 'blank.png', 'IT Operations', 'MySQL Administration', 'active', 1
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'System Monitor', 'blank.png', 'IT Support', 'System Monitoring', 'active', 0
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT UUID(), u.id, 'Security Admin', 'blank.png', 'Security', 'Access Control', 'inactive', 0
    FROM \`users\` u WHERE u.email = '<EMAIL>';

    -- Insert sample billing details for admin user
    INSERT INTO \`billing_details\` (\`user_id\`, \`first_name\`, \`last_name\`, \`company\`, \`email\`, \`phone\`, \`address_line1\`, \`city\`, \`state\`, \`postal_code\`, \`country\`, \`tax_id\`)
    SELECT u.id, 'Admin', 'User', 'H-CareCloud Healthcare Solutions', u.email, '******-0123', '123 Healthcare Ave', 'Medical City', 'CA', '90210', 'United States', 'TAX123456789'
    FROM \`users\` u WHERE u.email = '<EMAIL>';

    -- Insert sample payment methods for admin user
    INSERT INTO \`payment_methods\` (\`user_id\`, \`type\`, \`provider\`, \`last_four\`, \`expiry_date\`, \`is_primary\`)
    SELECT u.id, 'card', 'Visa', '4321', '12/26', 1
    FROM \`users\` u WHERE u.email = '<EMAIL>'
    UNION ALL
    SELECT u.id, 'card', 'Mastercard', '8765', '08/25', 0
    FROM \`users\` u WHERE u.email = '<EMAIL>';

    -- Fix password for admin user (MD5 hash of 'password')
    UPDATE \`users\` SET \`password\` = '5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8' 
    WHERE \`email\` = '<EMAIL>';
EOSQL

echo "H-CareManager database initialization completed successfully."


/**
 * H‑CareCloud Project – Other Notifications Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { hcmApi } from '@/services/hcm-api';
import { CardNotification } from '@/partials/cards';
import {
  Server,
  Shield,
  Database,
  HardDrive,
  Activity,
  Bell,
  AlertTriangle,
} from 'lucide-react';
import { Link } from 'react-router';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';
import { IChannelsItem, IChannelsItems } from './';

const OtherNotifications = () => {
  const [notifications, setNotifications] = useState<IChannelsItems>([]);
  const [systemAlertsEnabled, setSystemAlertsEnabled] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSystemNotifications();
  }, []);

  const loadSystemNotifications = async () => {
    try {
      // Load system notifications configuration
      const systemNotifications: IChannelsItems = [
        {
          id: '1',
          icon: Server,
          title: 'Server Status Updates',
          description: 'Receive notifications about server health, uptime, and performance.',
          button: false,
          enabled: true,
          connected: true,
        },
        {
          id: '2',
          icon: AlertTriangle,
          title: 'Critical System Alerts',
          description: 'Immediate notifications for system failures and critical errors.',
          button: false,
          enabled: true,
          connected: true,
        },
        {
          id: '3',
          icon: Database,
          title: 'Database Events',
          description: 'Notifications for database backups, migrations, and connectivity.',
          button: false,
          enabled: false,
          connected: true,
        },
        {
          id: '4',
          icon: HardDrive,
          title: 'Storage Alerts',
          description: 'Disk space, backup status, and storage performance notifications.',
          button: false,
          enabled: true,
          connected: true,
        },
        {
          id: '5',
          icon: Shield,
          title: 'Security Events',
          description: 'Login attempts, failed authentications, and security updates.',
          button: false,
          enabled: true,
          connected: true,
        },
        {
          id: '6',
          icon: Activity,
          title: 'Performance Monitoring',
          description: 'CPU, memory, and network performance alerts.',
          button: false,
          enabled: false,
          connected: true,
        },
        {
          id: '7',
          icon: Bell,
          title: 'User Activity Alerts',
          description: 'Notifications for user management and access changes.',
          button: false,
          enabled: true,
          connected: true,
        },
      ];
      
      setNotifications(systemNotifications);
      setSystemAlertsEnabled(true);
    } catch (error) {
      toast.error('Failed to load system notifications');
      if (import.meta.env.VITE_DEBUG) {
        console.error('[SystemNotifications] Load failed:', error);
      }
    } finally {
      setLoading(false);
    }
  };

  const toggleNotification = async (id: string, enabled: boolean) => {
    try {
      // Update local state immediately for better UX
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id ? { ...notification, enabled } : notification
        )
      );
      
      // TODO: Implement system notification settings API when backend is ready
      // await hcmApi.updateSystemNotificationSetting(id, { enabled });
      toast.success(`${enabled ? 'Enabled' : 'Disabled'} system notification`);
      
      if (import.meta.env.VITE_DEBUG) {
        console.log(`[SystemNotifications] Toggled notification ${id} to ${enabled}`);
      }
    } catch (error) {
      toast.error('Failed to update notification');
      if (import.meta.env.VITE_DEBUG) {
        console.error('[SystemNotifications] Toggle failed:', error);
      }
    }
  };

  const toggleSystemAlerts = async (enabled: boolean) => {
    try {
      // Update system notification preferences using existing API
      await hcmApi.updateSettings({ system_alerts_enabled: enabled });
      setSystemAlertsEnabled(enabled);
      toast.success(`System alerts ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      toast.error('Failed to update system alerts');
      if (import.meta.env.VITE_DEBUG) {
        console.error('[SystemNotifications] System alerts toggle failed:', error);
      }
    }
  };

  const getNotificationActions = (notification: IChannelsItem) => {
    if (notification.title.includes('Invoice') || notification.title.includes('Billing')) {
      return (
        <Button variant="outline">
          <Link to="/account/billing">View Billing</Link>
        </Button>
      );
    }

    if (notification.title.includes('Server') || notification.title.includes('System')) {
      return (
        <Button variant="outline">
          <Link to="/account/servers">View Servers</Link>
        </Button>
      );
    }

    return (
      <Switch 
        id={`notification-${notification.id}`}
        size="sm" 
        checked={notification.enabled}
        onCheckedChange={(checked) => toggleNotification(notification.id, checked)}
      />
    );
  };

  const renderItem = (item: IChannelsItem, index: number) => {
    return (
      <CardNotification
        icon={item.icon}
        title={item.title}
        description={item.description}
        button={item.button}
        actions={getNotificationActions(item)}
        key={item.id}
      />
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="gap-2">
          <CardTitle>System Notifications</CardTitle>
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-6 w-10" />
          </div>
        </CardHeader>
        <div id="notifications_cards" className="space-y-4 p-4">
          {Array.from({ length: 7 }).map((_, i) => (
            <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Skeleton className="h-5 w-5" />
                <div>
                  <Skeleton className="h-4 w-20 mb-1" />
                  <Skeleton className="h-3 w-40" />
                </div>
              </div>
              <Skeleton className="h-6 w-10" />
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="gap-2">
        <CardTitle>System Notifications</CardTitle>
        <div className="flex items-center gap-2">
          <Label htmlFor="system-alerts" className="text-sm">
            System-Wide Alerts
          </Label>
          <Switch 
            id="system-alerts" 
            size="sm" 
            checked={systemAlertsEnabled}
            onCheckedChange={toggleSystemAlerts}
          />
        </div>
      </CardHeader>
      <div id="notifications_cards">
        {notifications.length > 0 ? (
          notifications.map((item, index) => {
            return renderItem(item, index);
          })
        ) : (
          <div className="text-center py-8 px-4 text-secondary-foreground">
            No system notifications configured. Set up server monitoring alerts.
          </div>
        )}
      </div>
    </Card>
  );
};

export { OtherNotifications };

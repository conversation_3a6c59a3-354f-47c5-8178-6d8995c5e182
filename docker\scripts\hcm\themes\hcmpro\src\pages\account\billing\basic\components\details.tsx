/**
 * H‑CareCloud Project – Billing Details Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { SquarePen } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';

interface IDetailsItem {
  status: string;
  info: string;
}
type IDetailsItems = Array<IDetailsItem>;

interface BillingDetails {
  company_name: string;
  address: string;
  contact_person: string;
  vat_id: string;
  phone?: string;
  email?: string;
}

const Details = () => {
  const [billingDetails, setBillingDetails] = useState<BillingDetails | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadBillingDetails();
  }, []);

  const loadBillingDetails = async () => {
    try {
      const details = await hcmApi.getBillingDetails();
      setBillingDetails(details);
    } catch (error) {
      toast.error('Failed to load billing details');
      logger.error('Failed to load billing details', {
        component: 'BillingDetails',
        action: 'loadDetails'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const getDetailsItems = (): IDetailsItems => {
    if (!billingDetails) {
      return [
        { status: 'Company Name', info: 'N/A' },
        { status: 'Address', info: 'N/A' },
        { status: 'Contact', info: 'N/A' },
        { status: 'VAT ID', info: 'N/A' },
      ];
    }

    return [
      { status: 'Company Name', info: billingDetails.company_name },
      { status: 'Address', info: billingDetails.address },
      { status: 'Contact', info: billingDetails.contact_person },
      { status: 'VAT ID', info: billingDetails.vat_id },
    ];
  };

  const renderItem = (table: IDetailsItem, index: number) => {
    return (
      <TableRow key={index} className="border-0">
        <TableCell className="text-sm text-secondary-foreground min-w-36 pb-5 pe-6 py-2">
          {table.status}
        </TableCell>
        <TableCell className="text-sm text-foreground pb-5 py-2">
          {table.info}
        </TableCell>
      </TableRow>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Billing Details</CardTitle>
          <Skeleton className="h-10 w-32" />
        </CardHeader>
        <CardContent className="pt-4 pb-2">
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-48" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const tables = getDetailsItems();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Billing Details</CardTitle>
        <Button variant="outline">
          <SquarePen size={16} />
          Edit Billing
        </Button>
      </CardHeader>
      <CardContent className="pt-4 pb-2">
        <Table>
          <TableBody>
            {tables.map((table, index) => {
              return renderItem(table, index);
            })}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export { Details, type IDetailsItem, type IDetailsItems };

/**
 * H‑CareCloud Project – Appearance Service
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { hcmApi } from './hcm-api';
import { logger } from '@/lib/logger';

export interface AppearanceSettings {
  theme_mode: 'light' | 'dark' | 'auto';
  primary_color: string;
  sidebar_style: 'default' | 'compact' | 'minimal';
  font_size: 'small' | 'medium' | 'large';
  animations_enabled: boolean;
  custom_logo?: string;
  company_name?: string;
  // HCM Layout specific settings
  sidebar_width?: number;
  sidebar_collapse_width?: number;
  header_height?: number;
  sidebar_transition_duration?: string;
  sidebar_fixed?: boolean;
  header_fixed?: boolean;
  sidebar_collapse?: boolean;
  // Complete HCMPro color system
  brand_colors?: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    foreground: string;
    card: string;
    card_foreground: string;
    popover: string;
    popover_foreground: string;
    muted: string;
    muted_foreground: string;
    destructive: string;
    destructive_foreground: string;
    mono: string;
    mono_foreground: string;
    border: string;
    input: string;
    ring: string;
  };
}

export interface ThemeSettings {
  theme_name: string;
  theme_mode: string;
  theme_layout: string;
  primary_color: string;
  secondary_color: string;
}

class AppearanceService {
  private settings: AppearanceSettings | null = null;
  private themeSettings: ThemeSettings | null = null;

  /**
   * Get current appearance settings for the authenticated user
   */
  async getAppearanceSettings(): Promise<AppearanceSettings> {
    try {
      if (!this.settings) {
        const response = await hcmApi.getAppearanceSettings();
        this.settings = response || null;

        logger.debug('Appearance settings loaded', {
          component: 'AppearanceService',
          action: 'getSettings',
          data: { theme_mode: this.settings?.theme_mode }
        });
      }

      return this.settings || this.getDefaultSettings();
    } catch (error) {
      logger.error('Failed to load appearance settings', {
        component: 'AppearanceService',
        action: 'getSettings'
      }, error as Error);

      return this.getDefaultSettings();
    }
  }

  /**
   * Update appearance settings
   */
  async updateAppearanceSettings(settings: Partial<AppearanceSettings>): Promise<void> {
    try {
      await hcmApi.updateAppearanceSettings(settings);

      // Update cached settings
      this.settings = { ...this.settings, ...settings } as AppearanceSettings;

      // Apply settings to DOM
      this.applySettings(this.settings);

      logger.debug('Appearance settings updated', {
        component: 'AppearanceService',
        action: 'updateSettings',
        data: settings
      });
    } catch (error) {
      logger.error('Failed to update appearance settings', {
        component: 'AppearanceService',
        action: 'updateSettings',
        data: settings
      }, error as Error);
      throw error;
    }
  }

  /**
   * Get global theme settings
   */
  async getThemeSettings(): Promise<ThemeSettings> {
    try {
      if (!this.themeSettings) {
        const response = await hcmApi.getThemeSettings();
        this.themeSettings = response || null;
      }

      return this.themeSettings || this.getDefaultThemeSettings();
    } catch (error) {
      logger.error('Failed to load theme settings', {
        component: 'AppearanceService',
        action: 'getThemeSettings'
      }, error as Error);

      return this.getDefaultThemeSettings();
    }
  }

  /**
   * Get dynamic logo URL based on user settings and theme mode
   */
  async getLogoUrl(isDark?: boolean): Promise<string> {
    try {
      const settings = await this.getAppearanceSettings();

      if (settings.custom_logo) {
        return settings.custom_logo;
      }

      // Fallback to company logo from user profile
      const profile = await hcmApi.getUserProfile();
      if (profile?.avatar) {
        return profile.avatar;
      }

      // Use actual existing HCMPro logos based on theme
      const themeMode = isDark ?? (settings.theme_mode === 'dark' ||
        (settings.theme_mode === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches));

      return themeMode
        ? '/media/app/default-logo-dark.svg'
        : '/media/app/default-logo.svg';
    } catch (error) {
      logger.error('Failed to get logo URL', {
        component: 'AppearanceService',
        action: 'getLogoUrl'
      }, error as Error);

      // Fallback to actual existing logo
      return '/media/app/default-logo.svg';
    }
  }

  /**
   * Get mini logo URL for compact layouts
   */
  async getMiniLogoUrl(variant: 'default' | 'primary' | 'success' | 'gray' = 'default', isDark?: boolean): Promise<string> {
    try {
      const settings = await this.getAppearanceSettings();
      const themeMode = isDark ?? (settings.theme_mode === 'dark' ||
        (settings.theme_mode === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches));

      const suffix = themeMode ? '-dark' : '';
      const variantSuffix = variant !== 'default' ? `-${variant}` : '';

      return `/media/app/mini-logo-circle${variantSuffix}${suffix}.svg`;
    } catch (error) {
      logger.error('Failed to get mini logo URL', {
        component: 'AppearanceService',
        action: 'getMiniLogoUrl'
      }, error as Error);

      return '/media/app/mini-logo-circle.svg';
    }
  }

  /**
   * Get dynamic company name
   */
  async getCompanyName(): Promise<string> {
    try {
      const settings = await this.getAppearanceSettings();
      
      if (settings.company_name) {
        return settings.company_name;
      }
      
      // Fallback to user profile company name
      const profile = await hcmApi.getUserProfile();
      if (profile?.company) {
        return profile.company;
      }
      
      return 'H-CareManager';
    } catch (error) {
      logger.error('Failed to get company name', {
        component: 'AppearanceService',
        action: 'getCompanyName'
      }, error as Error);
      
      return 'H-CareManager';
    }
  }

  /**
   * Apply appearance settings to DOM
   */
  private applySettings(settings: AppearanceSettings): void {
    const root = document.documentElement;

    root.setAttribute('data-theme', settings.theme_mode);
    root.style.setProperty('--primary', settings.primary_color);
    root.setAttribute('data-font-size', settings.font_size);
    root.setAttribute('data-sidebar-style', settings.sidebar_style);

    if (!settings.animations_enabled) {
      root.style.setProperty('--animation-duration', '0s');
    } else {
      root.style.removeProperty('--animation-duration');
    }

    if (settings.sidebar_width) {
      root.style.setProperty('--sidebar-width', `${settings.sidebar_width}px`);
    }

    if (settings.sidebar_collapse_width) {
      root.style.setProperty('--sidebar-width-collapse', `${settings.sidebar_collapse_width}px`);
    }

    if (settings.header_height) {
      root.style.setProperty('--header-height', `${settings.header_height}px`);
    }

    if (settings.sidebar_transition_duration) {
      root.style.setProperty('--sidebar-transition-duration', settings.sidebar_transition_duration);
    }

    if (settings.brand_colors) {
      const colors = settings.brand_colors;
      root.style.setProperty('--primary', colors.primary);
      root.style.setProperty('--secondary', colors.secondary);
      root.style.setProperty('--accent', colors.accent);
      root.style.setProperty('--background', colors.background);
      root.style.setProperty('--foreground', colors.foreground);
      root.style.setProperty('--card', colors.card);
      root.style.setProperty('--card-foreground', colors.card_foreground);
      root.style.setProperty('--popover', colors.popover);
      root.style.setProperty('--popover-foreground', colors.popover_foreground);
      root.style.setProperty('--muted', colors.muted);
      root.style.setProperty('--muted-foreground', colors.muted_foreground);
      root.style.setProperty('--destructive', colors.destructive);
      root.style.setProperty('--destructive-foreground', colors.destructive_foreground);
      root.style.setProperty('--mono', colors.mono);
      root.style.setProperty('--mono-foreground', colors.mono_foreground);
      root.style.setProperty('--border', colors.border);
      root.style.setProperty('--input', colors.input);
      root.style.setProperty('--ring', colors.ring);
    }
  }

  /**
   * Initialize appearance service
   */
  async initialize(): Promise<void> {
    try {
      const settings = await this.getAppearanceSettings();
      this.applySettings(settings);
      
      logger.debug('Appearance service initialized', {
        component: 'AppearanceService',
        action: 'initialize'
      });
    } catch (error) {
      logger.error('Failed to initialize appearance service', {
        component: 'AppearanceService',
        action: 'initialize'
      }, error as Error);
    }
  }

  /**
   * Get default appearance settings (using actual HCMPro theme colors)
   */
  private getDefaultSettings(): AppearanceSettings {
    return {
      theme_mode: 'dark',
      primary_color: '#1379f0',
      sidebar_style: 'default',
      font_size: 'medium',
      animations_enabled: true,
      sidebar_width: 280,
      sidebar_collapse_width: 80,
      header_height: 70,
      sidebar_transition_duration: '0.3s',
      sidebar_fixed: true,
      header_fixed: true,
      sidebar_collapse: false,
      brand_colors: {
        primary: '#1379f0',
        secondary: 'oklch(27.4% 0.006 286.033)',
        accent: 'oklch(21% 0.006 285.885)',
        background: 'oklch(14.1% 0.005 285.823)',
        foreground: 'oklch(98.5% 0 0)',
        card: 'oklch(14.1% 0.005 285.823)',
        card_foreground: 'oklch(98.5% 0 0)',
        popover: 'oklch(14.1% 0.005 285.823)',
        popover_foreground: 'oklch(98.5% 0 0)',
        muted: 'oklch(21% 0.006 285.885)',
        muted_foreground: 'oklch(55.2% 0.016 285.938)',
        destructive: 'oklch(57.7% 0.245 27.325)',
        destructive_foreground: 'oklch(1 0 0)',
        mono: 'oklch(87.1% 0.006 286.286)',
        mono_foreground: 'oklch(0 0 0)',
        border: 'oklch(27.4% 0.006 286.033)',
        input: 'oklch(27.4% 0.006 286.033)',
        ring: 'oklch(27.4% 0.006 286.033)',
      },
    };
  }

  /**
   * Get default theme settings (using actual HCMPro values)
   */
  private getDefaultThemeSettings(): ThemeSettings {
    return {
      theme_name: 'hcmpro',
      theme_mode: 'dark',
      theme_layout: 'hcmlayout1',
      primary_color: '#1379f0',
      secondary_color: 'oklch(27.4% 0.006 286.033)',
    };
  }

  /**
   * Clear cached settings (useful for logout)
   */
  clearCache(): void {
    this.settings = null;
    this.themeSettings = null;
  }
}

export const appearanceService = new AppearanceService();

/**
 * H‑CareCloud Project – Appearance Service
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { hcmApi } from './hcm-api';
import { logger } from '@/lib/logger';

export interface AppearanceSettings {
  theme_mode: 'light' | 'dark' | 'auto';
  primary_color: string;
  sidebar_style: 'default' | 'compact' | 'minimal';
  font_size: 'small' | 'medium' | 'large';
  animations_enabled: boolean;
  custom_logo?: string;
  company_name?: string;
  brand_colors?: {
    primary: string;
    secondary: string;
    accent: string;
  };
}

export interface ThemeSettings {
  theme_name: string;
  theme_mode: string;
  theme_layout: string;
  primary_color: string;
  secondary_color: string;
}

class AppearanceService {
  private settings: AppearanceSettings | null = null;
  private themeSettings: ThemeSettings | null = null;

  /**
   * Get current appearance settings for the authenticated user
   */
  async getAppearanceSettings(): Promise<AppearanceSettings> {
    try {
      if (!this.settings) {
        const response = await hcmApi.request<AppearanceSettings>('/api/user/appearance/settings');
        this.settings = response.data;
        
        logger.debug('Appearance settings loaded', {
          component: 'AppearanceService',
          action: 'getSettings',
          data: { theme_mode: this.settings?.theme_mode }
        });
      }
      
      return this.settings || this.getDefaultSettings();
    } catch (error) {
      logger.error('Failed to load appearance settings', {
        component: 'AppearanceService',
        action: 'getSettings'
      }, error as Error);
      
      return this.getDefaultSettings();
    }
  }

  /**
   * Update appearance settings
   */
  async updateAppearanceSettings(settings: Partial<AppearanceSettings>): Promise<void> {
    try {
      await hcmApi.request('/api/user/appearance/settings', {
        method: 'PUT',
        body: JSON.stringify(settings),
      });

      // Update cached settings
      this.settings = { ...this.settings, ...settings } as AppearanceSettings;
      
      // Apply settings to DOM
      this.applySettings(this.settings);
      
      logger.debug('Appearance settings updated', {
        component: 'AppearanceService',
        action: 'updateSettings',
        data: settings
      });
    } catch (error) {
      logger.error('Failed to update appearance settings', {
        component: 'AppearanceService',
        action: 'updateSettings',
        data: settings
      }, error as Error);
      throw error;
    }
  }

  /**
   * Get global theme settings
   */
  async getThemeSettings(): Promise<ThemeSettings> {
    try {
      if (!this.themeSettings) {
        const response = await hcmApi.request<ThemeSettings>('/api/system/theme/settings');
        this.themeSettings = response.data;
      }
      
      return this.themeSettings || this.getDefaultThemeSettings();
    } catch (error) {
      logger.error('Failed to load theme settings', {
        component: 'AppearanceService',
        action: 'getThemeSettings'
      }, error as Error);
      
      return this.getDefaultThemeSettings();
    }
  }

  /**
   * Get dynamic logo URL based on user settings
   */
  async getLogoUrl(): Promise<string> {
    try {
      const settings = await this.getAppearanceSettings();
      
      if (settings.custom_logo) {
        return settings.custom_logo;
      }
      
      // Fallback to company logo
      const profile = await hcmApi.getUserProfile();
      if (profile.company_logo) {
        return profile.company_logo;
      }
      
      // Default H-CareManager logo
      return '/media/logos/hcaremanager-logo.svg';
    } catch (error) {
      logger.error('Failed to get logo URL', {
        component: 'AppearanceService',
        action: 'getLogoUrl'
      }, error as Error);
      
      return '/media/logos/hcaremanager-logo.svg';
    }
  }

  /**
   * Get dynamic company name
   */
  async getCompanyName(): Promise<string> {
    try {
      const settings = await this.getAppearanceSettings();
      
      if (settings.company_name) {
        return settings.company_name;
      }
      
      // Fallback to user profile company name
      const profile = await hcmApi.getUserProfile();
      if (profile.company_name) {
        return profile.company_name;
      }
      
      return 'H-CareManager';
    } catch (error) {
      logger.error('Failed to get company name', {
        component: 'AppearanceService',
        action: 'getCompanyName'
      }, error as Error);
      
      return 'H-CareManager';
    }
  }

  /**
   * Apply appearance settings to DOM
   */
  private applySettings(settings: AppearanceSettings): void {
    const root = document.documentElement;
    
    // Apply theme mode
    root.setAttribute('data-theme', settings.theme_mode);
    
    // Apply primary color
    root.style.setProperty('--primary-color', settings.primary_color);
    
    // Apply font size
    root.setAttribute('data-font-size', settings.font_size);
    
    // Apply sidebar style
    root.setAttribute('data-sidebar-style', settings.sidebar_style);
    
    // Apply animations
    if (!settings.animations_enabled) {
      root.style.setProperty('--animation-duration', '0s');
    } else {
      root.style.removeProperty('--animation-duration');
    }
    
    // Apply brand colors if available
    if (settings.brand_colors) {
      root.style.setProperty('--brand-primary', settings.brand_colors.primary);
      root.style.setProperty('--brand-secondary', settings.brand_colors.secondary);
      root.style.setProperty('--brand-accent', settings.brand_colors.accent);
    }
  }

  /**
   * Initialize appearance service
   */
  async initialize(): Promise<void> {
    try {
      const settings = await this.getAppearanceSettings();
      this.applySettings(settings);
      
      logger.debug('Appearance service initialized', {
        component: 'AppearanceService',
        action: 'initialize'
      });
    } catch (error) {
      logger.error('Failed to initialize appearance service', {
        component: 'AppearanceService',
        action: 'initialize'
      }, error as Error);
    }
  }

  /**
   * Get default appearance settings
   */
  private getDefaultSettings(): AppearanceSettings {
    return {
      theme_mode: 'dark',
      primary_color: '#2563EB',
      sidebar_style: 'default',
      font_size: 'medium',
      animations_enabled: true,
    };
  }

  /**
   * Get default theme settings
   */
  private getDefaultThemeSettings(): ThemeSettings {
    return {
      theme_name: 'hcmpro',
      theme_mode: 'dark',
      theme_layout: 'hcmlayout1',
      primary_color: '#2563EB',
      secondary_color: '#64748B',
    };
  }

  /**
   * Clear cached settings (useful for logout)
   */
  clearCache(): void {
    this.settings = null;
    this.themeSettings = null;
  }
}

export const appearanceService = new AppearanceService();

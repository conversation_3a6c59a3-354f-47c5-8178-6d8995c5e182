/**
 * H‑CareCloud Project – Production Logger Utility
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

/**
 * Production-ready logging system for H-CareManager
 * 
 * Environment Variables:
 * - VITE_DEBUG: Enable/disable debug logging (true/false)
 * - VITE_DEV: Development mode flag (true/false)
 * 
 * Usage:
 * - Production: Only errors and warnings are logged
 * - Development + Debug: All logs including debug info are shown
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogContext {
  component?: string;
  action?: string;
  userId?: string | number;
  data?: any;
}

class HCMLogger {
  private isDevelopment: boolean;
  private isDebugEnabled: boolean;

  constructor() {
    this.isDevelopment = import.meta.env.VITE_DEV === 'true';
    this.isDebugEnabled = import.meta.env.VITE_DEBUG === 'true';
  }

  private shouldLog(level: LogLevel): boolean {
    // In production, only log warnings and errors
    if (!this.isDevelopment) {
      return level === 'warn' || level === 'error';
    }

    // In development, log everything if debug is enabled
    if (this.isDebugEnabled) {
      return true;
    }

    // In development without debug, log info, warn, and error
    return level !== 'debug';
  }

  private formatMessage(level: LogLevel, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const prefix = `[HCM-${level.toUpperCase()}] ${timestamp}`;
    
    if (context?.component) {
      return `${prefix} [${context.component}] ${message}`;
    }
    
    return `${prefix} ${message}`;
  }

  private logWithContext(level: LogLevel, message: string, context?: LogContext, error?: Error) {
    if (!this.shouldLog(level)) return;

    const formattedMessage = this.formatMessage(level, message, context);
    
    switch (level) {
      case 'debug':
        console.debug(formattedMessage, context?.data || '', error || '');
        break;
      case 'info':
        console.info(formattedMessage, context?.data || '');
        break;
      case 'warn':
        console.warn(formattedMessage, context?.data || '', error || '');
        break;
      case 'error':
        console.error(formattedMessage, context?.data || '', error || '');
        break;
    }
  }

  /**
   * Debug logging - only shown in development with VITE_DEBUG=true
   */
  debug(message: string, context?: LogContext) {
    this.logWithContext('debug', message, context);
  }

  /**
   * Info logging - shown in development mode
   */
  info(message: string, context?: LogContext) {
    this.logWithContext('info', message, context);
  }

  /**
   * Warning logging - shown in all environments
   */
  warn(message: string, context?: LogContext, error?: Error) {
    this.logWithContext('warn', message, context, error);
  }

  /**
   * Error logging - shown in all environments
   */
  error(message: string, context?: LogContext, error?: Error) {
    this.logWithContext('error', message, context, error);
  }

  /**
   * API call logging - debug level
   */
  apiCall(method: string, endpoint: string, data?: any) {
    this.debug(`API ${method.toUpperCase()} ${endpoint}`, {
      component: 'API',
      action: 'request',
      data: data
    });
  }

  /**
   * API response logging - debug level
   */
  apiResponse(method: string, endpoint: string, status: number, data?: any) {
    this.debug(`API ${method.toUpperCase()} ${endpoint} - ${status}`, {
      component: 'API',
      action: 'response',
      data: data
    });
  }

  /**
   * User action logging - info level
   */
  userAction(action: string, userId: string | number, data?: any) {
    this.info(`User action: ${action}`, {
      component: 'USER',
      action: action,
      userId: userId.toString(),
      data: data
    });
  }
}

// Export singleton instance
export const logger = new HCMLogger();

// Export types for use in other files
export type { LogContext, LogLevel };

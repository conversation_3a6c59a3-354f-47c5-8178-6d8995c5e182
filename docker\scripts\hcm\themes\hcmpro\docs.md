# H-CareManager Documentation Hub

## Overview
This is the main documentation hub for H-CareManager, a React TypeScript dashboard for H-CareCloud Hospital Management System server management and setup wizard. This document serves as the central reference point for all project documentation.

**Project Context**: H-CareManager is a production-ready healthcare system control panel for server staff and developers, NOT a hospital interface.

## Project Architecture

### Complete System Structure
```
H-CareCloud/
├── docker/                          # Docker infrastructure
│   ├── backups/                     # 📋 Backup management system
│   ├── fpm/                         # 📋 PHP-FPM configuration
│   ├── logs/                        # 📋 Log management system
│   ├── manager/                     # 📋 Manager container setup
│   ├── mysql/                       # ✅ Database configuration (README.md ✓)
│   ├── nginx/                       # 📋 Web server configuration
│   ├── nodeJS/                      # 📋 Node.js container setup
│   ├── phpmyadmin/                  # 📋 phpMyAdmin configuration
│   ├── redis/                       # 📋 Redis cache configuration
│   └── scripts/                     # 📋 Utility scripts
│       └── hcm/                     # H-CareManager core
│           ├── api/                 # ✅ Flask API endpoints (README.md ✓)
│           ├── hcc_pymodules/       # ✅ Shared Python modules (README.md ✓)
│           ├── utils/               # 📋 Utility functions
│           └── themes/hcmpro/       # React TypeScript frontend
│               └── src/
│                   ├── auth/        # 📋 Authentication system
│                   ├── components/  # 📋 UI components
│                   ├── hooks/       # 📋 Custom React hooks
│                   ├── lib/         # 📋 Utility libraries
│                   ├── pages/       # 📋 Application pages
│                   │   └── account/ # ✅ Account management (customized)
│                   ├── providers/   # 📋 React context providers
│                   ├── routing/     # 📋 Application routing
│                   └── services/    # 📋 API integration services
└── [H-CareCloud HMS files...]       # Main hospital management system
```

**Legend**: ✅ = Documentation complete, 📋 = Documentation needed

## Documentation Structure

### Core Documentation Files
- **This file (docs.md)**: Main documentation hub and project overview
- **README.md files**: Detailed documentation for each major folder
- **CHANGELOG.md files**: Change tracking for each major folder
- **rules.md**: Development rules and standards (in .augment/rules/)

### Docker Infrastructure Documentation
| Folder | README.md | CHANGELOG.md | Purpose |
|--------|-----------|--------------|---------|
| `/docker/backups/` | 📋 Needed | 📋 Needed | Backup management and recovery |
| `/docker/fpm/` | 📋 Needed | 📋 Needed | PHP-FPM configuration and optimization |
| `/docker/logs/` | 📋 Needed | 📋 Needed | Log aggregation and management |
| `/docker/manager/` | 📋 Needed | 📋 Needed | Manager container setup and configuration |
| `/docker/mysql/` | ✅ Complete | ✅ Complete | Database schema and configuration |
| `/docker/nginx/` | 📋 Needed | 📋 Needed | Web server configuration and routing |
| `/docker/nodeJS/` | 📋 Needed | 📋 Needed | Node.js container and build process |
| `/docker/phpmyadmin/` | 📋 Needed | 📋 Needed | Database administration interface |
| `/docker/redis/` | 📋 Needed | 📋 Needed | Cache configuration and management |
| `/docker/scripts/` | 📋 Needed | 📋 Needed | Utility scripts and automation |

### Backend Documentation
| Folder | README.md | CHANGELOG.md | Purpose |
|--------|-----------|--------------|---------|
| `/api/` | ✅ Complete | ✅ Complete | Flask API endpoints and authentication |
| `/hcc_pymodules/` | ✅ Complete | ✅ Complete | Shared Python modules and utilities |
| `/utils/` | 📋 Needed | 📋 Needed | Backend utility functions |

### Frontend Documentation
| Folder | README.md | CHANGELOG.md | Purpose |
|--------|-----------|--------------|---------|
| `/src/auth/` | 📋 Needed | 📋 Needed | Authentication system and HCM auth provider |
| `/src/components/` | 📋 Needed | 📋 Needed | Reusable UI components and Metronic integration |
| `/src/hooks/` | 📋 Needed | 📋 Needed | Custom React hooks for API and state management |
| `/src/lib/` | 📋 Needed | 📋 Needed | Utility libraries (logger, storage, helpers) |
| `/src/pages/account/` | ✅ Customized | 📋 Needed | Account management modules (completed) |
| `/src/providers/` | 📋 Needed | 📋 Needed | React context providers and state management |
| `/src/routing/` | 📋 Needed | 📋 Needed | Application routing and navigation |
| `/src/services/` | 📋 Needed | 📋 Needed | API integration and service layer |

## Customization Progress

### Completed Customizations (2025-07-22)
- ✅ **Account Pages**: All 9 account modules completed with production-ready standards
  - security/, servers/, api-keys/, billing/, notifications/, integrations/, appearance/, home/, activity/
  - Real API integration, TypeScript compliance, logger integration, security standards
  - Files renamed properly (members.tsx → server-admins.tsx), all references updated
  - Comprehensive fixes applied (console.log → logger, mock data → real APIs)

### Current Development Status
- 🔄 **Backend API Verification**: Systematically verifying Python APIs match frontend expectations
- 🔄 **Professional Documentation**: Creating README.md and CHANGELOG.md for all major folders
- 📋 **Pending**: Complete backend-frontend integration testing

### Template Customization Approach
1. **Read COMPLETE files** (300+ lines) before any edits
2. **Replace ALL mock data** with real API calls
3. **Fix ALL issues** in single comprehensive edit (TypeScript, console.log, hardcoded values)
4. **Server management context** - Convert social/staff context to server management
5. **Production-ready standards** - Security, error handling, logging, HIPAA compliance

## Technology Stack

### Frontend
- **Framework**: React 18 + TypeScript + TSX (NO JSX)
- **Build Tool**: Vite with development and production modes
- **Styling**: Tailwind CSS with Metronic HCCManagerPro theme (demo3 layout)
- **State Management**: React Context + Custom hooks
- **Authentication**: Custom HCM authentication system (NOT Supabase)

### Backend
- **API Framework**: Python Flask with Blueprint architecture
- **Database**: MySQL with multi-database architecture
- **Authentication**: Token-based with session management
- **Configuration**: Centralized via hcc_config and environment variables

### Infrastructure
- **Containerization**: Docker with multi-service architecture
- **Web Server**: Nginx with custom configuration
- **Cache**: Redis for session and data caching
- **Database Admin**: phpMyAdmin for database management

## Development Standards

### File Reading Requirements (CRITICAL)
- **NEVER edit ANY file without reading it COMPLETELY first**
- **Treat every file touch as a complete task** - fix ALL issues in single edit
- **Apply comprehensive fixes** - TypeScript errors, console.log, mock data, hardcoded values
- **Rename files/components properly** and update ALL references

### Security Standards (HIPAA Compliance)
- **Audit all data access** - Log every database operation
- **Encrypt sensitive data** - Use proper encryption for PII/PHI
- **Session security** - Secure session management with IP tracking
- **Access controls** - Role-based permissions with principle of least privilege

### Documentation Standards
- **Update README.md and CHANGELOG.md** whenever editing files in a folder
- **Use correct date format**: 2025-07-22 (not 2025-01-21)
- **Complete structure graphs** showing everything we've been working on
- **Professional coding standards** - not optional

## Environment Configuration

### Required Environment Variables
```bash
# Database Configuration
MYSQL_HOST=mysql
MYSQL_USER=hcarecloud_user
MYSQL_PASSWORD=secure_password
MYSQL_DATABASE=hcarecloud
MYSQL_MANAGER_DATABASE=hcarecloud_manager

# H-CareManager API Configuration
VITE_APP_API_URL=http://localhost:5000
VITE_HMS_API_URL=http://localhost:8000
VITE_DEBUG=true
VITE_DEV=true
VITE_TENANT_ID=default
VITE_API_TIMEOUT=30000
VITE_MAX_FILE_SIZE=10485760

# Security Configuration
SECRET_KEY=your-secret-key
JWT_SECRET=jwt-secret-key
FLASK_ENV=development
```

## Quick Start Guide

### Development Setup
1. **Start Docker services**: `docker-compose up -d`
2. **Initialize database**: Run `init-db.sh` script
3. **Start frontend**: `npm run dev` in hcmpro directory
4. **Start backend**: `python hcc_manager.py` in hcm directory

### Testing
- **Frontend**: `npm run build` for production build
- **Backend**: Test API endpoints with authentication tokens
- **Database**: Verify all tables exist and relationships are correct

## Related Documentation

### External References
- **Metronic Documentation**: Theme components and styling guidelines
- **React Documentation**: React 18 features and best practices
- **Flask Documentation**: API development and Blueprint architecture
- **MySQL Documentation**: Database optimization and security

### Internal Documentation Links
- [Database Schema Documentation](../../../mysql/README.md)
- [API Endpoints Documentation](../../api/README.md)
- [Python Modules Documentation](../../hcc_pymodules/README.md)
- [Development Rules and Standards](../../../../../.augment/rules/rules.md)

---

**Last Updated**: 2025-07-22  
**Version**: 1.0.8  
**Maintainer**: H-CareCloud Development Team

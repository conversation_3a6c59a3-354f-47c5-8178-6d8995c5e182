/**
 * H‑CareCloud Project – Server Management Row Card Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { CircleCheck, Server } from 'lucide-react';
import { Link } from 'react-router';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { AvatarGroup } from '../common/avatar-group';
import { Rating } from '../common/rating';
import { IServerProps } from './card-server';

const CardServerRow = ({
  icon: Icon,
  title,
  description,
  labels,
  rating,
  admins,
  connected,
}: IServerProps) => {
  const renderItem = (label: string, index: number) => {
    return (
      <Badge key={index} size="md" variant="secondary" appearance="outline">
        {label}
      </Badge>
    );
  };

  return (
    <Card className="p-7.5">
      <div className="flex flex-wrap justify-between items-center gap-7">
        <div className="flex items-center gap-4">
          <div className="flex justify-center items-center size-14 shrink-0 rounded-full ring-1 ring-input bg-accent/60">
            <Icon size={16} className="text-2xl text-secondary-foreground" />
          </div>
          <div className="grid grid-col gap-1">
            <Link
              to="#"
              className="text-base font-medium text-mono hover:text-primary-active mb-px"
            >
              {title}
            </Link>
            <span className="text-sm text-secondary-foreground">
              {description}
            </span>
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-6 lg:gap-12">
          <div className="grid gap-5 justify-end lg:text-end">
            <span className="text-xs font-normal text-muted-foreground uppercase">
              services
            </span>
            <div className="flex gap-1.5">
              {labels.map((label: string, index: number) => {
                return renderItem(label, index);
              })}
            </div>
          </div>
          <div className="grid justify-end gap-6 lg:text-end">
            <div className="text-xs text-secondary-foreground uppercase">
              performance
            </div>
            <Rating rating={rating.value} round={rating.round} />
          </div>
          <div className="grid justify-end gap-3.5 lg:text-end lg:min-w-24 shrink-0 max-w-auto">
            <span className="text-xs text-secondary-foreground uppercase">
              admins
            </span>
            <AvatarGroup
              group={admins.group}
              more={admins.more}
              className={admins.className}
              size={admins.size}
            />
          </div>
          <div className="grid justify-end min-w-20">
            {connected ? (
              <Button variant="outline">
                <Link to="#">
                  <CircleCheck size={16} />
                </Link>{' '}
                Connected
              </Button>
            ) : (
              <Button variant="primary">
                <Link to="#">
                  <Server size={16} />
                </Link>{' '}
                Connect
              </Button>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export { CardServerRow };

/**
 * H‑CareCloud Project – Payment Methods Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { SquarePen, SquarePlus, Trash2 } from 'lucide-react';
import { Link } from 'react-router';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { toAbsoluteUrl } from '@/lib/helpers';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface IPaymentMethodsItem {
  id: string;
  type: 'card' | 'paypal' | 'bank' | 'mtn_momo';
  provider: string;
  last_four?: string;
  expiry_date?: string;
  email?: string;
  phone_number?: string;
  is_primary: boolean;
  status: 'active' | 'inactive' | 'expired';
  created_at: string;
}
type IPaymentMethodsItems = Array<IPaymentMethodsItem>;

const PaymentMethods = () => {
  const [paymentMethods, setPaymentMethods] = useState<IPaymentMethodsItems>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadPaymentMethods();
  }, []);

  const loadPaymentMethods = async () => {
    try {
      const methods = await hcmApi.getPaymentMethods();
      setPaymentMethods(methods);
    } catch (error) {
      toast.error('Failed to load payment methods');
      logger.error('Failed to load payment methods', {
        component: 'PaymentMethods',
        action: 'loadMethods'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const getProviderLogo = (provider: string, type: string) => {
    const logos: Record<string, string> = {
      visa: 'visa.svg',
      mastercard: 'visa.svg',
      paypal: 'paypal.svg',
      stripe: 'stripe.svg',
      ideal: 'ideal.svg',
      'mtn mobile money': 'mtn-momo.svg',
      'mtn_momo': 'mtn-momo.svg',
    };

    // For MTN MoMo, use specific logo
    if (type === 'mtn_momo') {
      return 'mtn-momo.svg';
    }

    return logos[provider.toLowerCase()] || 'visa.svg';
  };

  const formatPaymentInfo = (method: IPaymentMethodsItem) => {
    if (method.type === 'card' && method.last_four && method.expiry_date) {
      return `Ending ${method.last_four}    Expires on ${method.expiry_date}`;
    }
    if (method.type === 'paypal' && method.email) {
      return method.email;
    }
    if (method.type === 'mtn_momo' && method.phone_number) {
      return `Mobile: ${method.phone_number}`;
    }
    return `${method.provider} Payment Method`;
  };

  const renderItem = (item: IPaymentMethodsItem) => {
    return (
      <div
        key={item.id}
        className="flex items-center justify-between border border-border rounded-xl gap-2 px-4 py-4 bg-secondary-clarity"
      >
        <div className="flex items-center gap-3.5">
          <img
            src={toAbsoluteUrl(`/media/brand-logos/${getProviderLogo(item.provider, item.type)}`)}
            className="w-10 shrink-0"
            alt={item.provider}
          />
          <div className="flex flex-col">
            <Link
              to="#"
              className="text-sm font-medium text-mono hover:text-primary-active mb-px"
            >
              {item.provider}
            </Link>
            <span className="text-sm text-secondary-foreground">
              {formatPaymentInfo(item)}
            </span>
          </div>
        </div>
        <div className="flex items-center gap-5">
          {item.is_primary && (
            <Badge variant="success" appearance="outline">
              Primary
            </Badge>
          )}
          <div className="flex gap-0.5">
            <Button variant="ghost">
              <SquarePen />
            </Button>
            <Button variant="ghost">
              <Trash2 />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card className="grow">
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
          <Skeleton className="h-10 w-24" />
        </CardHeader>
        <CardContent className="lg:pb-7.5">
          <div className="grid gap-5">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between border border-border rounded-xl gap-2 px-4 py-4 bg-secondary-clarity">
                <div className="flex items-center gap-3.5">
                  <Skeleton className="w-10 h-6" />
                  <div className="flex flex-col gap-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                </div>
                <div className="flex items-center gap-5">
                  <Skeleton className="h-6 w-16" />
                  <div className="flex gap-0.5">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="grow">
      <CardHeader>
        <CardTitle>Payment Methods</CardTitle>
        <Button variant="outline">
          <SquarePlus size={16} />
          Add New
        </Button>
      </CardHeader>
      <CardContent className="lg:pb-7.5">
        <div className="grid gap-5">
          {paymentMethods.length > 0 ? (
            paymentMethods.map((item) => {
              return renderItem(item);
            })
          ) : (
            <div className="text-center py-8 text-secondary-foreground">
              No payment methods found. Add your first payment method to get started.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export { PaymentMethods, type IPaymentMethodsItem, type IPaymentMethodsItems };

/**
 * H‑CareCloud Project – Staff Connections Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { logger } from '@/lib/logger';
import { DropdownMenu4 } from '@/partials/dropdown-menu/dropdown-menu-4';
import { DropdownMenu5 } from '@/partials/dropdown-menu/dropdown-menu-5';
import { Check, EllipsisVertical, Plus } from 'lucide-react';
import { Link } from 'react-router';
import { toAbsoluteUrl } from '@/lib/helpers';
import { Skeleton } from '@/components/ui/skeleton';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';

interface IConnectionsItem {
  id: string;
  avatar: string;
  name: string;
  email: string;
  department: string;
  connections: number;
  jointLinks: number;
  connected: boolean;
  status: 'active' | 'inactive' | 'away';
}
type IConnectionsItems = Array<IConnectionsItem>;

interface IConnectionsProps {
  url: string;
}

const Connections = ({ url }: IConnectionsProps) => {
  const [items, setItems] = useState<IConnectionsItems>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadConnections();
  }, []);

  const loadConnections = async () => {
    try {
      const connections = await hcmApi.getStaffConnections();
      setItems(connections);
    } catch (error) {
      toast.error('Failed to load staff connections');
      logger.error('Failed to load server connections', {
        component: 'Connections',
        action: 'loadConnections'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const toggleConnection = async (id: string, currentStatus: boolean) => {
    try {
      const newStatus = !currentStatus;
      await hcmApi.updateStaffConnection(id, { connected: newStatus });
      setItems((prev) =>
        prev.map((item) =>
          item.id === id ? { ...item, connected: newStatus } : item,
        ),
      );
      toast.success(newStatus ? 'Staff member connected' : 'Staff member disconnected');
    } catch (error) {
      toast.error('Failed to update connection');
      logger.error('Failed to update server connection', {
        component: 'Connections',
        action: 'toggleConnection',
        data: { itemId, newStatus }
      }, error as Error);
    }
  };

  const renderItem = (item: IConnectionsItem) => (
    <TableRow key={item.id}>
      <TableCell>
        <div className="flex items-center grow gap-2.5">
          <img
            src={toAbsoluteUrl(`/media/avatars/${item.avatar}`)}
            className="rounded-full size-9 shrink-0"
            alt={item.name}
          />
          <div className="flex flex-col gap-1">
            <Link
              to="/public-profile/profiles/creator"
              className="text-sm font-medium text-mono hover:text-primary-active mb-px"
            >
              {item.name}
            </Link>
            <span className="text-xs font-normal text-secondary-foreground leading-3">
              {item.department} • {item.connections} connections
            </span>
          </div>
        </div>
      </TableCell>
      <TableCell className="py-2 text-end">{item.jointLinks || 'none'}</TableCell>
      <TableCell className="py-2 text-end">
        <Button
          className={`rounded-full ${
            item.connected
              ? 'bg-blue-500 text-white'
              : 'bg-blue-50 border border-blue-300 text-blue-600 hover:text-white hover:bg-blue-500'
          }`}
          size="sm"
          mode="icon"
          variant={item.connected ? 'primary' : 'outline'}
          onClick={() => toggleConnection(item.id, item.connected)}
        >
          {item.connected ? <Check size={18} /> : <Plus size={18} />}
        </Button>
      </TableCell>
      <TableCell className="text-end">
        <DropdownMenu5
          trigger={
            <Button variant="ghost" mode="icon">
              <EllipsisVertical />
            </Button>
          }
        />
      </TableCell>
    </TableRow>
  );

  if (loading) {
    return (
      <Card className="min-w-full">
        <CardHeader>
          <CardTitle>Staff Connections</CardTitle>
          <Skeleton className="h-8 w-8" />
        </CardHeader>
        <CardContent className="kt-scrollable-x-auto p-0">
          <div className="space-y-3 p-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="flex items-center gap-3">
                <Skeleton className="h-9 w-9 rounded-full" />
                <div className="flex-1">
                  <Skeleton className="h-4 w-24 mb-1" />
                  <Skeleton className="h-3 w-32" />
                </div>
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-8 w-8" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="min-w-full">
      <CardHeader>
        <CardTitle>Staff Connections</CardTitle>
        <DropdownMenu4
          trigger={
            <Button variant="ghost" mode="icon">
              <EllipsisVertical />
            </Button>
          }
        />
      </CardHeader>
      <CardContent className="kt-scrollable-x-auto p-0">
        <div className="kt-scrollable-auto">
          <Table className="align-middle text-sm text-secondary-foreground">
            <TableBody>
              <TableRow className="bg-accent/60">
                <TableCell className="text-start font-normal min-w-48 py-2.5">
                  Staff Member
                </TableCell>
                <TableCell className="text-end font-medium min-w-20 py-2.5">
                  Joint Links
                </TableCell>
                <TableCell className="text-end font-medium min-w-20 py-2.5">
                  Status
                </TableCell>
                <TableCell className="min-w-16" />
              </TableRow>
              {items.length > 0 ? (
                items.map(renderItem)
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-8 text-secondary-foreground">
                    No staff connections found. Connect with your team members.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <CardFooter className="justify-center">
        <Button mode="link" underlined="dashed" asChild>
          <Link to={url}>View All Staff</Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export {
  Connections,
  type IConnectionsItem,
  type IConnectionsItems,
  type IConnectionsProps,
};

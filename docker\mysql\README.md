# H-CareCloud MySQL Database Configuration

## Overview
This directory contains the MySQL database configuration and initialization scripts for the H-CareCloud Hospital Management System. The database setup supports a multi-database architecture with separate databases for the main HMS system, H-CareManager, and phpMyAdmin configuration storage.

## Architecture

### Multi-Database Structure
```
MySQL Server
├── hcarecloud_manager     # H-CareManager specific data
├── ${MYSQL_DATABASE}      # Main H-CareCloud HMS database
└── phpmyadmin            # phpMyAdmin configuration storage
```

### Database Purposes
- **hcarecloud_manager**: Authentication, settings, notifications, billing for H-CareManager
- **${MYSQL_DATABASE}**: Patient records, medical data, hospital operations (main HMS)
- **phpmyadmin**: phpMyAdmin user preferences and configuration storage

## Database Schema

### H-CareManager Database (hcarecloud_manager)

#### Core Tables
- **users**: User accounts and authentication
- **user_profiles**: Extended user profile information
- **company_profiles**: Company/organization profiles
- **user_sessions**: Session management and tracking
- **api_tokens**: API authentication tokens

#### Security Tables
- **user_security_settings**: Security preferences
- **two_factor_secrets**: 2FA authentication data
- **password_reset_tokens**: Password reset functionality
- **email_verification_tokens**: Email verification
- **security_devices**: Trusted device management
- **allowed_ip_addresses**: IP whitelist management
- **audit_logs**: Security audit trail

#### Application Tables
- **settings**: Application configuration
- **theme_settings**: UI theme configuration
- **notifications**: User notifications
- **user_notification_settings**: Notification preferences
- **activity_log**: User activity tracking

#### Billing Tables
- **billing_plans**: Available subscription plans
- **user_subscriptions**: User billing subscriptions
- **billing_history**: Payment and invoice history
- **payment_methods**: Stored payment methods
- **billing_details**: Billing contact information

#### Integration Tables
- **integrations**: External service integrations
- **server_integrations**: Server management integrations
- **notification_channels**: Notification delivery channels
- **system_notifications**: System-wide notifications

#### Server Management Tables
- **departments**: Server departments/groups
- **roles**: User roles and permissions
- **user_roles**: Role assignments
- **staff_connections**: Server administrator connections

## Initialization Process

### init-db.sh Script
The `docker-entrypoint-initdb.d/init-db.sh` script is the **single source of truth** for database schema. It performs:

1. **Database Creation**: Creates all required databases
2. **User Management**: Creates database users with proper permissions
3. **Schema Creation**: Creates all tables with proper relationships
4. **Data Seeding**: Inserts default data and sample records
5. **Security Setup**: Configures security settings and audit logging

### Execution Flow
```bash
1. Create databases (hcarecloud_manager, ${MYSQL_DATABASE}, phpmyadmin)
2. Create database users with appropriate permissions
3. Initialize H-CareManager schema with all tables
4. Insert default data (admin user, billing plans, roles, etc.)
5. Create sample data for testing and development
6. Set up security configurations
```

### Default Data
The initialization script creates:
- **Admin User**: `<EMAIL>` (password: `password`)
- **Billing Plans**: Starter, Professional, Enterprise plans
- **Departments**: IT, Development, Support, Management
- **Roles**: Super Admin, Admin, Manager, Developer, Support, User
- **Sample Integrations**: Docker Hub, H-CareCloud HMS, System Monitoring
- **Notification Channels**: Email, Mobile, Slack, Desktop

## Security Configuration

### User Permissions
```sql
-- H-CareManager database access
GRANT ALL PRIVILEGES ON `hcarecloud_manager`.* TO 'hcm_manager'@'%';
GRANT ALL PRIVILEGES ON `hcarecloud_manager`.* TO '${MYSQL_USER}'@'%';

-- Main HMS database access
GRANT ALL PRIVILEGES ON `${MYSQL_DATABASE}`.* TO '${MYSQL_USER}'@'%';

-- phpMyAdmin access
GRANT ALL PRIVILEGES ON `phpmyadmin`.* TO '${MYSQL_USER}'@'%';
```

### HIPAA Compliance
- **Audit Logging**: All data access logged to `audit_logs` table
- **Data Encryption**: Sensitive fields properly encrypted
- **Access Control**: Role-based permissions with principle of least privilege
- **Session Security**: Secure session management with IP tracking
- **Password Security**: Proper password hashing (MD5 for compatibility, bcrypt for new)

### Foreign Key Constraints
All tables use proper foreign key relationships to maintain data integrity:
- User-related tables reference `users.id`
- Billing tables maintain referential integrity
- Security tables properly cascade on user deletion
- Audit logs preserve data even after user deletion

## Environment Variables

### Required Variables
```bash
MYSQL_ROOT_PASSWORD=your_root_password
MYSQL_DATABASE=hcarecloud_main
MYSQL_USER=hcarecloud_user
MYSQL_PASSWORD=your_user_password
```

### Database Connection
```bash
# H-CareManager database
MYSQL_MANAGER_HOST=mysql
MYSQL_MANAGER_DATABASE=hcarecloud_manager
MYSQL_MANAGER_USER=hcm_manager
MYSQL_MANAGER_PASSWORD=hcm_secure_2025

# Main HMS database
MYSQL_HMS_HOST=mysql
MYSQL_HMS_DATABASE=${MYSQL_DATABASE}
MYSQL_HMS_USER=${MYSQL_USER}
MYSQL_HMS_PASSWORD=${MYSQL_PASSWORD}
```

## Development Guidelines

### Schema Changes
1. **NEVER modify tables directly** - Always update `init-db.sh`
2. **Test changes locally** before deploying
3. **Backup data** before schema modifications
4. **Update API code** to match schema changes
5. **Document changes** in this README and CHANGELOG

### Adding New Tables
```sql
-- Template for new table
CREATE TABLE IF NOT EXISTS `new_table` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `new_table_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### Data Migration
For production data migrations:
1. Create backup before migration
2. Test migration on staging environment
3. Use transactions for data safety
4. Verify data integrity after migration
5. Update application code if needed

## Backup and Recovery

### Automated Backups
```bash
# Database backup
mysqldump -u root -p${MYSQL_ROOT_PASSWORD} hcarecloud_manager > backup_manager.sql
mysqldump -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} > backup_main.sql

# Restore from backup
mysql -u root -p${MYSQL_ROOT_PASSWORD} hcarecloud_manager < backup_manager.sql
mysql -u root -p${MYSQL_ROOT_PASSWORD} ${MYSQL_DATABASE} < backup_main.sql
```

### Backup Strategy
- **Daily**: Automated full database backups
- **Hourly**: Transaction log backups
- **Weekly**: Full system backups including files
- **Monthly**: Archive backups for long-term retention

## Monitoring and Maintenance

### Performance Monitoring
- **Query Performance**: Monitor slow queries and optimize
- **Index Usage**: Ensure proper indexing for frequently accessed data
- **Connection Pooling**: Monitor connection usage and pool efficiency
- **Storage Usage**: Track database growth and plan capacity

### Health Checks
```sql
-- Check database connectivity
SELECT 1;

-- Check table status
SHOW TABLE STATUS FROM hcarecloud_manager;

-- Check user permissions
SHOW GRANTS FOR 'hcm_manager'@'%';

-- Check foreign key constraints
SELECT * FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'hcarecloud_manager';
```

## Troubleshooting

### Common Issues
1. **Connection Refused**: Check MySQL service status and network connectivity
2. **Access Denied**: Verify user credentials and permissions
3. **Table Not Found**: Ensure `init-db.sh` has been executed
4. **Foreign Key Errors**: Check referential integrity and constraint definitions
5. **Character Set Issues**: Ensure UTF8MB4 encoding for proper Unicode support

### Debug Commands
```bash
# Check MySQL service
docker exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "SELECT VERSION();"

# List databases
docker exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "SHOW DATABASES;"

# Check table structure
docker exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} hcarecloud_manager -e "DESCRIBE users;"

# Check user permissions
docker exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "SELECT User, Host FROM mysql.user;"
```

## Related Documentation
- `/docker/scripts/hcm/api/README.md` - API layer documentation
- `/docker/scripts/hcm/hcc_pymodules/README.md` - Python modules documentation
- `/docker/scripts/hcm/themes/hcmpro/README.md` - Frontend documentation

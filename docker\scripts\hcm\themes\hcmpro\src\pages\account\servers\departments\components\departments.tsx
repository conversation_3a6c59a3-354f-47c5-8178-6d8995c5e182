/**
 * H‑CareCloud Project – Server Access Departments Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { useEffect, useMemo, useState } from 'react';
import { hcmApi } from '@/services/hcm-api';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { Skeleton } from '@/components/ui/skeleton';
import {
  ColumnDef,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  PaginationState,
  RowSelectionState,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { Search, Settings2, SquarePen, Trash2, X, Plus } from 'lucide-react';
import { Link } from 'react-router';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardFooter,
  CardHeader,
  CardHeading,
  CardTable,
  CardToolbar,
} from '@/components/ui/card';
import { DataGrid, useDataGrid } from '@/components/ui/data-grid';
import { DataGridColumnHeader } from '@/components/ui/data-grid-column-header';
import { DataGridColumnVisibility } from '@/components/ui/data-grid-column-visibility';
import { DataGridPagination } from '@/components/ui/data-grid-pagination';
import {
  DataGridTable,
  DataGridTableRowSelect,
  DataGridTableRowSelectAll,
} from '@/components/ui/data-grid-table';
import { Input } from '@/components/ui/input';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';

// Interface matching the departments table structure from init-db.sh
interface IDepartmentData {
  id: number;
  name: string;
  description: string | null;
  manager_id: number | null;
  budget: number | null;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
  manager_name?: string;
  member_count?: number;
}

const Departments = () => {
  const [departments, setDepartments] = useState<IDepartmentData[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'name', desc: false },
  ]);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadDepartments();
  }, []);

  const loadDepartments = async () => {
    try {
      setLoading(true);
      const data = await hcmApi.getDepartments();
      setDepartments(data);
    } catch (error) {
      toast.error('Failed to load server departments');
      logger.error('Failed to load server departments', {
        component: 'Departments',
        action: 'loadDepartments'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const filteredData = useMemo(() => {
    return departments.filter((department) => {
      if (!searchQuery) return true;
      const searchLower = searchQuery.toLowerCase();
      return (
        department.name.toLowerCase().includes(searchLower) ||
        (department.description && department.description.toLowerCase().includes(searchLower)) ||
        (department.manager_name && department.manager_name.toLowerCase().includes(searchLower))
      );
    });
  }, [departments, searchQuery]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatBudget = (budget: number | null) => {
    if (!budget) return 'Not set';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(budget);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="success">Active</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const columns = useMemo<ColumnDef<IDepartmentData>[]>(
    () => [
      {
        accessorKey: 'id',
        header: () => <DataGridTableRowSelectAll />,
        cell: ({ row }) => <DataGridTableRowSelect row={row} />,
        enableSorting: false,
        enableHiding: false,
        enableResizing: false,
        size: 46,
        meta: {
          cellClassName: '',
        },
      },
      {
        id: 'name',
        accessorFn: (row) => row.name,
        header: ({ column }) => (
          <DataGridColumnHeader title="Department" column={column} />
        ),
        cell: ({ row }) => (
          <div className="flex flex-col">
            <Link
              to={`/account/servers/departments/${row.original.id}`}
              className="font-semibold text-gray-900 hover:text-primary"
            >
              {row.original.name}
            </Link>
            {row.original.description && (
              <span className="text-sm text-gray-600">
                {row.original.description}
              </span>
            )}
          </div>
        ),
        enableSorting: true,
        size: 250,
        meta: {
          headerClassName: '',
        },
      },
      {
        id: 'manager',
        accessorFn: (row) => row.manager_name || 'Unassigned',
        header: ({ column }) => (
          <DataGridColumnHeader title="Manager" column={column} />
        ),
        cell: ({ row }) => (
          <span className="text-secondary-foreground font-normal">
            {row.original.manager_name || 'Unassigned'}
          </span>
        ),
        enableSorting: true,
        size: 150,
        meta: {
          headerClassName: '',
        },
      },
      {
        id: 'budget',
        accessorFn: (row) => row.budget || 0,
        header: ({ column }) => (
          <DataGridColumnHeader title="Budget" column={column} />
        ),
        cell: ({ row }) => (
          <span className="text-secondary-foreground font-normal">
            {formatBudget(row.original.budget)}
          </span>
        ),
        enableSorting: true,
        size: 120,
        meta: {
          headerClassName: '',
        },
      },
      {
        id: 'members',
        accessorFn: (row) => row.member_count || 0,
        header: ({ column }) => (
          <DataGridColumnHeader title="Members" column={column} />
        ),
        cell: ({ row }) => (
          <span className="text-secondary-foreground font-normal">
            {row.original.member_count || 0}
          </span>
        ),
        enableSorting: true,
        size: 90,
        meta: {
          headerClassName: '',
        },
      },
      {
        id: 'status',
        accessorFn: (row) => row.status,
        header: ({ column }) => (
          <DataGridColumnHeader title="Status" column={column} />
        ),
        cell: ({ row }) => getStatusBadge(row.original.status),
        enableSorting: true,
        size: 100,
        meta: {
          headerClassName: '',
        },
      },
      {
        id: 'created_at',
        accessorFn: (row) => row.created_at,
        header: ({ column }) => (
          <DataGridColumnHeader title="Created" column={column} />
        ),
        cell: ({ row }) => (
          <span className="text-secondary-foreground font-normal">
            {formatDate(row.original.created_at)}
          </span>
        ),
        enableSorting: true,
        size: 120,
        meta: {
          headerClassName: '',
        },
      },
      {
        id: 'edit',
        header: () => '',
        cell: ({ row }) => (
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => {
              // Handle edit department
              toast.info(`Edit department: ${row.original.name}`);
            }}
          >
            <SquarePen className="h-4 w-4" />
          </Button>
        ),
        enableSorting: false,
        size: 65,
        meta: {
          headerClassName: '',
        },
      },
      {
        id: 'delete',
        header: () => '',
        cell: ({ row }) => (
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => {
              // Handle delete department
              toast.error(`Delete department: ${row.original.name}`);
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        ),
        enableSorting: false,
        size: 65,
        meta: {
          headerClassName: '',
        },
      },
    ],
    [],
  );

  useEffect(() => {
    const selectedRowIds = Object.keys(rowSelection);
    if (selectedRowIds.length > 0) {
      toast(`Total ${selectedRowIds.length} departments selected.`, {
        description: `Selected IDs: ${selectedRowIds}`,
        action: {
          label: 'Undo',
          onClick: () => {
            logger.debug('Department selection undo requested', {
              component: 'Departments',
              action: 'undoSelection',
              data: { selectedRowIds }
            });
            setRowSelection({});
          },
        },
      });
    }
  }, [rowSelection]);

  const table = useReactTable({
    columns,
    data: filteredData,
    pageCount: Math.ceil((filteredData?.length || 0) / pagination.pageSize),
    getRowId: (row: IDepartmentData) => row.id.toString(),
    state: {
      pagination,
      sorting,
    },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  const Toolbar = () => {
    const { table } = useDataGrid();

    return (
      <CardToolbar>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Department
        </Button>
        <DataGridColumnVisibility
          table={table}
          trigger={
            <Button variant="outline">
              <Settings2 className="h-4 w-4 mr-2" />
              Columns
            </Button>
          }
        />
      </CardToolbar>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="py-4">
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-48" />
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-32" />
              <Skeleton className="h-8 w-24" />
            </div>
          </div>
        </CardHeader>
        <CardTable>
          <div className="space-y-4 p-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center gap-4">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
            ))}
          </div>
        </CardTable>
      </Card>
    );
  }

  return (
    <DataGrid
      table={table}
      recordCount={filteredData?.length || 0}
      tableLayout={{
        columnsPinnable: true,
        columnsMovable: true,
        columnsVisibility: true,
        cellBorder: true,
      }}
    >
      <Card>
        <CardHeader className="py-4">
          <CardHeading>
            <div className="flex items-center gap-2.5">
              <div className="relative">
                <Search className="size-4 text-muted-foreground absolute start-3 top-1/2 -translate-y-1/2" />
                <Input
                  placeholder="Search departments..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="ps-9 w-64"
                />
                {searchQuery.length > 0 && (
                  <Button
                    mode="icon"
                    variant="ghost"
                    className="absolute end-1.5 top-1/2 -translate-y-1/2 h-6 w-6"
                    onClick={() => setSearchQuery('')}
                  >
                    <X />
                  </Button>
                )}
              </div>
            </div>
          </CardHeading>
          <Toolbar />
        </CardHeader>
        <CardTable>
          <ScrollArea>
            <DataGridTable />
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </CardTable>
        <CardFooter>
          <DataGridPagination />
        </CardFooter>
      </Card>
    </DataGrid>
  );
};

export { Departments };
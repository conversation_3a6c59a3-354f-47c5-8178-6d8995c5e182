/**
 * H‑CareCloud Project – API Tokens Hook
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { useState, useEffect } from 'react';
import { hcmApi, type ApiToken } from '@/services/hcm-api';
import { logger } from '@/lib/logger';

export function useApiTokens() {
  const [tokens, setTokens] = useState<ApiToken[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTokens = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await hcmApi.getApiTokens();
      setTokens(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch API tokens');
      logger.error('Failed to fetch API tokens', {
        component: 'useApiTokens',
        action: 'fetchTokens'
      }, err as Error);
    } finally {
      setLoading(false);
    }
  };

  const createToken = async (name: string, abilities: string[] = []) => {
    try {
      setError(null);
      const newToken = await hcmApi.createApiToken(name, abilities);
      setTokens(prev => [...prev, newToken]);
      return newToken;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create API token');
      logger.error('Failed to create API token', {
        component: 'useApiTokens',
        action: 'createToken',
        data: { name, abilities }
      }, err as Error);
      throw err;
    }
  };

  const revokeToken = async (token: string) => {
    try {
      setError(null);
      await hcmApi.revokeApiToken(token);
      setTokens(prev => prev.filter(t => t.token !== token));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to revoke API token');
      logger.error('Failed to revoke API token', {
        component: 'useApiTokens',
        action: 'revokeToken',
        data: { token }
      }, err as Error);
      throw err;
    }
  };

  useEffect(() => {
    fetchTokens();
  }, []);

  return {
    tokens,
    loading,
    error,
    createToken,
    revokeToken,
    refetch: fetchTokens,
  };
}

/**
 * H‑CareCloud Project – Secure Session Management
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { logger } from './logger';

interface SessionData {
  token: string;
  user: {
    id: number;
    name: string;
    email: string;
    role: string;
  };
  expiresAt: number;
  refreshToken?: string;
  csrfToken: string;
  sessionId: string;
  ipAddress?: string;
  userAgent?: string;
}

interface SessionOptions {
  maxAge?: number; // Session duration in milliseconds
  secure?: boolean; // Use secure cookies
  sameSite?: 'strict' | 'lax' | 'none';
  domain?: string;
}

class SecureSessionManager {
  private readonly SESSION_KEY = 'hcm_session';
  private readonly CSRF_KEY = 'hcm_csrf';
  private readonly DEFAULT_MAX_AGE = 24 * 60 * 60 * 1000; // 24 hours
  private sessionData: SessionData | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;

  constructor(private options: SessionOptions = {}) {
    this.options = {
      maxAge: this.DEFAULT_MAX_AGE,
      secure: window.location.protocol === 'https:',
      sameSite: 'strict',
      ...options
    };

    // Initialize session validation
    this.validateSession();
    this.setupAutoRefresh();
  }

  /**
   * Create a new secure session
   */
  async createSession(sessionData: Omit<SessionData, 'expiresAt' | 'csrfToken' | 'sessionId'>): Promise<void> {
    try {
      const now = Date.now();
      const expiresAt = now + (this.options.maxAge || this.DEFAULT_MAX_AGE);
      const csrfToken = this.generateCSRFToken();
      const sessionId = this.generateSessionId();

      const fullSessionData: SessionData = {
        ...sessionData,
        expiresAt,
        csrfToken,
        sessionId,
        ipAddress: await this.getClientIP(),
        userAgent: navigator.userAgent
      };

      // Store session data securely
      await this.storeSession(fullSessionData);
      this.sessionData = fullSessionData;

      // Setup automatic refresh
      this.setupAutoRefresh();

      logger.info('Secure session created', {
        component: 'SecureSession',
        action: 'createSession',
        userId: sessionData.user.id,
        data: { sessionId, expiresAt: new Date(expiresAt).toISOString() }
      });

    } catch (error) {
      logger.error('Failed to create secure session', {
        component: 'SecureSession',
        action: 'createSession'
      }, error as Error);
      throw error;
    }
  }

  /**
   * Get current session data
   */
  getSession(): SessionData | null {
    if (!this.sessionData || this.isSessionExpired()) {
      return null;
    }
    return this.sessionData;
  }

  /**
   * Validate current session
   */
  async validateSession(): Promise<boolean> {
    try {
      const storedSession = await this.retrieveSession();
      
      if (!storedSession) {
        return false;
      }

      // Check expiration
      if (this.isSessionExpired(storedSession)) {
        await this.destroySession();
        return false;
      }

      // Validate session integrity
      if (!this.validateSessionIntegrity(storedSession)) {
        await this.destroySession();
        logger.warn('Session integrity validation failed', {
          component: 'SecureSession',
          action: 'validateSession'
        });
        return false;
      }

      this.sessionData = storedSession;
      return true;

    } catch (error) {
      logger.error('Session validation failed', {
        component: 'SecureSession',
        action: 'validateSession'
      }, error as Error);
      return false;
    }
  }

  /**
   * Refresh session token
   */
  async refreshSession(): Promise<boolean> {
    try {
      if (!this.sessionData?.refreshToken) {
        return false;
      }

      // Call API to refresh token
      const response = await fetch(`${import.meta.env.VITE_APP_API_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': this.sessionData.csrfToken
        },
        body: JSON.stringify({
          refresh_token: this.sessionData.refreshToken,
          session_id: this.sessionData.sessionId
        })
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const data = await response.json();
      
      if (data.success && data.token) {
        // Update session with new token
        const updatedSession: SessionData = {
          ...this.sessionData,
          token: data.token,
          expiresAt: Date.now() + (this.options.maxAge || this.DEFAULT_MAX_AGE),
          refreshToken: data.refresh_token || this.sessionData.refreshToken
        };

        await this.storeSession(updatedSession);
        this.sessionData = updatedSession;

        logger.debug('Session refreshed successfully', {
          component: 'SecureSession',
          action: 'refreshSession',
          userId: this.sessionData.user.id
        });

        return true;
      }

      return false;

    } catch (error) {
      logger.error('Session refresh failed', {
        component: 'SecureSession',
        action: 'refreshSession'
      }, error as Error);
      return false;
    }
  }

  /**
   * Destroy current session
   */
  async destroySession(): Promise<void> {
    try {
      if (this.sessionData) {
        // Notify server about session destruction
        try {
          await fetch(`${import.meta.env.VITE_APP_API_URL}/auth/logout`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${this.sessionData.token}`,
              'X-CSRF-Token': this.sessionData.csrfToken
            },
            body: JSON.stringify({
              session_id: this.sessionData.sessionId
            })
          });
        } catch (error) {
          // Log but don't throw - session cleanup should continue
          logger.warn('Server logout notification failed', {
            component: 'SecureSession',
            action: 'destroySession'
          }, error as Error);
        }
      }

      // Clear local session data
      await this.clearStoredSession();
      this.sessionData = null;

      // Clear refresh timer
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
        this.refreshTimer = null;
      }

      logger.info('Session destroyed', {
        component: 'SecureSession',
        action: 'destroySession'
      });

    } catch (error) {
      logger.error('Session destruction failed', {
        component: 'SecureSession',
        action: 'destroySession'
      }, error as Error);
    }
  }

  /**
   * Get CSRF token for requests
   */
  getCSRFToken(): string | null {
    return this.sessionData?.csrfToken || null;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.getSession() !== null;
  }

  // Private helper methods
  private generateCSRFToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  private generateSessionId(): string {
    return `hcm_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private async getClientIP(): Promise<string | undefined> {
    try {
      // This would typically be handled by the server
      // For client-side, we can't reliably get the real IP
      return undefined;
    } catch {
      return undefined;
    }
  }

  private isSessionExpired(session?: SessionData): boolean {
    const sessionToCheck = session || this.sessionData;
    if (!sessionToCheck) return true;
    return Date.now() >= sessionToCheck.expiresAt;
  }

  private validateSessionIntegrity(session: SessionData): boolean {
    // Validate required fields
    if (!session.token || !session.user || !session.csrfToken || !session.sessionId) {
      return false;
    }

    // Validate user agent (if stored)
    if (session.userAgent && session.userAgent !== navigator.userAgent) {
      logger.warn('User agent mismatch detected', {
        component: 'SecureSession',
        action: 'validateSessionIntegrity'
      });
      return false;
    }

    return true;
  }

  private async storeSession(session: SessionData): Promise<void> {
    try {
      // Use sessionStorage instead of localStorage for better security
      // sessionStorage is cleared when tab is closed
      const encryptedData = btoa(JSON.stringify(session));
      sessionStorage.setItem(this.SESSION_KEY, encryptedData);
      
      // Store CSRF token separately
      sessionStorage.setItem(this.CSRF_KEY, session.csrfToken);
      
    } catch (error) {
      logger.error('Failed to store session', {
        component: 'SecureSession',
        action: 'storeSession'
      }, error as Error);
      throw error;
    }
  }

  private async retrieveSession(): Promise<SessionData | null> {
    try {
      const encryptedData = sessionStorage.getItem(this.SESSION_KEY);
      if (!encryptedData) return null;

      const sessionData = JSON.parse(atob(encryptedData)) as SessionData;
      return sessionData;
      
    } catch (error) {
      logger.error('Failed to retrieve session', {
        component: 'SecureSession',
        action: 'retrieveSession'
      }, error as Error);
      return null;
    }
  }

  private async clearStoredSession(): Promise<void> {
    sessionStorage.removeItem(this.SESSION_KEY);
    sessionStorage.removeItem(this.CSRF_KEY);
    
    // Also clear any localStorage tokens (legacy cleanup)
    localStorage.removeItem('hcm_auth_token');
  }

  private setupAutoRefresh(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    if (!this.sessionData) return;

    // Refresh token 5 minutes before expiration
    const refreshTime = this.sessionData.expiresAt - Date.now() - (5 * 60 * 1000);
    
    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(async () => {
        const success = await this.refreshSession();
        if (success) {
          this.setupAutoRefresh(); // Setup next refresh
        } else {
          await this.destroySession(); // Cleanup on refresh failure
        }
      }, refreshTime);
    }
  }
}

// Export singleton instance
export const secureSession = new SecureSessionManager();

// Export types
export type { SessionData, SessionOptions };

# H-CareManager Components

## Overview
This directory contains reusable UI components for H-CareManager Project, built with React TypeScript and integrated with the Metronic HCCManagerPro theme. The components provide a consistent, professional interface for server management functionality while maintaining HIPAA compliance and accessibility standards.

## Architecture

### Components Structure
```
/src/components/
├── ui/                     # Core UI components
│   ├── Button.tsx         # Custom button variants
│   ├── Input.tsx          # Form input components
│   ├── Modal.tsx          # Modal dialog components
│   ├── Table.tsx          # Data table components
│   ├── Card.tsx           # Card layout components
│   ├── Skeleton.tsx       # Loading skeleton components
│   └── Toast.tsx          # Notification components
├── forms/                 # Form-specific components
│   ├── FormField.tsx      # Reusable form field wrapper
│   ├── ValidationMessage.tsx # Form validation display
│   ├── FileUpload.tsx     # File upload component
│   └── SearchInput.tsx    # Search input with debouncing
├── layout/                # Layout components
│   ├── Header.tsx         # Application header
│   ├── Sidebar.tsx        # Navigation sidebar
│   ├── Footer.tsx         # Application footer
│   └── PageWrapper.tsx    # Page layout wrapper
├── server/                # Server management components
│   ├── ServerCard.tsx     # Server status card
│   ├── ServerList.tsx     # Server listing component
│   ├── ServerMetrics.tsx  # Server performance metrics
│   └── ServerActions.tsx  # Server action buttons
└── shared/                # Shared utility components
    ├── LoadingSpinner.tsx # Loading indicators
    ├── ErrorBoundary.tsx  # Error handling wrapper
    ├── ConfirmDialog.tsx  # Confirmation dialogs
    └── DataTable.tsx      # Advanced data table
```

### Component Categories
- **UI Components**: Basic building blocks (buttons, inputs, modals)
- **Form Components**: Form-specific functionality and validation
- **Layout Components**: Application structure and navigation
- **Server Components**: Server management specific components
- **Shared Components**: Utility and helper components

## Core UI Components

### Button.tsx
**Purpose**: Consistent button styling with Metronic theme integration.

**Variants**:
```typescript
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
  onClick?: () => void;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  icon,
  children,
  onClick,
  ...props
}) => {
  const baseClasses = 'btn';
  const variantClasses = `btn-${variant}`;
  const sizeClasses = `btn-${size}`;
  
  return (
    <button
      className={`${baseClasses} ${variantClasses} ${sizeClasses}`}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {loading && <span className="spinner-border spinner-border-sm me-2" />}
      {icon && <span className="me-2">{icon}</span>}
      {children}
    </button>
  );
};

// Usage examples
<Button variant="primary" size="lg" loading={isLoading}>
  Save Changes
</Button>

<Button variant="danger" icon={<TrashIcon />}>
  Delete Server
</Button>
```

### Input.tsx
**Purpose**: Form input components with validation and accessibility.

**Features**:
```typescript
interface InputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel';
  label?: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  icon?: React.ReactNode;
  helpText?: string;
}

export const Input: React.FC<InputProps> = ({
  type = 'text',
  label,
  placeholder,
  value,
  onChange,
  error,
  required = false,
  disabled = false,
  icon,
  helpText,
  ...props
}) => {
  const inputId = useId();
  
  return (
    <div className="mb-3">
      {label && (
        <label htmlFor={inputId} className="form-label">
          {label}
          {required && <span className="text-danger">*</span>}
        </label>
      )}
      
      <div className="input-group">
        {icon && (
          <span className="input-group-text">
            {icon}
          </span>
        )}
        
        <input
          id={inputId}
          type={type}
          className={`form-control ${error ? 'is-invalid' : ''}`}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          required={required}
          {...props}
        />
        
        {error && (
          <div className="invalid-feedback">
            {error}
          </div>
        )}
      </div>
      
      {helpText && (
        <div className="form-text">
          {helpText}
        </div>
      )}
    </div>
  );
};
```

### Modal.tsx
**Purpose**: Accessible modal dialogs with Metronic styling.

**Implementation**:
```typescript
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
  footer?: React.ReactNode;
  closeOnBackdrop?: boolean;
}

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  size = 'md',
  children,
  footer,
  closeOnBackdrop = true
}) => {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div 
      className="modal fade show d-block" 
      style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}
      onClick={closeOnBackdrop ? onClose : undefined}
    >
      <div 
        className={`modal-dialog modal-${size}`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="modal-content">
          {title && (
            <div className="modal-header">
              <h5 className="modal-title">{title}</h5>
              <button
                type="button"
                className="btn-close"
                onClick={onClose}
                aria-label="Close"
              />
            </div>
          )}
          
          <div className="modal-body">
            {children}
          </div>
          
          {footer && (
            <div className="modal-footer">
              {footer}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
```

### Skeleton.tsx
**Purpose**: Loading skeleton components for professional loading states.

**Implementation**:
```typescript
interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  className?: string;
  variant?: 'text' | 'rectangular' | 'circular';
  animation?: 'pulse' | 'wave' | 'none';
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '1rem',
  className = '',
  variant = 'text',
  animation = 'pulse'
}) => {
  const baseClasses = 'skeleton';
  const variantClasses = `skeleton-${variant}`;
  const animationClasses = animation !== 'none' ? `skeleton-${animation}` : '';
  
  return (
    <div
      className={`${baseClasses} ${variantClasses} ${animationClasses} ${className}`}
      style={{ width, height }}
    />
  );
};

// Usage examples
<Skeleton width="100%" height="2rem" />
<Skeleton variant="circular" width="40px" height="40px" />
<Skeleton variant="rectangular" width="100%" height="200px" />
```

## Form Components

### FormField.tsx
**Purpose**: Reusable form field wrapper with consistent styling.

**Features**:
```typescript
interface FormFieldProps {
  label?: string;
  required?: boolean;
  error?: string;
  helpText?: string;
  children: React.ReactNode;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  required = false,
  error,
  helpText,
  children
}) => {
  return (
    <div className="mb-3">
      {label && (
        <label className="form-label">
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </label>
      )}
      
      {children}
      
      {error && (
        <div className="text-danger mt-1">
          <small>{error}</small>
        </div>
      )}
      
      {helpText && !error && (
        <div className="form-text">
          {helpText}
        </div>
      )}
    </div>
  );
};
```

### FileUpload.tsx
**Purpose**: Secure file upload component with validation.

**Features**:
```typescript
interface FileUploadProps {
  accept?: string;
  maxSize?: number;
  multiple?: boolean;
  onUpload: (files: File[]) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  accept,
  maxSize = 10 * 1024 * 1024, // 10MB default
  multiple = false,
  onUpload,
  onError,
  disabled = false
}) => {
  const [dragActive, setDragActive] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const validateFiles = (files: FileList): File[] => {
    const validFiles: File[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (file.size > maxSize) {
        onError?.(`File ${file.name} is too large. Maximum size is ${formatFileSize(maxSize)}`);
        continue;
      }
      
      validFiles.push(file);
    }
    
    return validFiles;
  };

  const handleFiles = (files: FileList) => {
    const validFiles = validateFiles(files);
    if (validFiles.length > 0) {
      onUpload(validFiles);
    }
  };

  return (
    <div
      className={`file-upload ${dragActive ? 'drag-active' : ''} ${disabled ? 'disabled' : ''}`}
      onDragEnter={() => setDragActive(true)}
      onDragLeave={() => setDragActive(false)}
      onDragOver={(e) => e.preventDefault()}
      onDrop={(e) => {
        e.preventDefault();
        setDragActive(false);
        if (!disabled) {
          handleFiles(e.dataTransfer.files);
        }
      }}
    >
      <input
        ref={inputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        disabled={disabled}
        onChange={(e) => e.target.files && handleFiles(e.target.files)}
        style={{ display: 'none' }}
      />
      
      <div className="upload-content">
        <div className="upload-icon">
          <UploadIcon />
        </div>
        <p>Drag and drop files here or click to browse</p>
        <Button
          variant="secondary"
          disabled={disabled}
          onClick={() => inputRef.current?.click()}
        >
          Choose Files
        </Button>
      </div>
    </div>
  );
};
```

## Server Management Components

### ServerCard.tsx
**Purpose**: Server status and information display card.

**Features**:
```typescript
interface ServerCardProps {
  server: {
    id: string;
    name: string;
    status: 'online' | 'offline' | 'maintenance';
    cpu: number;
    memory: number;
    disk: number;
    uptime: string;
  };
  onAction: (action: string, serverId: string) => void;
}

export const ServerCard: React.FC<ServerCardProps> = ({ server, onAction }) => {
  const statusColor = {
    online: 'success',
    offline: 'danger',
    maintenance: 'warning'
  }[server.status];

  return (
    <div className="card">
      <div className="card-header">
        <div className="d-flex justify-content-between align-items-center">
          <h5 className="card-title mb-0">{server.name}</h5>
          <span className={`badge badge-${statusColor}`}>
            {server.status.toUpperCase()}
          </span>
        </div>
      </div>
      
      <div className="card-body">
        <div className="row">
          <div className="col-4">
            <div className="text-center">
              <div className="fs-2 fw-bold">{server.cpu}%</div>
              <div className="text-muted">CPU</div>
            </div>
          </div>
          <div className="col-4">
            <div className="text-center">
              <div className="fs-2 fw-bold">{server.memory}%</div>
              <div className="text-muted">Memory</div>
            </div>
          </div>
          <div className="col-4">
            <div className="text-center">
              <div className="fs-2 fw-bold">{server.disk}%</div>
              <div className="text-muted">Disk</div>
            </div>
          </div>
        </div>
        
        <div className="mt-3">
          <small className="text-muted">Uptime: {server.uptime}</small>
        </div>
      </div>
      
      <div className="card-footer">
        <div className="d-flex gap-2">
          <Button
            size="sm"
            variant="primary"
            onClick={() => onAction('restart', server.id)}
          >
            Restart
          </Button>
          <Button
            size="sm"
            variant="secondary"
            onClick={() => onAction('logs', server.id)}
          >
            View Logs
          </Button>
        </div>
      </div>
    </div>
  );
};
```

## Shared Components

### DataTable.tsx
**Purpose**: Advanced data table with sorting, filtering, and pagination.

**Features**:
```typescript
interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pagination?: boolean;
  sorting?: boolean;
  filtering?: boolean;
  onRowClick?: (row: T) => void;
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  onFilter?: (filters: Record<string, any>) => void;
}

export const DataTable = <T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  pagination = true,
  sorting = true,
  filtering = false,
  onRowClick,
  onSort,
  onFilter
}: DataTableProps<T>) => {
  const [sortColumn, setSortColumn] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  if (loading) {
    return (
      <div className="table-responsive">
        <table className="table">
          <thead>
            <tr>
              {columns.map((column, index) => (
                <th key={index}>
                  <Skeleton width="100%" height="1.5rem" />
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {Array.from({ length: 5 }).map((_, index) => (
              <tr key={index}>
                {columns.map((_, colIndex) => (
                  <td key={colIndex}>
                    <Skeleton width="100%" height="1.5rem" />
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  }

  return (
    <div className="datatable-wrapper">
      {filtering && (
        <DataTableFilters columns={columns} onFilter={onFilter} />
      )}
      
      <div className="table-responsive">
        <table className="table table-striped">
          <thead>
            <tr>
              {columns.map((column, index) => (
                <th
                  key={index}
                  className={sorting && column.sortable ? 'sortable' : ''}
                  onClick={() => {
                    if (sorting && column.sortable) {
                      const newDirection = sortColumn === column.key && sortDirection === 'asc' ? 'desc' : 'asc';
                      setSortColumn(column.key);
                      setSortDirection(newDirection);
                      onSort?.(column.key, newDirection);
                    }
                  }}
                >
                  {column.title}
                  {sorting && column.sortable && (
                    <SortIcon column={column.key} current={sortColumn} direction={sortDirection} />
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((row, index) => (
              <tr
                key={index}
                className={onRowClick ? 'clickable' : ''}
                onClick={() => onRowClick?.(row)}
              >
                {columns.map((column, colIndex) => (
                  <td key={colIndex}>
                    {column.render ? column.render(row[column.key], row) : row[column.key]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {pagination && (
        <DataTablePagination
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={data.length}
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
        />
      )}
    </div>
  );
};
```

## Component Standards

### Accessibility
- **ARIA Labels**: Proper ARIA labeling for screen readers
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Proper focus handling and indicators
- **Color Contrast**: WCAG 2.1 AA compliant color contrast

### Performance
- **React.memo**: Memoization for expensive components
- **useCallback**: Optimized event handlers
- **Lazy Loading**: Code splitting for large components
- **Virtual Scrolling**: For large data sets

### Testing
```typescript
// Component testing example
describe('Button Component', () => {
  it('renders with correct variant class', () => {
    render(<Button variant="primary">Test</Button>);
    expect(screen.getByRole('button')).toHaveClass('btn-primary');
  });

  it('shows loading state', () => {
    render(<Button loading>Test</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
    expect(screen.getByRole('button')).toContainHTML('spinner-border');
  });
});
```

## Related Documentation
- [Metronic Theme Documentation](https://keenthemes.com/metronic)
- [React TypeScript Documentation](https://react-typescript-cheatsheet.netlify.app/)
- [Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [H-CareManager Project Documentation](../../docs.md)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
H‑CareCloud Project – Authentication API
<AUTHOR> <<EMAIL>>
@scrum_master <PERSON><PERSON> <<EMAIL>>
@company Hostwek LTD – WekTurbo Dev
@website https://hostwek.com/wekturbo
@support <EMAIL>
@version 1.0.8
@year 2025

Production-ready authentication for H-CareManager using existing seeded database.
"""

import os
import json
import hashlib
import secrets
import logging
import pymysql
from datetime import datetime, timedelta
from flask import jsonify, request, Blueprint, current_app, g
from functools import wraps

# Import centralized configuration
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))
from hcc_pymodules import hcc_config
from hcc_pymodules import hcc_database

# Set up logging with proper debug levels
logger = logging.getLogger('HCareCloud.api.auth')

# Configure debug logging for development
if os.getenv('FLASK_ENV') == 'development' or os.getenv('NODE_ENV') == 'development':
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger.setLevel(logging.DEBUG)
else:
    logger.setLevel(logging.INFO)

# Use centralized configuration
PROJECT_ROOT = hcc_config.PROJECT_ROOT
ENV_FILE = hcc_config.ENV_FILE

# Create Blueprint
auth_bp = Blueprint('auth', __name__)

# Database connection
def get_db_connection():
    """Get database connection"""
    try:
        # Use the centralized hcc_database module to get a database connection
        return hcc_database.get_db_connection()
    except Exception as e:
        logger.error(f"Error connecting to database: {e}")
        return None

# Authentication decorator
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        # Get tenant ID from headers (using environment variable)
        tenant_id = request.headers.get('X-Tenant-ID', os.getenv('VITE_TENANT_ID'))

        token = None

        # Get token from Authorization header
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]

        if not token:
            return jsonify({'message': 'Token is missing'}), 401

        try:
            # Verify token
            connection = get_db_connection()
            if not connection:
                logger.error("Database connection failed during token validation")
                return jsonify({'message': 'Database connection failed'}), 500

            with connection.cursor() as cursor:
                # Check if token exists and is active
                sql = "SELECT * FROM api_tokens WHERE token = %s AND status = 'active' AND (expires_at IS NULL OR expires_at > NOW())"
                cursor.execute(sql, (token,))
                token_data = cursor.fetchone()

                if not token_data:
                    return jsonify({'message': 'Invalid or expired token'}), 401

                # Update last_used_at
                sql = "UPDATE api_tokens SET last_used_at = NOW() WHERE id = %s"
                cursor.execute(sql, (token_data['id'],))
                connection.commit()

                # Get user data
                sql = "SELECT * FROM users WHERE id = %s"
                cursor.execute(sql, (token_data['user_id'],))
                user = cursor.fetchone()

                if not user:
                    return jsonify({'message': 'User not found'}), 401

                # Remove password from user data and convert to dict
                user_dict = dict(user)
                if 'password' in user_dict:
                    del user_dict['password']

            connection.close()

            # Add user data to Flask g object
            g.user = user_dict
            g.token_data = dict(token_data)

            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error verifying token: {e}")
            return jsonify({'message': 'Token verification failed'}), 401

    return decorated

# Login route
@auth_bp.route('/login', methods=['POST'])
def login():
    """Login user and return token"""
    try:
        data = request.json

        if not data or 'email' not in data or 'password' not in data:
            return jsonify({'message': 'Email and password are required'}), 400

        email = data['email']
        password = data['password']

        logger.debug(f"Login attempt for email: {email}")
        logger.debug(f"Request headers: {dict(request.headers)}")
        logger.debug(f"Request data keys: {list(data.keys()) if data else 'No data'}")

        # Hash password (MD5 for compatibility with existing database)
        hashed_password = hashlib.md5(password.encode()).hexdigest()
        logger.debug(f"Password hashed successfully")

        # Connect to database
        connection = get_db_connection()
        if not connection:
            return jsonify({'message': 'Database connection failed'}), 500

        with connection.cursor() as cursor:
            # Check if user exists and is active
            sql = "SELECT * FROM users WHERE email = %s AND status = 'active'"
            cursor.execute(sql, (email,))
            user = cursor.fetchone()

            if not user:
                return jsonify({'success': False, 'message': 'No account found with this email address'}), 401

            # Check password
            if user['password'] != hashed_password:
                return jsonify({'success': False, 'message': 'Incorrect password'}), 401

            # Check if user is active
            if user.get('is_active', 1) != 1:
                return jsonify({'success': False, 'message': 'Account is deactivated'}), 401

            # Generate token
            token = secrets.token_hex(32)
            expires_at = datetime.now() + timedelta(days=7)

            # Save token to database - using environment configured database
            sql = "INSERT INTO api_tokens (user_id, name, token, abilities, status, created_at, updated_at, expires_at) VALUES (%s, %s, %s, %s, 'active', NOW(), NOW(), %s)"
            cursor.execute(sql, (user['id'], 'HCM Login Token', token, json.dumps(['*']), expires_at))

            # Update last login time
            cursor.execute("UPDATE users SET last_login_at = NOW() WHERE id = %s", (user['id'],))
            connection.commit()

            # Remove password from user data
            del user['password']

        connection.close()

        return jsonify({
            'success': True,
            'token': token,
            'user': user,
            'expires_at': expires_at.isoformat()
        })
    except Exception as e:
        logger.error(f"Error logging in: {e}")
        return jsonify({'message': str(e)}), 500

# Verify token route
@auth_bp.route('/verify', methods=['GET'])
def verify_token():
    """Verify token and return user data"""
    try:
        # Get token from Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'message': 'No valid authorization header'}), 401

        token = auth_header.split(' ')[1]

        # Verify token directly
        connection = get_db_connection()
        if not connection:
            return jsonify({'message': 'Database connection failed'}), 500

        with connection.cursor() as cursor:
            # Check if token exists and is valid
            sql = "SELECT * FROM api_tokens WHERE token = %s AND status = 'active' AND (expires_at IS NULL OR expires_at > NOW())"
            cursor.execute(sql, (token,))
            token_data = cursor.fetchone()

            if not token_data:
                return jsonify({'message': 'Invalid or expired token'}), 401

            # Get user data
            sql = "SELECT * FROM users WHERE id = %s AND status = 'active'"
            cursor.execute(sql, (token_data['user_id'],))
            user = cursor.fetchone()

            if not user:
                return jsonify({'message': 'User not found'}), 401

            # Convert to dict and remove password
            user_dict = dict(user)
            if 'password' in user_dict:
                del user_dict['password']

        connection.close()

        return jsonify({
            'success': True,
            'user': user_dict
        })
    except Exception as e:
        logger.error(f"Error verifying token: {e}")
        return jsonify({'message': str(e)}), 500

# Logout route
@auth_bp.route('/logout', methods=['POST'])
@token_required
def logout():
    """Logout user by invalidating token"""
    try:
        # Get token from Authorization header
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]

            # Connect to database
            connection = get_db_connection()
            if not connection:
                return jsonify({'message': 'Database connection failed'}), 500

            with connection.cursor() as cursor:
                # Revoke token for security audit
                sql = "UPDATE api_tokens SET status = 'revoked', updated_at = NOW() WHERE token = %s"
                cursor.execute(sql, (token,))
                connection.commit()

            connection.close()

            return jsonify({'success': True, 'message': 'Logged out successfully'})

        return jsonify({'message': 'Token is missing'}), 401
    except Exception as e:
        logger.error(f"Error logging out: {e}")
        return jsonify({'message': str(e)}), 500

# Status route
@auth_bp.route('/status', methods=['GET'])
def auth_status():
    """Get authentication status"""
    try:
        # Check if database is accessible
        connection = get_db_connection()
        if not connection:
            return jsonify({
                'success': False,
                'message': 'Database connection failed',
                'status': 'error'
            }), 500

        # Check if users table exists and has data
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) as count FROM users WHERE status = 'active'")
            user_count = cursor.fetchone()['count']

            # Check if theme settings are properly configured
            cursor.execute("SELECT * FROM theme_settings WHERE `key` = 'theme_name'")
            theme_name = cursor.fetchone()

            cursor.execute("SELECT * FROM theme_settings WHERE `key` = 'theme_mode'")
            theme_mode = cursor.fetchone()

        connection.close()

        return jsonify({
            'success': True,
            'status': 'ok',
            'database': 'connected',
            'user_count': user_count,
            'theme': {
                'name': theme_name['value'] if theme_name else None,
                'mode': theme_mode['value'] if theme_mode else None
            }
        })
    except Exception as e:
        logger.error(f"Error checking auth status: {e}")
        return jsonify({
            'success': False,
            'message': str(e),
            'status': 'error'
        }), 500

# User Security API endpoints
@auth_bp.route('/security/overview', methods=['GET'])
@token_required
def get_security_overview():
    """Get user security overview including sessions, devices, and login history"""
    try:
        user_id = g.user['id']
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': 'Database connection failed'}), 500

        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # Get active sessions
        cursor.execute("""
            SELECT id, ip_address, user_agent, created_at, updated_at,
                   CASE WHEN updated_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 1 ELSE 0 END as is_active
            FROM sessions
            WHERE user_id = %s
            ORDER BY updated_at DESC
            LIMIT 10
        """, (user_id,))
        sessions = cursor.fetchall()

        # Get recent login history from api_tokens (login tokens)
        cursor.execute("""
            SELECT created_at, expires_at, name
            FROM api_tokens
            WHERE user_id = %s
            ORDER BY created_at DESC
            LIMIT 20
        """, (user_id,))
        login_history = cursor.fetchall()

        # Get user info
        cursor.execute("SELECT email, created_at, updated_at FROM users WHERE id = %s", (user_id,))
        user_info = cursor.fetchone()

        connection.close()

        return jsonify({
            'success': True,
            'data': {
                'sessions': sessions,
                'login_history': login_history,
                'user_info': user_info,
                'security_score': calculate_security_score(sessions, login_history, user_info)
            }
        })

    except Exception as e:
        logger.error(f"Error fetching security overview: {e}")
        return jsonify({'error': 'Failed to fetch security overview'}), 500

@auth_bp.route('/security/change-password', methods=['POST'])
@token_required
def change_password():
    """Change user password"""
    try:
        data = request.json
        if not data or 'current_password' not in data or 'new_password' not in data:
            return jsonify({'error': 'Current password and new password are required'}), 400

        user_id = g.user['id']
        current_password = data['current_password']
        new_password = data['new_password']

        # Validate new password strength
        if len(new_password) < 8:
            return jsonify({'error': 'New password must be at least 8 characters long'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': 'Database connection failed'}), 500

        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # Verify current password
        cursor.execute("SELECT password FROM users WHERE id = %s", (user_id,))
        user = cursor.fetchone()

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Check current password (MD5 for compatibility)
        import hashlib
        current_password_hash = hashlib.md5(current_password.encode()).hexdigest()

        if user['password'] != current_password_hash:
            return jsonify({'error': 'Current password is incorrect'}), 400

        # Update password
        new_password_hash = hashlib.md5(new_password.encode()).hexdigest()
        cursor.execute("""
            UPDATE users
            SET password = %s, updated_at = NOW()
            WHERE id = %s
        """, (new_password_hash, user_id))

        connection.commit()
        connection.close()

        logger.info(f"Password changed for user ID: {user_id}")

        return jsonify({
            'success': True,
            'message': 'Password changed successfully'
        })

    except Exception as e:
        logger.error(f"Error changing password: {e}")
        return jsonify({'error': 'Failed to change password'}), 500

@auth_bp.route('/security/sessions', methods=['GET'])
@token_required
def get_user_sessions():
    """Get all user sessions"""
    try:
        user_id = g.user['id']
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': 'Database connection failed'}), 500

        cursor = connection.cursor(pymysql.cursors.DictCursor)

        cursor.execute("""
            SELECT id, ip_address, user_agent, created_at, updated_at,
                   CASE WHEN updated_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 1 ELSE 0 END as is_active
            FROM sessions
            WHERE user_id = %s
            ORDER BY updated_at DESC
        """, (user_id,))
        sessions = cursor.fetchall()

        connection.close()

        return jsonify({
            'success': True,
            'data': sessions
        })

    except Exception as e:
        logger.error(f"Error fetching user sessions: {e}")
        return jsonify({'error': 'Failed to fetch sessions'}), 500

@auth_bp.route('/security/sessions/<session_id>', methods=['DELETE'])
@token_required
def terminate_session(session_id):
    """Terminate a specific session"""
    try:
        user_id = g.user['id']
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': 'Database connection failed'}), 500

        cursor = connection.cursor()

        # Delete the session (only if it belongs to the current user)
        cursor.execute("""
            DELETE FROM sessions
            WHERE id = %s AND user_id = %s
        """, (session_id, user_id))

        if cursor.rowcount == 0:
            return jsonify({'error': 'Session not found or access denied'}), 404

        connection.commit()
        connection.close()

        logger.info(f"Session {session_id} terminated by user {user_id}")

        return jsonify({
            'success': True,
            'message': 'Session terminated successfully'
        })

    except Exception as e:
        logger.error(f"Error terminating session: {e}")
        return jsonify({'error': 'Failed to terminate session'}), 500

@auth_bp.route('/profile', methods=['GET'])
@token_required
def get_user_profile():
    """Get user profile information"""
    try:
        user_id = g.user['id']
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': 'Database connection failed'}), 500

        cursor = connection.cursor(pymysql.cursors.DictCursor)

        cursor.execute("""
            SELECT id, name, first_name, last_name, email, profile_image,
                   phone, bio, role, last_login, created_at, updated_at
            FROM users
            WHERE id = %s
        """, (user_id,))
        user = cursor.fetchone()

        connection.close()

        if not user:
            return jsonify({'error': 'User not found'}), 404

        return jsonify({
            'success': True,
            'data': user
        })

    except Exception as e:
        logger.error(f"Error fetching user profile: {e}")
        return jsonify({'error': 'Failed to fetch profile'}), 500

@auth_bp.route('/profile', methods=['PUT'])
@token_required
def update_user_profile():
    """Update user profile information"""
    try:
        user_id = g.user['id']
        data = request.json

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': 'Database connection failed'}), 500

        cursor = connection.cursor()

        # Build update query dynamically based on provided fields
        allowed_fields = ['name', 'first_name', 'last_name', 'phone', 'bio']
        update_fields = []
        update_values = []

        for field in allowed_fields:
            if field in data:
                update_fields.append(f"`{field}` = %s")
                update_values.append(data[field])

        if not update_fields:
            return jsonify({'error': 'No valid fields to update'}), 400

        update_values.append(user_id)

        sql = f"UPDATE users SET {', '.join(update_fields)}, updated_at = NOW() WHERE id = %s"
        cursor.execute(sql, update_values)
        connection.commit()
        connection.close()

        logger.info(f"Profile updated for user ID: {user_id}")

        return jsonify({
            'success': True,
            'message': 'Profile updated successfully'
        })

    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        return jsonify({'error': 'Failed to update profile'}), 500

@auth_bp.route('/profile/image', methods=['POST'])
@token_required
def upload_profile_image():
    """Upload user profile image"""
    try:
        user_id = g.user['id']

        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']

        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Validate file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'error': 'Invalid file type. Only PNG, JPG, JPEG, and GIF are allowed'}), 400

        # Create uploads directory if it doesn't exist
        upload_dir = os.path.join(PROJECT_ROOT, 'uploads', 'profiles')
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        import uuid
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        filename = f"{user_id}_{uuid.uuid4().hex}.{file_extension}"
        file_path = os.path.join(upload_dir, filename)

        # Save file
        file.save(file_path)

        # Update database
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': 'Database connection failed'}), 500

        cursor = connection.cursor()

        # Store relative path
        relative_path = f"uploads/profiles/{filename}"
        cursor.execute("UPDATE users SET profile_image = %s, updated_at = NOW() WHERE id = %s",
                      (relative_path, user_id))
        connection.commit()
        connection.close()

        logger.info(f"Profile image uploaded for user ID: {user_id}")

        return jsonify({
            'success': True,
            'message': 'Profile image uploaded successfully',
            'image_url': f"/uploads/profiles/{filename}"
        })

    except Exception as e:
        logger.error(f"Error uploading profile image: {e}")
        return jsonify({'error': 'Failed to upload image'}), 500

def calculate_security_score(sessions, login_history, user_info):
    """Calculate a security score based on user activity"""
    score = 100

    # Deduct points for too many active sessions
    active_sessions = len([s for s in sessions if s.get('is_active')])
    if active_sessions > 3:
        score -= (active_sessions - 3) * 5

    # Deduct points for old password (if user hasn't updated recently)
    if user_info and user_info.get('updated_at'):
        from datetime import datetime, timedelta
        last_update = user_info['updated_at']
        if isinstance(last_update, str):
            last_update = datetime.fromisoformat(last_update.replace('Z', '+00:00'))

        if datetime.now() - last_update > timedelta(days=90):
            score -= 20

    # Ensure score doesn't go below 0
    return max(0, score)

# Validate database tables exist (no creation - use seeded database)
def validate_database_tables():
    """Validate that required tables exist in the configured database"""
    try:
        connection = get_db_connection()
        if not connection:
            logger.error("Database connection failed")
            return False

        with connection.cursor() as cursor:
            required_tables = ['users', 'api_tokens', 'user_sessions', 'settings']
            missing_tables = []
            
            for table in required_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                if not cursor.fetchone():
                    missing_tables.append(table)
            
            if missing_tables:
                logger.error(f"Required tables missing: {missing_tables}. Please run init-db.sh")
                return False
                
            logger.info("All required database tables exist")
            return True
            
    except Exception as e:
        logger.error(f"Error validating database tables: {e}")
        return False
    finally:
        if connection:
            connection.close()

# Register routes with Flask app
def init_app(app):
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    logger.info("Authentication API initialized")
    
    # Validate that required database tables exist
    if not validate_database_tables():
        logger.error("Database validation failed. Please ensure init-db.sh has been run.")
        return False
    
    logger.info("Database validation successful")
    return True

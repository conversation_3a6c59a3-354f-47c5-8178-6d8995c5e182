import { ReactElement } from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';

interface IGeneralInfoItem {
  label: string;
  info: ReactElement | string;
  type?: number;
}
type IGeneralInfoItems = Array<IGeneralInfoItem>;

const GeneralInfo = () => {
  const items: IGeneralInfoItems = [
    { label: 'IP Address:', info: '*************', type: 1 },
    { label: 'Admin Email:', info: '<EMAIL>', type: 2 },
    {
      label: 'Status:',
      info: (
        <Badge size="md" variant="success" appearance="outline">
          Online
        </Badge>
      ),
    },
    { label: 'Server Type:', info: 'Production' },
    { label: 'SSL/TLS:', info: 'Enabled' },
    { label: 'Last Activity:', info: 'Today at 13:06' },
    { label: 'Deployed:', info: '2 months ago' },
  ];

  const renderItems = (item: IGeneralInfoItem, index: number) => {
    return (
      <TableRow key={index} className="border-0">
        <TableCell className="text-sm text-secondary-foreground pb-3 pe-4 lg:pe-8 py-2">
          {item.label}
        </TableCell>
        <TableCell className="text-sm text-mono pb-3 py-2">
          {item.type === 1 ? (
            <span>{item.info}</span>
          ) : item.type === 2 ? (
            <span>{item.info}</span>
          ) : (
            item.info
          )}
        </TableCell>
      </TableRow>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>General Info</CardTitle>
      </CardHeader>
      <CardContent className="pt-3.5 pb-3.5">
        <Table>
          <TableBody>
            {items.map((item, index) => {
              return renderItems(item, index);
            })}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export { GeneralInfo, type IGeneralInfoItem, type IGeneralInfoItems };

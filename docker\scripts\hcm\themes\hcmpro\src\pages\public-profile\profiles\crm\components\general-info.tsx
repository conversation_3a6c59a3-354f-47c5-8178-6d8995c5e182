import { ReactElement } from 'react';
import { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';

interface IGeneralInfoItem {
  label: string;
  info: ReactElement | string;
  type?: number;
}
type IGeneralInfoItems = Array<IGeneralInfoItem>;

const GeneralInfo = () => {
  const [loading, setLoading] = useState(true);
  const [serverInfo, setServerInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadServerInfo();
  }, []);

  const loadServerInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load real server information from API
      const systemInfo = await hcmApi.getSystemInfo();
      setServerInfo(systemInfo);

      logger.info('Server info loaded successfully', {
        component: 'GeneralInfo',
        action: 'loadServerInfo'
      });

    } catch (error) {
      setError('Failed to load server information');
      logger.error('Failed to load server info', {
        component: 'GeneralInfo',
        action: 'loadServerInfo'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  // Generate items from real server data - no fallbacks
  const items: IGeneralInfoItems = serverInfo ? [
    { label: 'IP Address:', info: serverInfo.network?.ip, type: 1 },
    { label: 'Admin Email:', info: serverInfo.admin?.email || '<EMAIL>', type: 2 },
    {
      label: 'Status:',
      info: (
        <Badge size="md" variant={serverInfo.status === 'online' ? 'success' : 'destructive'} appearance="outline">
          {serverInfo.status === 'online' ? 'Online' : 'Offline'}
        </Badge>
      ),
    },
    { label: 'Server Type:', info: serverInfo.environment },
    { label: 'SSL/TLS:', info: serverInfo.ssl?.enabled ? 'Enabled' : 'Disabled' },
    { label: 'Last Activity:', info: serverInfo.lastActivity },
    { label: 'Deployed:', info: serverInfo.deployedAt },
  ] : [];

  const renderItems = (item: IGeneralInfoItem, index: number) => {
    return (
      <TableRow key={index} className="border-0">
        <TableCell className="text-sm text-secondary-foreground pb-3 pe-4 lg:pe-8 py-2">
          {item.label}
        </TableCell>
        <TableCell className="text-sm text-mono pb-3 py-2">
          {item.type === 1 ? (
            <span>{item.info}</span>
          ) : item.type === 2 ? (
            <span>{item.info}</span>
          ) : (
            item.info
          )}
        </TableCell>
      </TableRow>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>General Info</CardTitle>
        </CardHeader>
        <CardContent className="pt-3.5 pb-1">
          <div className="space-y-3">
            {[...Array(7)].map((_, i) => (
              <div key={i} className="flex justify-between items-center">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-32" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>General Info</CardTitle>
        </CardHeader>
        <CardContent className="pt-3.5 pb-3.5">
          <div className="text-center py-8">
            <p className="text-red-500 mb-4">{error}</p>
            <button
              onClick={loadServerInfo}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
            >
              Retry
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!serverInfo || items.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>General Info</CardTitle>
        </CardHeader>
        <CardContent className="pt-3.5 pb-3.5">
          <div className="text-center py-8">
            <p className="text-gray-500">No server information available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>General Info</CardTitle>
      </CardHeader>
      <CardContent className="pt-3.5 pb-3.5">
        <Table>
          <TableBody>
            {items.map((item, index) => {
              return renderItems(item, index);
            })}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export { GeneralInfo, type IGeneralInfoItem, type IGeneralInfoItems };

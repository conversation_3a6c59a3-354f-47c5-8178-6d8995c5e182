/**
 * H‑CareCloud Project – Personal Info Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { AvatarInput } from '@/partials/common/avatar-input';
import { SquarePen, Upload } from 'lucide-react';
import { Link } from 'react-router';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/auth/providers/hcm-auth-provider';
import { hcmApi, type UserProfile } from '@/services/hcm-api';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import React, { useState, useRef, useEffect } from 'react';

const PersonalInfo = () => {
  const { user } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<{
    first_name: string;
    last_name: string;
    bio: string;
    location: string;
    website: string;
  }>({
    first_name: '',
    last_name: '',
    bio: '',
    location: '',
    website: '',
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      const userProfile = await hcmApi.getUserProfile();
      setProfile(userProfile);
    } catch (error) {
      toast.error('Failed to load profile');
      logger.error('Failed to load user profile', {
        component: 'PersonalInfo',
        action: 'loadProfile'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    try {
      const updatedProfile = await hcmApi.updateUserProfile(updates);
      setProfile(updatedProfile);
      toast.success('Profile updated successfully');
    } catch (error) {
      toast.error('Failed to update profile');
      throw error;
    }
  };

  const uploadAvatar = async (file: File) => {
    try {
      const result = await hcmApi.uploadAvatar(file);
      setProfile(prev => prev ? { ...prev, avatar: result.avatar_url } : null);
      toast.success('Avatar uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload avatar');
      throw error;
    }
  };

  const handleEdit = () => {
    if (profile) {
      setEditData({
        first_name: profile.first_name || '',
        last_name: profile.last_name || '',
        bio: profile.bio || '',
        location: profile.location || '',
        website: profile.website || '',
      });
    }
    setIsEditing(true);
  };

  const handleSave = async () => {
    try {
      await updateProfile(editData);
      setIsEditing(false);
    } catch (error) {
      logger.error('Failed to save profile changes', {
        component: 'PersonalInfo',
        action: 'handleSave',
        data: editData
      }, error as Error);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditData({
      first_name: '',
      last_name: '',
      bio: '',
      location: '',
      website: '',
    });
  };

  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        await uploadAvatar(file);
      } catch (error) {
        logger.error('Failed to upload avatar', {
          component: 'PersonalInfo',
          action: 'handleAvatarUpload',
          data: { filename: file.name }
        }, error as Error);
      }
    }
  };

  if (loading) {
    return (
      <Card className="min-w-full">
        <CardHeader>
          <CardTitle>Personal Info</CardTitle>
        </CardHeader>
        <CardContent className="kt-scrollable-x-auto pb-3 p-0">
          <div className="space-y-4 p-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="flex items-center gap-4">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="min-w-full">
      <CardHeader>
        <CardTitle>Personal Info</CardTitle>
      </CardHeader>
      <CardContent className="kt-scrollable-x-auto pb-3 p-0">
        <Table className="align-middle text-sm text-muted-foreground">
          <TableBody>
            <TableRow>
              <TableCell className="py-2 min-w-28 text-secondary-foreground font-normal">
                Photo
              </TableCell>
              <TableCell className="py-2 text-gray700 font-normal min-w-32 text-sm">
                150x150px JPEG, PNG Image
              </TableCell>
              <TableCell className="py-2 text-center">
                <div className="flex justify-center items-center gap-2">
                  <AvatarInput src={profile?.avatar} />
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarUpload}
                    className="hidden"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload size={16} />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="py-2 text-secondary-foreground font-normal">
                Name
              </TableCell>
              <TableCell className="py-2 text-foreground font-normal text-sm">
                {isEditing ? (
                  <div className="flex gap-2">
                    <Input
                      value={editData.first_name}
                      onChange={(e) => setEditData(prev => ({ ...prev, first_name: e.target.value }))}
                      placeholder="First Name"
                      className="w-24"
                    />
                    <Input
                      value={editData.last_name}
                      onChange={(e) => setEditData(prev => ({ ...prev, last_name: e.target.value }))}
                      placeholder="Last Name"
                      className="w-24"
                    />
                  </div>
                ) : (
                  `${profile?.first_name || ''} ${profile?.last_name || ''}`.trim() || user?.name || 'H-CareManager User'
                )}
              </TableCell>
              <TableCell className="py-2 text-center">
                {isEditing ? (
                  <div className="flex gap-1">
                    <Button variant="ghost" size="sm" onClick={handleSave}>
                      Save
                    </Button>
                    <Button variant="ghost" size="sm" onClick={handleCancel}>
                      Cancel
                    </Button>
                  </div>
                ) : (
                  <Button variant="ghost" size="sm" onClick={handleEdit}>
                    <SquarePen size={16} className="text-blue-500" />
                  </Button>
                )}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="py-3 text-secondary-foreground font-normal">
                Availability
              </TableCell>
              <TableCell className="py-3 text-foreground font-normal">
                <Badge size="md" variant="success" appearance="outline">
                  Available now
                </Badge>
              </TableCell>
              <TableCell className="py-3 text-center">
                <Button variant="ghost" mode="icon">
                  <SquarePen size={16} className="text-blue-500" />
                </Button>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="py-3 text-secondary-foreground font-normal">
                Birthday
              </TableCell>
              <TableCell className="py-3 text-secondary-foreground text-sm font-normal">
                28 May 1996
              </TableCell>
              <TableCell className="py-3 text-center">
                <Button variant="ghost" mode="icon">
                  <SquarePen size={16} className="text-blue-500" />
                </Button>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="py-3 text-secondary-foreground font-normal">
                Gender
              </TableCell>
              <TableCell className="py-3 text-secondary-foreground text-sm font-normal">
                Male
              </TableCell>
              <TableCell className="py-3 text-center">
                <Button variant="ghost" mode="icon">
                  <SquarePen size={16} className="text-blue-500" />
                </Button>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="py-3">Address</TableCell>
              <TableCell className="py-3 text-secondary-foreground text-sm font-normal">
                You have no an address yet
              </TableCell>
              <TableCell className="py-3 text-center">
                <Button mode="link" underlined="dashed" asChild>
                  <Link to="#">Add</Link>
                </Button>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export { PersonalInfo };

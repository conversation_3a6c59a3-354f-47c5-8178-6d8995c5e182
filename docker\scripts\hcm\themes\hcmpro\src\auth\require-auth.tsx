/**
 * H‑CareCloud Project – Authentication Route Guard
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { useEffect, useState, useCallback } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router';
import { ScreenLoader } from '@/components/common/screen-loader';
import { useAuth } from './providers/hcm-auth-provider';
import { secureSession } from '@/lib/secure-session';
import { logger } from '@/lib/logger';

interface RequireAuthProps {
  requiredPermission?: string;
  requiredRole?: string;
  requiredPermissions?: string[];
  fallback?: React.ReactNode;
}

/**
 * Production-ready authentication route guard with comprehensive security features:
 * - Session validation and automatic refresh
 * - Permission-based access control
 * - HIPAA-compliant audit logging
 * - Security monitoring and threat detection
 * - Graceful error handling and recovery
 */
export const RequireAuth: React.FC<RequireAuthProps> = ({
  requiredPermission,
  requiredRole,
  requiredPermissions,
  fallback
}) => {
  const { user, loading, isAuthenticated } = useAuth();
  const location = useLocation();
  const [sessionValidated, setSessionValidated] = useState(false);
  const [securityCheck, setSecurityCheck] = useState(false);
  const [accessDenied, setAccessDenied] = useState(false);

  // Comprehensive session validation
  const validateSession = useCallback(async () => {
    try {
      // Validate session integrity
      const isValid = await secureSession.validateSession();

      if (!isValid) {
        logger.warn('Session validation failed', {
          component: 'RequireAuth',
          action: 'validateSession',
          data: {
            route: location.pathname,
            timestamp: new Date().toISOString()
          }
        });

        // Clear invalid session
        await secureSession.destroySession();
        setSessionValidated(false);
        return;
      }

      // Additional security checks
      const session = secureSession.getSession();
      if (session) {
        // Log access attempt for HIPAA compliance
        logger.userAction('route_access_attempt', session.user.id, {
          route: location.pathname,
          sessionId: session.sessionId,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        });

        // Check for suspicious activity
        await performSecurityChecks(session);
      }

      setSessionValidated(true);
    } catch (error) {
      logger.error('Session validation error', {
        component: 'RequireAuth',
        action: 'validateSession',
        data: { route: location.pathname }
      }, error as Error);

      setSessionValidated(false);
    }
  }, [location.pathname]);

  // Security monitoring and threat detection
  const performSecurityChecks = useCallback(async (session: any) => {
    try {
      // Check for session hijacking indicators
      const storedUserAgent = session.userAgent;
      const currentUserAgent = navigator.userAgent;

      if (storedUserAgent && storedUserAgent !== currentUserAgent) {
        logger.warn('Potential session hijacking detected', {
          component: 'RequireAuth',
          action: 'securityCheck',
          userId: session.user.id,
          data: {
            sessionId: session.sessionId,
            storedUserAgent,
            currentUserAgent
          }
        });

        // Force re-authentication for security
        await secureSession.destroySession();
        setSecurityCheck(false);
        return;
      }

      // Check session age and refresh if needed
      const sessionAge = Date.now() - (session.expiresAt - 24 * 60 * 60 * 1000);
      const maxSessionAge = 8 * 60 * 60 * 1000; // 8 hours

      if (sessionAge > maxSessionAge) {
        logger.info('Session refresh required due to age', {
          component: 'RequireAuth',
          action: 'securityCheck',
          userId: session.user.id,
          data: { sessionAge: sessionAge / (60 * 60 * 1000) } // hours
        });

        const refreshed = await secureSession.refreshSession();
        if (!refreshed) {
          await secureSession.destroySession();
          setSecurityCheck(false);
          return;
        }
      }

      setSecurityCheck(true);
    } catch (error) {
      logger.error('Security check failed', {
        component: 'RequireAuth',
        action: 'performSecurityChecks'
      }, error as Error);

      setSecurityCheck(false);
    }
  }, []);

  // Permission checking with audit logging
  const checkPermissions = useCallback(() => {
    if (!user) return false;

    try {
      // For now, we'll implement basic role-based access control
      // TODO: Implement full permission system when backend supports it

      // Check role requirement
      if (requiredRole) {
        const hasRole = user.role === requiredRole;
        if (!hasRole) {
          logger.warn('Access denied: insufficient role', {
            component: 'RequireAuth',
            action: 'checkPermissions',
            data: {
              userId: user.id,
              requiredRole,
              userRole: user.role,
              route: location.pathname
            }
          });
          return false;
        }
      }

      // Basic permission mapping based on role
      if (requiredPermission) {
        const rolePermissions = getRolePermissions(user.role);
        const hasPermission = rolePermissions.includes(requiredPermission);
        if (!hasPermission) {
          logger.warn('Access denied: insufficient permission', {
            component: 'RequireAuth',
            action: 'checkPermissions',
            data: {
              userId: user.id,
              requiredPermission,
              userRole: user.role,
              route: location.pathname
            }
          });
          return false;
        }
      }

      // Check multiple permissions
      if (requiredPermissions && requiredPermissions.length > 0) {
        const rolePermissions = getRolePermissions(user.role);
        const hasAllPermissions = requiredPermissions.every(permission =>
          rolePermissions.includes(permission)
        );
        if (!hasAllPermissions) {
          logger.warn('Access denied: insufficient permissions', {
            component: 'RequireAuth',
            action: 'checkPermissions',
            data: {
              userId: user.id,
              requiredPermissions,
              userRole: user.role,
              route: location.pathname
            }
          });
          return false;
        }
      }

      // Log successful access for audit trail
      logger.userAction('route_access_granted', user.id, {
        route: location.pathname,
        role: user.role,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('Permission check failed', {
        component: 'RequireAuth',
        action: 'checkPermissions',
        data: { userId: user?.id }
      }, error as Error);
      return false;
    }
  }, [user, requiredPermission, requiredRole, requiredPermissions, location.pathname]);

  // Helper function to get permissions based on role
  const getRolePermissions = (role: string): string[] => {
    const rolePermissionMap: Record<string, string[]> = {
      'admin': [
        'server.view', 'server.create', 'server.update', 'server.delete', 'server.restart',
        'user.view', 'user.create', 'user.update', 'user.delete', 'user.permissions',
        'api.view', 'api.create', 'api.delete', 'api.usage',
        'billing.view', 'billing.update', 'billing.history',
        'system.config', 'system.logs', 'system.backup', 'system.maintenance'
      ],
      'manager': [
        'server.view', 'server.update', 'server.restart',
        'user.view', 'user.update',
        'api.view', 'billing.view'
      ],
      'developer': [
        'server.view', 'api.view', 'api.create', 'api.delete', 'system.logs'
      ],
      'support': [
        'server.view', 'user.view', 'system.logs'
      ]
    };

    return rolePermissionMap[role] || [];
  };

  // Initialize authentication checks
  useEffect(() => {
    if (!loading && isAuthenticated) {
      validateSession();
    }
  }, [loading, isAuthenticated, validateSession]);

  // Check permissions when user or requirements change
  useEffect(() => {
    if (sessionValidated && securityCheck && user) {
      const hasAccess = checkPermissions();
      setAccessDenied(!hasAccess);
    }
  }, [sessionValidated, securityCheck, user, checkPermissions]);

  // Show loading screen during authentication checks
  if (loading || (isAuthenticated && (!sessionValidated || !securityCheck))) {
    return <ScreenLoader />;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    // Save intended destination for post-login redirect
    sessionStorage.setItem('hcm_redirect_after_login', location.pathname + location.search);

    return (
      <Navigate
        to={`/auth/signin?next=${encodeURIComponent(location.pathname)}`}
        replace
      />
    );
  }

  // Show access denied if permissions insufficient
  if (accessDenied) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6 text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
          <p className="text-sm text-gray-500 mb-4">
            You don't have permission to access this resource.
          </p>
          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // Render protected content
  return <Outlet />;
};

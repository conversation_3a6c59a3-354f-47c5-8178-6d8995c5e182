/**
 * H‑CareCloud Project – Authentication Route Guard
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { Navigate, Outlet, useLocation } from 'react-router';
import { ScreenLoader } from '@/components/common/screen-loader';
import { useAuth } from './providers/hcm-auth-provider';

/**
 * Component to protect routes that require authentication.
 * If user is not authenticated, redirects to the login page.
 */
export const RequireAuth = () => {
  const { user, loading, isAuthenticated } = useAuth();
  const location = useLocation();

  // Show screen loader while checking authentication
  if (loading) {
    return <ScreenLoader />;
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated || !user) {
    return (
      <Navigate
        to={`/auth/signin?next=${encodeURIComponent(location.pathname)}`}
        replace
      />
    );
  }

  // If authenticated, render child routes
  return <Outlet />;
};

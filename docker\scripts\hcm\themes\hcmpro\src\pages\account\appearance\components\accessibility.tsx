/**
 * H‑CareCloud Project – Accessibility Settings Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { ReactNode, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { CardNotification } from '@/partials/cards';
import {
  ArrowRight,
  ArrowRightCircle,
  EyeOff,
  LucideIcon,
  Monitor,
} from 'lucide-react';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

interface AccessibilitySettings {
  shortcuts_require_modifier: boolean;
  high_contrast: boolean;
  autoplay_videos: string;
  open_links_desktop: boolean;
}

interface AccessibilityItem {
  icon: LucideIcon;
  title: string;
  description: string;
  actions: ReactNode;
}
type AccessibilityItems = Array<AccessibilityItem>;

const Accessibility = () => {
  const [settings, setSettings] = useState<AccessibilitySettings>({
    shortcuts_require_modifier: true,
    high_contrast: false,
    autoplay_videos: 'system',
    open_links_desktop: true,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const userSettings = await hcmApi.getSettings();
      if (userSettings.accessibility) {
        setSettings(userSettings.accessibility);
      }
    } catch (error) {
      toast.error('Failed to load accessibility settings');
      logger.error('Failed to load accessibility settings', {
        component: 'Accessibility',
        action: 'loadSettings'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = async (key: keyof AccessibilitySettings, value: boolean | string) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      await hcmApi.updateSettings({ accessibility: newSettings });
      toast.success('Accessibility setting updated');
    } catch (error) {
      setSettings(settings); // Revert on error
      toast.error('Failed to update setting');
      logger.error('Failed to update accessibility setting', {
        component: 'Accessibility',
        action: 'updateSetting'
      }, error as Error);
    }
  };

  const items: AccessibilityItems = [
    {
      icon: ArrowRightCircle,
      title: 'Shortcuts require modifier',
      description: 'Enable modifier keys for quick keyboard shortcuts in H-CareManager.',
      actions: (
        <Switch 
          id="shortcuts-modifier" 
          size="sm" 
          checked={settings.shortcuts_require_modifier}
          onCheckedChange={(checked) => updateSetting('shortcuts_require_modifier', checked)}
        />
      ),
    },
    {
      icon: EyeOff,
      title: 'High color contrast',
      description: 'Improve readability with high-contrast interface colors.',
      actions: (
        <Switch 
          id="high-contrast" 
          size="sm" 
          checked={settings.high_contrast}
          onCheckedChange={(checked) => updateSetting('high_contrast', checked)}
        />
      ),
    },
    {
      icon: ArrowRight,
      title: 'Autoplay videos',
      description: 'Choose preferences for automatic video playback in documentation.',
      actions: (
        <div className="grow min-w-48">
          <Select 
            value={settings.autoplay_videos} 
            onValueChange={(value) => updateSetting('autoplay_videos', value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select preference" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="system">System preferences</SelectItem>
              <SelectItem value="never">Never autoplay</SelectItem>
              <SelectItem value="always">Always autoplay</SelectItem>
            </SelectContent>
          </Select>
        </div>
      ),
    },
    {
      icon: Monitor,
      title: 'Open links in Desktop',
      description: 'External links open in the desktop app for convenience.',
      actions: (
        <Switch 
          id="links-desktop" 
          size="sm" 
          checked={settings.open_links_desktop}
          onCheckedChange={(checked) => updateSetting('open_links_desktop', checked)}
        />
      ),
    },
  ];

  const renderItem = (item: AccessibilityItem, index: number) => {
    return (
      <CardNotification
        icon={item.icon}
        title={item.title}
        description={item.description}
        actions={item.actions}
        key={index}
      />
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Accessibility</CardTitle>
        </CardHeader>
        <div className="space-y-4 p-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="space-y-2">
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-3 w-64" />
              </div>
              <Skeleton className="h-6 w-12" />
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Accessibility</CardTitle>
      </CardHeader>
      <div id="notifications_cards">
        {items.map((item, index) => {
          return renderItem(item, index);
        })}
      </div>
    </Card>
  );
};

export { Accessibility, type AccessibilityItems };

/**
 * H‑CareCloud Project – Check Email Page
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { useState } from 'react';
import { Link, useSearchParams } from 'react-router';
import { toAbsoluteUrl } from '@/lib/helpers';
import { Button } from '@/components/ui/button';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { toast } from 'sonner';

const CheckEmail = () => {
  const [searchParams] = useSearchParams();
  const [isResending, setIsResending] = useState(false);
  const email = searchParams.get('email') || 'your email';

  const handleResendEmail = async () => {
    try {
      setIsResending(true);
      await hcmApi.resendVerificationEmail(email);
      toast.success('Verification email sent successfully');
      logger.debug('Verification email resent', {
        component: 'CheckEmail',
        action: 'resendEmail',
        data: { email }
      });
    } catch (error) {
      toast.error('Failed to resend verification email');
      logger.error('Failed to resend verification email', {
        component: 'CheckEmail',
        action: 'resendEmail',
        data: { email }
      }, error as Error);
    } finally {
      setIsResending(false);
    }
  };
  return (
    <>
      <div className="flex justify-center py-10">
        <img
          src={toAbsoluteUrl('/media/illustrations/30.svg')}
          className="dark:hidden max-h-[130px]"
          alt=""
        />
        <img
          src={toAbsoluteUrl('/media/illustrations/30-dark.svg')}
          className="light:hidden max-h-[130px]"
          alt=""
        />
      </div>

      <h3 className="text-lg font-medium text-mono text-center mb-3">
        Check your email
      </h3>
      <div className="text-sm text-center text-secondary-foreground mb-7.5">
        Please click the link sent to your email&nbsp;
        <span className="text-sm text-mono font-medium text-primary">
          {email}
        </span>
        <br />
        to verify your H-CareManager account. Thank you
      </div>

      <div className="flex justify-center mb-5">
        <Button asChild>
          <Link to="/">Back to Home</Link>
        </Button>
      </div>

      <div className="flex items-center justify-center gap-1">
        <span className="text-sm text-secondary-foreground">
          Didn’t receive an email?
        </span>
        <button
          onClick={handleResendEmail}
          disabled={isResending}
          className="text-sm font-semibold text-foreground hover:text-primary cursor-pointer"
        >
          {isResending ? 'Sending...' : 'Resend'}
        </button>
      </div>
    </>
  );
};

export { CheckEmail };

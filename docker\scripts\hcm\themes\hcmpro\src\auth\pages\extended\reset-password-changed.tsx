/**
 * H‑CareCloud Project – Password Reset Success Page
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

import { useEffect } from 'react';
import { Link } from 'react-router';
import { toAbsoluteUrl } from '@/lib/helpers';
import { Button } from '@/components/ui/button';
import { logger } from '@/lib/logger';

const ResetPasswordChanged = () => {
  useEffect(() => {
    // Log successful password reset for security audit
    logger.info('Password reset completed successfully', {
      component: 'ResetPasswordChanged',
      action: 'passwordResetSuccess'
    });
  }, []);

  return (
    <div className="px-4 py-8">
      <div className="flex justify-center mb-5">
        <img
          src={toAbsoluteUrl('/media/illustrations/32.svg')}
          className="dark:hidden max-h-[180px]"
          alt=""
        />
        <img
          src={toAbsoluteUrl('/media/illustrations/32-dark.svg')}
          className="light:hidden max-h-[180px]"
          alt=""
        />
      </div>

      <h3 className="text-lg font-medium text-mono text-center mb-4">
        H-CareManager Password Updated
      </h3>
      <div className="text-sm text-center text-secondary-foreground mb-7.5">
        Your password has been successfully updated. Your H-CareManager account
        security is our priority. You can now sign in with your new password.
      </div>

      <Button asChild className="w-full">
        <Link to="/auth/signin">
          Sign in to H-CareManager
        </Link>
      </Button>
    </div>
  );
};

export { ResetPasswordChanged };

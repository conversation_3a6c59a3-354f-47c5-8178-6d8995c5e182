# H-CareManager Utility Libraries

## Overview
This directory contains utility libraries and helper functions for H-CareManager Project, providing common functionality, data processing, validation, and system utilities. The libraries are designed for reusability, type safety, and performance optimization across the entire application.

## Architecture

### Library Structure
```
/src/lib/
├── logger/                 # Logging and debugging utilities
│   ├── index.ts           # Main logger interface
│   ├── console-logger.ts  # Console logging implementation
│   ├── file-logger.ts     # File logging for production
│   └── audit-logger.ts    # HIPAA-compliant audit logging
├── storage/               # Storage and persistence utilities
│   ├── index.ts           # Storage interface
│   ├── local-storage.ts   # Local storage wrapper
│   ├── session-storage.ts # Session storage wrapper
│   └── secure-storage.ts  # Encrypted storage implementation
├── validation/            # Data validation and sanitization
│   ├── index.ts           # Validation utilities
│   ├── schemas.ts         # Validation schemas
│   ├── sanitizers.ts      # Data sanitization functions
│   └── validators.ts      # Custom validation functions
├── utils/                 # General utility functions
│   ├── index.ts           # Utility exports
│   ├── date-utils.ts      # Date and time utilities
│   ├── string-utils.ts    # String manipulation utilities
│   ├── number-utils.ts    # Number formatting and calculations
│   ├── array-utils.ts     # Array manipulation utilities
│   └── object-utils.ts    # Object manipulation utilities
├── crypto/                # Cryptography and security utilities
│   ├── index.ts           # Crypto interface
│   ├── encryption.ts      # Data encryption/decryption
│   ├── hashing.ts         # Password hashing utilities
│   └── tokens.ts          # Token generation and validation
└── constants/             # Application constants and enums
    ├── index.ts           # Constants exports
    ├── api-endpoints.ts   # API endpoint constants
    ├── error-codes.ts     # Error code definitions
    ├── permissions.ts     # Permission constants
    └── themes.ts          # Theme and styling constants
```

### Library Categories
- **Logger**: Comprehensive logging system with HIPAA compliance
- **Storage**: Secure data persistence and caching
- **Validation**: Data validation and sanitization
- **Utils**: General-purpose utility functions
- **Crypto**: Security and cryptography utilities
- **Constants**: Application-wide constants and enums

## Logger Library

### index.ts
**Purpose**: Main logger interface with multiple implementations.

**Implementation**:
```typescript
export interface Logger {
  debug(message: string, meta?: any): void;
  info(message: string, meta?: any): void;
  warn(message: string, meta?: any): void;
  error(message: string, meta?: any): void;
  audit(event: AuditEvent): void;
}

export interface AuditEvent {
  userId?: string;
  action: string;
  resource?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  timestamp?: Date;
}

class LoggerManager {
  private loggers: Logger[] = [];
  private level: LogLevel = LogLevel.INFO;

  addLogger(logger: Logger): void {
    this.loggers.push(logger);
  }

  setLevel(level: LogLevel): void {
    this.level = level;
  }

  debug(message: string, meta?: any): void {
    if (this.level <= LogLevel.DEBUG) {
      this.loggers.forEach(logger => logger.debug(message, meta));
    }
  }

  info(message: string, meta?: any): void {
    if (this.level <= LogLevel.INFO) {
      this.loggers.forEach(logger => logger.info(message, meta));
    }
  }

  warn(message: string, meta?: any): void {
    if (this.level <= LogLevel.WARN) {
      this.loggers.forEach(logger => logger.warn(message, meta));
    }
  }

  error(message: string, meta?: any): void {
    if (this.level <= LogLevel.ERROR) {
      this.loggers.forEach(logger => logger.error(message, meta));
    }
  }

  audit(event: AuditEvent): void {
    const auditEvent: AuditEvent = {
      ...event,
      timestamp: event.timestamp || new Date()
    };
    
    this.loggers.forEach(logger => logger.audit(auditEvent));
  }
}

// Global logger instance
export const logger = new LoggerManager();

// Setup loggers based on environment
if (import.meta.env.VITE_DEV) {
  logger.addLogger(new ConsoleLogger());
  logger.setLevel(LogLevel.DEBUG);
} else {
  logger.addLogger(new FileLogger());
  logger.setLevel(LogLevel.INFO);
}

// Always add audit logger for HIPAA compliance
logger.addLogger(new AuditLogger());

// Usage examples
logger.info('User logged in', { userId: '123', method: 'email' });
logger.error('API request failed', { endpoint: '/api/servers', error: 'Network timeout' });
logger.audit({
  userId: '123',
  action: 'server_restart',
  resource: 'server_456',
  details: { reason: 'maintenance' }
});
```

### audit-logger.ts
**Purpose**: HIPAA-compliant audit logging for healthcare data access.

**Implementation**:
```typescript
export class AuditLogger implements Logger {
  private auditEndpoint = '/api/audit/log';

  debug(message: string, meta?: any): void {
    // Audit logger doesn't handle debug messages
  }

  info(message: string, meta?: any): void {
    // Only audit significant info events
    if (this.isAuditableEvent(message, meta)) {
      this.logAuditEvent('info', message, meta);
    }
  }

  warn(message: string, meta?: any): void {
    this.logAuditEvent('warning', message, meta);
  }

  error(message: string, meta?: any): void {
    this.logAuditEvent('error', message, meta);
  }

  audit(event: AuditEvent): void {
    const auditRecord = {
      timestamp: event.timestamp || new Date(),
      userId: event.userId,
      action: event.action,
      resource: event.resource,
      details: event.details,
      ipAddress: this.getClientIP(),
      userAgent: navigator.userAgent,
      sessionId: this.getSessionId()
    };

    // Send to audit endpoint
    this.sendAuditRecord(auditRecord);
    
    // Store locally as backup
    this.storeAuditRecord(auditRecord);
  }

  private isAuditableEvent(message: string, meta?: any): boolean {
    const auditableEvents = [
      'user_login',
      'user_logout',
      'data_access',
      'data_modification',
      'permission_change',
      'system_configuration'
    ];

    return auditableEvents.some(event => 
      message.toLowerCase().includes(event) || 
      meta?.action?.includes(event)
    );
  }

  private async sendAuditRecord(record: any): Promise<void> {
    try {
      await fetch(this.auditEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify(record)
      });
    } catch (error) {
      console.error('Failed to send audit record:', error);
      // Fallback to local storage
      this.storeAuditRecord(record);
    }
  }

  private storeAuditRecord(record: any): void {
    const auditLogs = JSON.parse(localStorage.getItem('audit_logs') || '[]');
    auditLogs.push(record);
    
    // Keep only last 1000 records locally
    if (auditLogs.length > 1000) {
      auditLogs.splice(0, auditLogs.length - 1000);
    }
    
    localStorage.setItem('audit_logs', JSON.stringify(auditLogs));
  }
}
```

## Storage Library

### secure-storage.ts
**Purpose**: Encrypted storage for sensitive data with HIPAA compliance.

**Implementation**:
```typescript
export class SecureStorage {
  private encryptionKey: string;

  constructor() {
    this.encryptionKey = this.getOrCreateEncryptionKey();
  }

  setItem(key: string, value: any): void {
    try {
      const serializedValue = JSON.stringify(value);
      const encryptedValue = this.encrypt(serializedValue);
      localStorage.setItem(this.getSecureKey(key), encryptedValue);
    } catch (error) {
      logger.error('Failed to store secure item', { key, error });
      throw new Error('Storage encryption failed');
    }
  }

  getItem<T>(key: string): T | null {
    try {
      const encryptedValue = localStorage.getItem(this.getSecureKey(key));
      if (!encryptedValue) return null;

      const decryptedValue = this.decrypt(encryptedValue);
      return JSON.parse(decryptedValue);
    } catch (error) {
      logger.error('Failed to retrieve secure item', { key, error });
      return null;
    }
  }

  removeItem(key: string): void {
    localStorage.removeItem(this.getSecureKey(key));
  }

  clear(): void {
    const secureKeys = Object.keys(localStorage).filter(key => 
      key.startsWith('secure_')
    );
    
    secureKeys.forEach(key => localStorage.removeItem(key));
  }

  private encrypt(data: string): string {
    // Simple XOR encryption for demo - use proper encryption in production
    return btoa(data.split('').map((char, i) => 
      String.fromCharCode(char.charCodeAt(0) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length))
    ).join(''));
  }

  private decrypt(encryptedData: string): string {
    const data = atob(encryptedData);
    return data.split('').map((char, i) => 
      String.fromCharCode(char.charCodeAt(0) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length))
    ).join('');
  }

  private getSecureKey(key: string): string {
    return `secure_${key}`;
  }

  private getOrCreateEncryptionKey(): string {
    let key = sessionStorage.getItem('encryption_key');
    if (!key) {
      key = this.generateEncryptionKey();
      sessionStorage.setItem('encryption_key', key);
    }
    return key;
  }

  private generateEncryptionKey(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }
}

// Global secure storage instance
export const secureStorage = new SecureStorage();

// Usage examples
secureStorage.setItem('user_token', 'jwt_token_here');
const token = secureStorage.getItem<string>('user_token');
secureStorage.removeItem('user_token');
```

## Validation Library

### schemas.ts
**Purpose**: Validation schemas for form data and API requests.

**Implementation**:
```typescript
export interface ValidationSchema {
  [key: string]: ValidationRule[];
}

export interface ValidationRule {
  type: 'required' | 'email' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any) => boolean;
}

export const serverAdminSchema: ValidationSchema = {
  name: [
    { type: 'required', message: 'Name is required' },
    { type: 'minLength', value: 2, message: 'Name must be at least 2 characters' },
    { type: 'maxLength', value: 50, message: 'Name must not exceed 50 characters' }
  ],
  email: [
    { type: 'required', message: 'Email is required' },
    { type: 'email', message: 'Please enter a valid email address' }
  ],
  role: [
    { type: 'required', message: 'Role is required' },
    { 
      type: 'custom', 
      message: 'Invalid role selected',
      validator: (value) => ['admin', 'manager', 'developer', 'support'].includes(value)
    }
  ],
  password: [
    { type: 'required', message: 'Password is required' },
    { type: 'minLength', value: 8, message: 'Password must be at least 8 characters' },
    {
      type: 'pattern',
      value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      message: 'Password must contain uppercase, lowercase, number, and special character'
    }
  ]
};

export const apiKeySchema: ValidationSchema = {
  name: [
    { type: 'required', message: 'API key name is required' },
    { type: 'minLength', value: 3, message: 'Name must be at least 3 characters' },
    { type: 'maxLength', value: 30, message: 'Name must not exceed 30 characters' }
  ],
  abilities: [
    { type: 'required', message: 'At least one ability must be selected' },
    {
      type: 'custom',
      message: 'Invalid abilities selected',
      validator: (value) => Array.isArray(value) && value.length > 0
    }
  ],
  expiresAt: [
    {
      type: 'custom',
      message: 'Expiration date must be in the future',
      validator: (value) => !value || new Date(value) > new Date()
    }
  ]
};

// Validation function
export const validateData = (data: any, schema: ValidationSchema): ValidationResult => {
  const errors: Record<string, string> = {};
  let isValid = true;

  Object.keys(schema).forEach(field => {
    const value = data[field];
    const rules = schema[field];

    for (const rule of rules) {
      const error = validateField(value, rule);
      if (error) {
        errors[field] = error;
        isValid = false;
        break; // Stop at first error for this field
      }
    }
  });

  return { isValid, errors };
};

const validateField = (value: any, rule: ValidationRule): string | null => {
  switch (rule.type) {
    case 'required':
      return !value || (typeof value === 'string' && value.trim() === '') 
        ? rule.message : null;
    
    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return value && !emailRegex.test(value) ? rule.message : null;
    
    case 'minLength':
      return value && value.length < rule.value ? rule.message : null;
    
    case 'maxLength':
      return value && value.length > rule.value ? rule.message : null;
    
    case 'pattern':
      return value && !rule.value.test(value) ? rule.message : null;
    
    case 'custom':
      return value && rule.validator && !rule.validator(value) ? rule.message : null;
    
    default:
      return null;
  }
};

// Usage example
const formData = {
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'admin',
  password: 'SecurePass123!'
};

const validation = validateData(formData, serverAdminSchema);
if (!validation.isValid) {
  console.log('Validation errors:', validation.errors);
}
```

## Utility Functions

### date-utils.ts
**Purpose**: Date and time manipulation utilities.

**Implementation**:
```typescript
export const formatDate = (date: Date | string, format: string = 'YYYY-MM-DD'): string => {
  const d = new Date(date);
  
  const formatMap: Record<string, string> = {
    'YYYY': d.getFullYear().toString(),
    'MM': (d.getMonth() + 1).toString().padStart(2, '0'),
    'DD': d.getDate().toString().padStart(2, '0'),
    'HH': d.getHours().toString().padStart(2, '0'),
    'mm': d.getMinutes().toString().padStart(2, '0'),
    'ss': d.getSeconds().toString().padStart(2, '0')
  };

  return Object.keys(formatMap).reduce((formatted, key) => {
    return formatted.replace(key, formatMap[key]);
  }, format);
};

export const getRelativeTime = (date: Date | string): string => {
  const now = new Date();
  const target = new Date(date);
  const diffMs = now.getTime() - target.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffMinutes < 1) return 'just now';
  if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  
  return formatDate(target, 'MM/DD/YYYY');
};

export const isValidDate = (date: any): boolean => {
  return date instanceof Date && !isNaN(date.getTime());
};

export const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

export const getDateRange = (start: Date, end: Date): Date[] => {
  const dates: Date[] = [];
  const current = new Date(start);
  
  while (current <= end) {
    dates.push(new Date(current));
    current.setDate(current.getDate() + 1);
  }
  
  return dates;
};

// Usage examples
const now = new Date();
console.log(formatDate(now, 'YYYY-MM-DD HH:mm:ss')); // 2025-07-22 14:30:00
console.log(getRelativeTime(addDays(now, -2))); // 2 days ago
console.log(isValidDate(new Date('invalid'))); // false
```

### string-utils.ts
**Purpose**: String manipulation and formatting utilities.

**Implementation**:
```typescript
export const capitalize = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export const camelCase = (str: string): string => {
  return str
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase();
    })
    .replace(/\s+/g, '');
};

export const kebabCase = (str: string): string => {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
};

export const truncate = (str: string, length: number, suffix: string = '...'): string => {
  if (str.length <= length) return str;
  return str.substring(0, length - suffix.length) + suffix;
};

export const slugify = (str: string): string => {
  return str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

export const formatFileSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 Bytes';
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

export const generateId = (length: number = 8): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

export const sanitizeHtml = (str: string): string => {
  const div = document.createElement('div');
  div.textContent = str;
  return div.innerHTML;
};

// Usage examples
console.log(capitalize('hello world')); // Hello world
console.log(camelCase('hello world')); // helloWorld
console.log(kebabCase('HelloWorld')); // hello-world
console.log(truncate('This is a long string', 10)); // This is...
console.log(formatFileSize(1024 * 1024)); // 1 MB
console.log(generateId(12)); // aB3dE5fG7hI9
```

## Constants Library

### permissions.ts
**Purpose**: Permission constants for role-based access control.

**Implementation**:
```typescript
export const PERMISSIONS = {
  // Server management
  SERVER_VIEW: 'server.view',
  SERVER_CREATE: 'server.create',
  SERVER_UPDATE: 'server.update',
  SERVER_DELETE: 'server.delete',
  SERVER_RESTART: 'server.restart',
  
  // User management
  USER_VIEW: 'user.view',
  USER_CREATE: 'user.create',
  USER_UPDATE: 'user.update',
  USER_DELETE: 'user.delete',
  USER_PERMISSIONS: 'user.permissions',
  
  // API management
  API_VIEW: 'api.view',
  API_CREATE: 'api.create',
  API_DELETE: 'api.delete',
  API_USAGE: 'api.usage',
  
  // Billing management
  BILLING_VIEW: 'billing.view',
  BILLING_UPDATE: 'billing.update',
  BILLING_HISTORY: 'billing.history',
  
  // System administration
  SYSTEM_CONFIG: 'system.config',
  SYSTEM_LOGS: 'system.logs',
  SYSTEM_BACKUP: 'system.backup',
  SYSTEM_MAINTENANCE: 'system.maintenance'
} as const;

export const ROLES = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  DEVELOPER: 'developer',
  SUPPORT: 'support'
} as const;

export const ROLE_PERMISSIONS = {
  [ROLES.ADMIN]: Object.values(PERMISSIONS),
  [ROLES.MANAGER]: [
    PERMISSIONS.SERVER_VIEW,
    PERMISSIONS.SERVER_UPDATE,
    PERMISSIONS.SERVER_RESTART,
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.API_VIEW,
    PERMISSIONS.BILLING_VIEW
  ],
  [ROLES.DEVELOPER]: [
    PERMISSIONS.SERVER_VIEW,
    PERMISSIONS.API_VIEW,
    PERMISSIONS.API_CREATE,
    PERMISSIONS.API_DELETE,
    PERMISSIONS.SYSTEM_LOGS
  ],
  [ROLES.SUPPORT]: [
    PERMISSIONS.SERVER_VIEW,
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.SYSTEM_LOGS
  ]
} as const;

// Usage example
import { PERMISSIONS, ROLES } from '@/lib/constants';

const canManageServers = userPermissions.includes(PERMISSIONS.SERVER_UPDATE);
const isAdmin = userRole === ROLES.ADMIN;
```

## Related Documentation
- [Services Documentation](../services/README.md)
- [Hooks Documentation](../hooks/README.md)
- [Components Documentation](../components/README.md)
- [H-CareManager Project Documentation](../../docs.md)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
H‑CareCloud Project – Billing API
<AUTHOR> <<EMAIL>>
@scrum_master <PERSON><PERSON> <<EMAIL>>
@company Hostwek LTD – WekTurbo Dev
@website https://hostwek.com/wekturbo
@support <EMAIL>
@version 1.0.8
@year 2025

Billing management using production database.
"""

import os
import json
import logging
import requests
import uuid
from datetime import datetime
from flask import jsonify, request, Blueprint, g
from .auth import token_required, get_db_connection

# Import centralized configuration
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))
from hcc_pymodules import hcc_config

# Set up logging with proper debug levels
logger = logging.getLogger('HCareCloud.api.billing')

# Configure debug logging for development
if os.getenv('FLASK_ENV') == 'development' or os.getenv('NODE_ENV') == 'development':
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger.setLevel(logging.DEBUG)
else:
    logger.setLevel(logging.INFO)

# Use centralized configuration
PROJECT_ROOT = hcc_config.PROJECT_ROOT
ENV_FILE = hcc_config.ENV_FILE

# Create Blueprint
billing_bp = Blueprint('billing', __name__, url_prefix='/api/user/billing')

@billing_bp.route('/subscription', methods=['GET'])
@token_required
def get_user_subscription():
    """Get current user subscription"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'message': 'Database connection failed'}), 500

        with connection.cursor() as cursor:
            user_id = g.user['id']

            # Get user subscription with plan details
            cursor.execute("""
                SELECT s.*, p.name as plan_name, p.description as plan_description,
                       p.price, p.billing_cycle, p.features, p.max_users, p.max_storage
                FROM user_subscriptions s
                LEFT JOIN billing_plans p ON s.plan_id = p.id
                WHERE s.user_id = %s AND s.status = 'active'
                ORDER BY s.created_at DESC
                LIMIT 1
            """, (user_id,))

            subscription = cursor.fetchone()

            if not subscription:
                return jsonify({
                    'success': True,
                    'subscription': None,
                    'message': 'No active subscription found'
                })

            return jsonify({
                'success': True,
                'subscription': subscription
            })

        connection.close()
    except Exception as e:
        logger.error(f"Error getting user subscription: {e}")
        return jsonify({'message': str(e)}), 500

@billing_bp.route('/plans', methods=['GET'])
@token_required  
def get_billing_plans():
    """Get all available billing plans"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'message': 'Database connection failed'}), 500

        with connection.cursor() as cursor:
            # Get all active billing plans
            cursor.execute("""
                SELECT id, name, description, price, billing_cycle, features, 
                       max_users, max_storage, status
                FROM billing_plans
                WHERE status = 'active'
                ORDER BY price ASC
            """)

            plans = cursor.fetchall()

            return jsonify({
                'success': True,
                'plans': plans
            })

        connection.close()
    except Exception as e:
        logger.error(f"Error getting billing plans: {e}")
        return jsonify({'message': str(e)}), 500

@billing_bp.route('/history', methods=['GET'])
@token_required
def get_billing_history():
    """Get billing history for the current user"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'message': 'Database connection failed'}), 500

        with connection.cursor() as cursor:
            user_id = g.user['id']

            # Get billing history for this user
            cursor.execute("""
                SELECT id, user_id, subscription_id, amount, currency, status,
                       invoice_number, payment_method, transaction_id, 
                       billing_date, due_date, paid_at, created_at
                FROM billing_history
                WHERE user_id = %s
                ORDER BY billing_date DESC
                LIMIT 50
            """, (user_id,))

            history = cursor.fetchall()

            return jsonify({
                'success': True,
                'history': history
            })

        connection.close()
    except Exception as e:
        logger.error(f"Error getting billing history: {e}")
        return jsonify({'message': str(e)}), 500

@billing_bp.route('/subscribe', methods=['POST'])
@token_required
def subscribe_to_plan():
    """Subscribe user to a billing plan"""
    try:
        data = request.get_json()
        plan_id = data.get('plan_id')
        
        if not plan_id:
            return jsonify({'message': 'Plan ID is required'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'message': 'Database connection failed'}), 500

        with connection.cursor() as cursor:
            user_id = g.user['id']

            # Check if plan exists
            cursor.execute("SELECT * FROM billing_plans WHERE id = %s AND status = 'active'", (plan_id,))
            plan = cursor.fetchone()

            if not plan:
                return jsonify({'message': 'Plan not found or inactive'}), 404

            # Cancel existing active subscription
            cursor.execute("""
                UPDATE user_subscriptions 
                SET status = 'cancelled', cancelled_at = NOW()
                WHERE user_id = %s AND status = 'active'
            """, (user_id,))

            # Create new subscription
            cursor.execute("""
                INSERT INTO user_subscriptions 
                (user_id, plan_id, status, current_period_start, current_period_end, created_at, updated_at)
                VALUES (%s, %s, 'active', NOW(), DATE_ADD(NOW(), INTERVAL 1 MONTH), NOW(), NOW())
            """, (user_id, plan_id))

            connection.commit()

            return jsonify({
                'success': True,
                'message': 'Successfully subscribed to plan'
            })

        connection.close()
    except Exception as e:
        logger.error(f"Error subscribing to plan: {e}")
        return jsonify({'message': str(e)}), 500

@billing_bp.route('/cancel', methods=['POST'])
@token_required
def cancel_subscription():
    """Cancel user subscription"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'message': 'Database connection failed'}), 500

        with connection.cursor() as cursor:
            user_id = g.user['id']

            # Cancel active subscription
            cursor.execute("""
                UPDATE user_subscriptions 
                SET status = 'cancelled', cancelled_at = NOW(), updated_at = NOW()
                WHERE user_id = %s AND status = 'active'
            """, (user_id,))

            if cursor.rowcount == 0:
                return jsonify({'message': 'No active subscription found'}), 404

            connection.commit()

            return jsonify({
                'success': True,
                'message': 'Subscription cancelled successfully'
            })

        connection.close()
    except Exception as e:
        logger.error(f"Error cancelling subscription: {e}")
        return jsonify({'message': str(e)}), 500

def validate_billing_tables():
    """Validate that billing tables exist in production database"""
    try:
        connection = get_db_connection()
        if not connection:
            logger.error("Database connection failed")
            return False

        with connection.cursor() as cursor:
            required_tables = ['billing_plans', 'user_subscriptions', 'billing_history']
            missing_tables = []
            
            for table in required_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                if not cursor.fetchone():
                    missing_tables.append(table)
            
            if missing_tables:
                logger.error(f"Required billing tables missing: {missing_tables}. Please run init-db.sh")
                return False
                
            logger.info("All required billing tables exist")
            return True
            
    except Exception as e:
        logger.error(f"Error validating billing tables: {e}")
        return False
    finally:
        if connection:
            connection.close()

# MTN MoMo Payment Gateway Integration for South Sudan
class MTNMoMoGateway:
    def __init__(self):
        self.base_url = os.getenv('MTN_MOMO_BASE_URL')
        self.collection_user_id = os.getenv('MTN_MOMO_COLLECTION_USER_ID')
        self.collection_api_key = os.getenv('MTN_MOMO_COLLECTION_API_KEY')
        self.collection_subscription_key = os.getenv('MTN_MOMO_COLLECTION_SUBSCRIPTION_KEY')
        self.target_environment = os.getenv('MTN_MOMO_TARGET_ENVIRONMENT')
        self.currency = os.getenv('MTN_MOMO_CURRENCY')

        # Validate required environment variables
        if not all([self.base_url, self.collection_user_id, self.collection_api_key, self.collection_subscription_key]):
            logger.error("MTN MoMo configuration incomplete. Please check environment variables.")
            raise ValueError("MTN MoMo configuration incomplete")

    def get_access_token(self):
        """Get access token for MTN MoMo API"""
        try:
            url = f"{self.base_url}/collection/token/"
            headers = {
                'Authorization': f'Basic {self.collection_api_key}',
                'Ocp-Apim-Subscription-Key': self.collection_subscription_key,
                'Content-Type': 'application/json'
            }

            response = requests.post(url, headers=headers)
            if response.status_code == 200:
                return response.json().get('access_token')
            else:
                logger.error(f"Failed to get MTN MoMo access token: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error getting MTN MoMo access token: {e}")
            return None

    def request_to_pay(self, amount, currency, phone_number, payer_message="Payment for H-CareCloud services"):
        """Request payment from MTN MoMo user"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return {'success': False, 'error': 'Failed to get access token'}

            transaction_id = str(uuid.uuid4())
            url = f"{self.base_url}/collection/v1_0/requesttopay"

            headers = {
                'Authorization': f'Bearer {access_token}',
                'X-Reference-Id': transaction_id,
                'X-Target-Environment': self.target_environment,
                'Ocp-Apim-Subscription-Key': self.collection_subscription_key,
                'Content-Type': 'application/json'
            }

            payload = {
                'amount': str(amount),
                'currency': currency,
                'externalId': transaction_id,
                'payer': {
                    'partyIdType': 'MSISDN',
                    'partyId': phone_number
                },
                'payerMessage': payer_message,
                'payeeNote': f'Payment for H-CareCloud subscription - {transaction_id}'
            }

            response = requests.post(url, headers=headers, json=payload)

            if response.status_code == 202:
                return {
                    'success': True,
                    'transaction_id': transaction_id,
                    'status': 'PENDING'
                }
            else:
                logger.error(f"MTN MoMo payment request failed: {response.text}")
                return {'success': False, 'error': response.text}

        except Exception as e:
            logger.error(f"Error requesting MTN MoMo payment: {e}")
            return {'success': False, 'error': str(e)}

    def check_payment_status(self, transaction_id):
        """Check the status of a payment request"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return {'success': False, 'error': 'Failed to get access token'}

            url = f"{self.base_url}/collection/v1_0/requesttopay/{transaction_id}"
            headers = {
                'Authorization': f'Bearer {access_token}',
                'X-Target-Environment': self.target_environment,
                'Ocp-Apim-Subscription-Key': self.collection_subscription_key
            }

            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                payment_data = response.json()
                return {
                    'success': True,
                    'status': payment_data.get('status'),
                    'amount': payment_data.get('amount'),
                    'currency': payment_data.get('currency'),
                    'financial_transaction_id': payment_data.get('financialTransactionId'),
                    'external_id': payment_data.get('externalId')
                }
            else:
                logger.error(f"Failed to check MTN MoMo payment status: {response.text}")
                return {'success': False, 'error': response.text}

        except Exception as e:
            logger.error(f"Error checking MTN MoMo payment status: {e}")
            return {'success': False, 'error': str(e)}

# Initialize MTN MoMo gateway
mtn_momo = MTNMoMoGateway()

@billing_bp.route('/payment-methods', methods=['GET'])
@token_required
def get_payment_methods():
    """Get user's saved payment methods"""
    try:
        user_id = request.current_user_id
        connection = get_db_connection()
        if not connection:
            return jsonify({'success': False, 'message': 'Database connection failed'}), 500

        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT id, type, provider, last_four, expiry_date, email, phone_number, is_primary, created_at
                FROM payment_methods
                WHERE user_id = %s AND status = 'active'
                ORDER BY is_primary DESC, created_at DESC
            """, (user_id,))

            methods = cursor.fetchall()

            # Format datetime objects
            for method in methods:
                method['created_at'] = method['created_at'].isoformat() if method['created_at'] else None

            connection.close()

            return jsonify({
                'success': True,
                'data': methods
            })

    except Exception as e:
        logger.error(f"Error getting payment methods: {e}")
        return jsonify({'success': False, 'message': f'Database error: {str(e)}'}), 500

@billing_bp.route('/payment-methods', methods=['POST'])
@token_required
def add_payment_method():
    """Add a new payment method"""
    try:
        user_id = request.current_user_id
        data = request.get_json()

        method_type = data.get('type')  # 'card', 'mtn_momo', 'paypal'
        provider = data.get('provider')

        if not all([method_type, provider]):
            return jsonify({'success': False, 'message': 'Type and provider are required'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'success': False, 'message': 'Database connection failed'}), 500

        with connection.cursor() as cursor:
            # Insert new payment method
            cursor.execute("""
                INSERT INTO payment_methods
                (user_id, type, provider, last_four, expiry_date, email, phone_number, is_primary, status)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, 'active')
            """, (
                user_id,
                method_type,
                provider,
                data.get('last_four'),
                data.get('expiry_date'),
                data.get('email'),
                data.get('phone_number'),
                data.get('is_primary', False)
            ))

            method_id = cursor.lastrowid
            connection.commit()
            connection.close()

            return jsonify({
                'success': True,
                'message': 'Payment method added successfully',
                'method_id': method_id
            })

    except Exception as e:
        logger.error(f"Error adding payment method: {e}")
        return jsonify({'success': False, 'message': f'Database error: {str(e)}'}), 500

@billing_bp.route('/mtn-momo/request-payment', methods=['POST'])
@token_required
def request_mtn_momo_payment():
    """Request payment via MTN MoMo for South Sudan"""
    try:
        user_id = request.current_user_id
        data = request.get_json()

        amount = data.get('amount')
        phone_number = data.get('phone_number')
        currency = data.get('currency', self.currency or 'EUR')

        if not all([amount, phone_number]):
            return jsonify({'success': False, 'message': 'Amount and phone number are required'}), 400

        # Request payment from MTN MoMo
        payment_result = mtn_momo.request_to_pay(amount, currency, phone_number)

        if payment_result['success']:
            # Store transaction in database
            connection = get_db_connection()
            if connection:
                with connection.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO billing_history
                        (user_id, transaction_id, amount, currency, payment_method, status, gateway_response)
                        VALUES (%s, %s, %s, %s, 'mtn_momo', 'pending', %s)
                    """, (
                        user_id,
                        payment_result['transaction_id'],
                        amount,
                        currency,
                        json.dumps(payment_result)
                    ))
                    connection.commit()
                    connection.close()

            return jsonify({
                'success': True,
                'message': 'Payment request sent successfully',
                'transaction_id': payment_result['transaction_id'],
                'status': payment_result['status']
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Payment request failed',
                'error': payment_result.get('error')
            }), 400

    except Exception as e:
        logger.error(f"Error requesting MTN MoMo payment: {e}")
        return jsonify({'success': False, 'message': f'Payment error: {str(e)}'}), 500

@billing_bp.route('/mtn-momo/check-status/<transaction_id>', methods=['GET'])
@token_required
def check_mtn_momo_status(transaction_id):
    """Check MTN MoMo payment status"""
    try:
        user_id = request.current_user_id

        # Check payment status with MTN MoMo
        status_result = mtn_momo.check_payment_status(transaction_id)

        if status_result['success']:
            # Update transaction status in database
            connection = get_db_connection()
            if connection:
                with connection.cursor() as cursor:
                    cursor.execute("""
                        UPDATE billing_history
                        SET status = %s, gateway_response = %s, updated_at = NOW()
                        WHERE transaction_id = %s AND user_id = %s
                    """, (
                        status_result['status'].lower(),
                        json.dumps(status_result),
                        transaction_id,
                        user_id
                    ))
                    connection.commit()
                    connection.close()

            return jsonify({
                'success': True,
                'status': status_result['status'],
                'amount': status_result.get('amount'),
                'currency': status_result.get('currency'),
                'financial_transaction_id': status_result.get('financial_transaction_id')
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to check payment status',
                'error': status_result.get('error')
            }), 400

    except Exception as e:
        logger.error(f"Error checking MTN MoMo payment status: {e}")
        return jsonify({'success': False, 'message': f'Status check error: {str(e)}'}), 500

def init_app(app):
    """Initialize the Billing API with the Flask app"""
    app.register_blueprint(billing_bp)

    # Validate that required database tables exist
    if not validate_billing_tables():
        logger.error("Billing tables validation failed. Please ensure init-db.sh has been run.")
        return False

    logger.info("Billing API initialized successfully")
    return True

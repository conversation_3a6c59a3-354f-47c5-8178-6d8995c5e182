import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';

interface IAttributesItem {
  label: string;
  info: string;
}
type IAttributesItems = Array<IAttributesItem>;

const Attributes = () => {
  const items: IAttributesItems = [
    { label: 'server_id:', info: 'SRV567' },
    { label: 'hostname:', info: 'hcm-prod-01' },
    { label: 'docker_id:', info: 'DCK123' },
    { label: 'container_id:', info: 'CNT567' },
    { label: 'network_id:', info: 'NET345' },
    { label: 'volume_mount:', info: '/var/www/html' },
  ];

  const renderItem = (item: IAttributesItem, index: number) => {
    return (
      <TableRow key={index} className="border-0">
        <TableCell className="text-sm text-secondary-foreground pb-3.5 pe-4 lg:pe-6 py-2">
          {item.label}
        </TableCell>
        <TableCell className="text-sm text-mono pb-3 py-2">
          {item.info}
        </TableCell>
      </TableRow>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Attributes</CardTitle>
      </CardHeader>
      <CardContent className="pt-3.5 pb-1">
        <Table>
          <TableBody>
            {items.map((item, index) => {
              return renderItem(item, index);
            })}
          </TableBody>
        </Table>
      </CardContent>
      <CardFooter className="justify-center">
        <Button mode="link" underlined="dashed" asChild>
          <Link to="/network/user-table/store-clients">All Attributes</Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export { Attributes, type IAttributesItem, type IAttributesItems };

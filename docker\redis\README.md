# H-CareCloud Redis Cache Configuration

## Overview
This directory contains the Redis cache configuration for H-CareCloud Hospital Management System. Redis provides high-performance caching, session storage, and real-time data management for both the main HMS Laravel application and H-CareManager dashboard, optimized for healthcare data processing requirements.

## Architecture

### Redis Structure
```
/docker/redis/
├── redis.conf              # Main Redis configuration
├── redis.conf.template     # Template for environment-based config
└── docker-entrypoint.sh    # Container initialization script
```

### Cache Usage
- **Session Storage**: User session management and authentication
- **Application Cache**: Laravel cache for HMS operations
- **API Response Cache**: Cached API responses for performance
- **Real-time Data**: Live updates and notifications

## Configuration

### redis.conf
```conf
# Network configuration
bind 0.0.0.0
port 6379
protected-mode yes
requirepass ${REDIS_PASSWORD}

# Memory management
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Persistence configuration
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Logging
loglevel notice
logfile /var/log/redis/redis.log
syslog-enabled yes
syslog-ident redis

# Security settings
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command EVAL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_${REDIS_SECRET}"

# Performance optimization
tcp-keepalive 300
timeout 0
tcp-backlog 511
databases 16

# Slow log configuration
slowlog-log-slower-than 10000
slowlog-max-len 128

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# HIPAA compliance
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
```

### docker-entrypoint.sh
```bash
#!/bin/bash
set -e

# Environment setup
export REDIS_PASSWORD=${REDIS_PASSWORD:-secure_redis_password}
export REDIS_SECRET=${REDIS_SECRET:-$(openssl rand -hex 16)}

# Create necessary directories
mkdir -p /var/log/redis
mkdir -p /data

# Set proper permissions
chown -R redis:redis /var/log/redis
chown -R redis:redis /data
chmod 750 /var/log/redis
chmod 750 /data

# Generate configuration from template
envsubst < /etc/redis/redis.conf.template > /etc/redis/redis.conf

# Validate configuration
redis-server --test-config /etc/redis/redis.conf

# Start Redis server
echo "Starting Redis server..."
exec redis-server /etc/redis/redis.conf
```

## Performance Optimization

### Memory Management
```conf
# Memory allocation
maxmemory 512mb
maxmemory-policy allkeys-lru

# Memory sampling
maxmemory-samples 5
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
```

### Connection Optimization
```conf
# Connection settings
tcp-keepalive 300
timeout 0
tcp-backlog 511

# Client limits
maxclients 10000
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
```

### Persistence Strategy
```conf
# RDB snapshots
save 900 1      # Save if at least 1 key changed in 900 seconds
save 300 10     # Save if at least 10 keys changed in 300 seconds
save 60 10000   # Save if at least 10000 keys changed in 60 seconds

# AOF persistence
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
```

## Security Configuration

### HIPAA Compliance
```conf
# Authentication
requirepass ${REDIS_PASSWORD}
protected-mode yes

# Command restrictions
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command EVAL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_${REDIS_SECRET}"

# Network security
bind 127.0.0.1 **********/12
port 6379
```

### Access Control
```conf
# User-based access control (Redis 6+)
user default on nopass ~* &* -@all +@read +@write +@keyspace +@string +@hash +@list +@set +@sortedset +@stream +@bitmap +@hyperloglog +@geo +@pubsub

user hcm_cache on >${HCM_CACHE_PASSWORD} ~hcm:* +@read +@write +@keyspace +@string +@hash +@list +@set +@sortedset

user hms_session on >${HMS_SESSION_PASSWORD} ~session:* +@read +@write +@keyspace +@string +@hash
```

## Application Integration

### Laravel Cache Configuration
```php
// config/cache.php
'redis' => [
    'driver' => 'redis',
    'connection' => 'cache',
    'lock_connection' => 'default',
],

'stores' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
    ],
],

// config/database.php
'redis' => [
    'client' => 'predis',
    'options' => [
        'cluster' => env('REDIS_CLUSTER', 'redis'),
        'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_database_'),
    ],
    'default' => [
        'url' => env('REDIS_URL'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD', null),
        'port' => env('REDIS_PORT', '6379'),
        'database' => env('REDIS_DB', '0'),
    ],
    'cache' => [
        'url' => env('REDIS_URL'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD', null),
        'port' => env('REDIS_PORT', '6379'),
        'database' => env('REDIS_CACHE_DB', '1'),
    ],
],
```

### Session Storage
```php
// config/session.php
'driver' => env('SESSION_DRIVER', 'redis'),
'connection' => 'default',
'store' => env('SESSION_STORE', null),
'lottery' => [2, 100],
'cookie' => env('SESSION_COOKIE', Str::slug(env('APP_NAME', 'laravel'), '_').'_session'),
'path' => '/',
'domain' => env('SESSION_DOMAIN', null),
'secure' => env('SESSION_SECURE_COOKIE'),
'http_only' => true,
'same_site' => 'lax',
```

## Environment Configuration

### Environment Variables
```bash
# Redis configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=secure_redis_password
REDIS_DB=0
REDIS_CACHE_DB=1

# Performance settings
REDIS_MAXMEMORY=512mb
REDIS_MAXMEMORY_POLICY=allkeys-lru
REDIS_TIMEOUT=0

# Security settings
REDIS_PROTECTED_MODE=yes
REDIS_SECRET=random_secret_key

# Persistence settings
REDIS_SAVE_ENABLED=yes
REDIS_AOF_ENABLED=yes
REDIS_AOF_FSYNC=everysec
```

### Docker Integration
```yaml
# docker-compose.yml
redis:
  image: redis:7-alpine
  command: redis-server /etc/redis/redis.conf
  environment:
    - REDIS_PASSWORD=${REDIS_PASSWORD}
    - REDIS_SECRET=${REDIS_SECRET}
  volumes:
    - ./docker/redis/redis.conf.template:/etc/redis/redis.conf.template
    - ./docker/redis/docker-entrypoint.sh:/docker-entrypoint.sh
    - redis_data:/data
    - ./docker/logs/redis:/var/log/redis
  ports:
    - "6379:6379"
  sysctls:
    - net.core.somaxconn=1024
```

## Monitoring and Maintenance

### Performance Monitoring
```bash
# Redis CLI monitoring
redis-cli --latency-history -i 1
redis-cli --stat
redis-cli info memory
redis-cli info stats

# Slow query monitoring
redis-cli slowlog get 10
redis-cli slowlog len
redis-cli slowlog reset
```

### Health Checks
```bash
# Connection test
redis-cli ping

# Memory usage
redis-cli info memory | grep used_memory_human

# Key statistics
redis-cli info keyspace

# Performance metrics
redis-cli info stats | grep -E "(total_commands_processed|total_connections_received)"
```

### Backup and Recovery
```bash
# Manual backup
redis-cli BGSAVE

# Check backup status
redis-cli LASTSAVE

# Restore from backup
cp /backup/dump.rdb /data/
redis-cli DEBUG RESTART
```

## Troubleshooting

### Common Issues
1. **Memory Exhaustion**: Monitor memory usage and adjust maxmemory
2. **Connection Timeouts**: Check network connectivity and timeout settings
3. **Slow Performance**: Analyze slow log and optimize queries
4. **Persistence Failures**: Check disk space and permissions

### Debug Commands
```bash
# Check Redis logs
docker logs redis -f

# Monitor Redis performance
docker exec redis redis-cli --latency

# Check configuration
docker exec redis redis-cli config get "*"

# Memory analysis
docker exec redis redis-cli --bigkeys
```

## Related Documentation
- [Laravel Cache Documentation](https://laravel.com/docs/cache)
- [Session Management Documentation](../../docs/session-management.md)
- [Performance Optimization Documentation](../../docs/performance.md)
- [Main Project Documentation](../scripts/hcm/themes/hcmpro/docs.md)

# H-CareManager Authentication System

## Overview
This directory contains the authentication system for H-CareManager Project, providing secure user authentication, session management, and access control for the server management dashboard. The authentication system is designed for HIPAA compliance and integrates with the H-CareCloud backend for centralized user management.

## Architecture

### Authentication Structure
```
/src/auth/
├── HcmAuthProvider.tsx     # Main authentication context provider
├── AuthContext.tsx         # Authentication context definition
├── auth-types.ts          # TypeScript type definitions
├── auth-utils.ts          # Authentication utility functions
└── hooks/                 # Authentication-related hooks
    ├── useAuth.ts         # Main authentication hook
    ├── usePermissions.ts  # Permission checking hook
    └── useSession.ts      # Session management hook
```

### Authentication Flow
1. **Login Process**: User credentials → Backend validation → JWT token → Session creation
2. **Token Management**: Automatic token refresh, secure storage, expiration handling
3. **Session Monitoring**: Activity tracking, timeout management, security monitoring
4. **Access Control**: Role-based permissions, route protection, component-level security

## Core Components

### HcmAuthProvider.tsx
**Purpose**: Main authentication provider that wraps the entire application.

**Key Features**:
- Centralized authentication state management
- Automatic token refresh and validation
- Session persistence and restoration
- Security event handling and logging
- HIPAA-compliant audit logging

**Implementation**:
```typescript
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  permissions: string[];
  sessionId: string | null;
}

export const HcmAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    permissions: [],
    sessionId: null
  });

  // Authentication methods
  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      const response = await hcmApi.login(credentials);
      
      // Store token securely
      storageService.setSecure('hcm_auth_token', response.token);
      
      // Update auth state
      setAuthState({
        user: response.user,
        isAuthenticated: true,
        isLoading: false,
        permissions: response.permissions,
        sessionId: response.sessionId
      });
      
      // Log authentication event
      logger.info('User authenticated successfully', {
        userId: response.user.id,
        sessionId: response.sessionId,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      logger.error('Authentication failed', error);
      throw error;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await hcmApi.logout();
    } finally {
      // Clear auth state
      storageService.remove('hcm_auth_token');
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        permissions: [],
        sessionId: null
      });
    }
  };

  return (
    <AuthContext.Provider value={{ ...authState, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};
```

### AuthContext.tsx
**Purpose**: React context definition for authentication state and methods.

**Context Interface**:
```typescript
interface AuthContextType {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  permissions: string[];
  sessionId: string | null;
  
  // Methods
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  checkPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  
  // Session management
  extendSession: () => Promise<void>;
  getSessionInfo: () => SessionInfo;
}
```

### auth-types.ts
**Purpose**: TypeScript type definitions for authentication system.

**Key Types**:
```typescript
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  permissions: string[];
  avatar?: string;
  lastLoginAt: string;
  createdAt: string;
}

interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
  permissions: string[];
  sessionId: string;
  expiresAt: string;
}

interface SessionInfo {
  id: string;
  userId: string;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
  lastActivity: string;
  expiresAt: string;
}

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

interface Role {
  id: string;
  name: string;
  level: number;
  permissions: Permission[];
}
```

## Authentication Hooks

### useAuth.ts
**Purpose**: Main authentication hook for components.

**Usage**:
```typescript
import { useAuth } from '@/auth/hooks/useAuth';

const MyComponent = () => {
  const { 
    user, 
    isAuthenticated, 
    login, 
    logout, 
    isLoading 
  } = useAuth();

  if (isLoading) {
    return <Skeleton className="w-full h-64" />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/auth/signin" />;
  }

  return (
    <div>
      <h1>Welcome, {user?.name}</h1>
      <button onClick={logout}>Logout</button>
    </div>
  );
};
```

### usePermissions.ts
**Purpose**: Permission checking and role-based access control.

**Usage**:
```typescript
import { usePermissions } from '@/auth/hooks/usePermissions';

const AdminPanel = () => {
  const { 
    checkPermission, 
    hasRole, 
    permissions 
  } = usePermissions();

  if (!hasRole('admin')) {
    return <div>Access denied</div>;
  }

  return (
    <div>
      {checkPermission('server.manage') && (
        <ServerManagementPanel />
      )}
      {checkPermission('user.manage') && (
        <UserManagementPanel />
      )}
    </div>
  );
};
```

### useSession.ts
**Purpose**: Session management and monitoring.

**Usage**:
```typescript
import { useSession } from '@/auth/hooks/useSession';

const SessionMonitor = () => {
  const { 
    sessionInfo, 
    timeRemaining, 
    extendSession, 
    isExpiringSoon 
  } = useSession();

  if (isExpiringSoon) {
    return (
      <div className="session-warning">
        <p>Session expires in {timeRemaining} minutes</p>
        <button onClick={extendSession}>Extend Session</button>
      </div>
    );
  }

  return null;
};
```

## Security Features

### HIPAA Compliance
- **Audit Logging**: All authentication events logged with user ID, IP, timestamp
- **Session Security**: Secure session management with automatic timeout
- **Data Encryption**: Sensitive data encrypted in storage and transit
- **Access Control**: Role-based permissions with principle of least privilege

### Token Security
```typescript
// Secure token storage
class TokenManager {
  private static readonly TOKEN_KEY = 'hcm_auth_token';
  private static readonly REFRESH_KEY = 'hcm_refresh_token';

  static setTokens(token: string, refreshToken: string): void {
    storageService.setSecure(this.TOKEN_KEY, token);
    storageService.setSecure(this.REFRESH_KEY, refreshToken);
  }

  static getToken(): string | null {
    return storageService.getSecure(this.TOKEN_KEY);
  }

  static isTokenValid(): boolean {
    const token = this.getToken();
    if (!token) return false;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp > Date.now() / 1000;
    } catch {
      return false;
    }
  }

  static clearTokens(): void {
    storageService.remove(this.TOKEN_KEY);
    storageService.remove(this.REFRESH_KEY);
  }
}
```

### Session Monitoring
```typescript
// Session activity tracking
class SessionMonitor {
  private activityTimer: NodeJS.Timeout | null = null;
  private warningTimer: NodeJS.Timeout | null = null;

  startMonitoring(): void {
    // Track user activity
    document.addEventListener('mousedown', this.resetActivityTimer);
    document.addEventListener('keydown', this.resetActivityTimer);
    
    // Set session warning
    this.setSessionWarning();
  }

  private resetActivityTimer = (): void => {
    if (this.activityTimer) {
      clearTimeout(this.activityTimer);
    }
    
    this.activityTimer = setTimeout(() => {
      this.handleSessionTimeout();
    }, SESSION_TIMEOUT);
  };

  private handleSessionTimeout(): void {
    // Log session timeout
    logger.warn('Session timeout', {
      userId: authState.user?.id,
      sessionId: authState.sessionId
    });
    
    // Force logout
    authService.logout();
  }
}
```

## Route Protection

### Protected Route Component
```typescript
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredRole?: string;
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredRole,
  fallback = <Navigate to="/auth/signin" />
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const { checkPermission, hasRole } = usePermissions();

  if (isLoading) {
    return <Skeleton className="w-full h-screen" />;
  }

  if (!isAuthenticated) {
    return fallback;
  }

  if (requiredPermission && !checkPermission(requiredPermission)) {
    return <div>Access denied: Insufficient permissions</div>;
  }

  if (requiredRole && !hasRole(requiredRole)) {
    return <div>Access denied: Insufficient role</div>;
  }

  return <>{children}</>;
};
```

### Route Configuration
```typescript
// Protected routes with permissions
const routes = [
  {
    path: '/account/servers',
    element: (
      <ProtectedRoute requiredPermission="server.view">
        <ServerManagement />
      </ProtectedRoute>
    )
  },
  {
    path: '/account/security',
    element: (
      <ProtectedRoute requiredRole="admin">
        <SecuritySettings />
      </ProtectedRoute>
    )
  }
];
```

## Error Handling

### Authentication Errors
```typescript
// Centralized error handling
class AuthErrorHandler {
  static handle(error: AuthError): void {
    switch (error.code) {
      case 'INVALID_CREDENTIALS':
        toast.error('Invalid email or password');
        break;
      case 'ACCOUNT_LOCKED':
        toast.error('Account locked. Contact administrator.');
        break;
      case 'SESSION_EXPIRED':
        toast.warning('Session expired. Please login again.');
        authService.logout();
        break;
      case 'INSUFFICIENT_PERMISSIONS':
        toast.error('Access denied: Insufficient permissions');
        break;
      default:
        toast.error('Authentication error occurred');
    }

    // Log error for audit
    logger.error('Authentication error', {
      code: error.code,
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
```

## Testing

### Authentication Testing
```typescript
// Mock authentication provider for testing
export const MockAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const mockAuthState = {
    user: mockUser,
    isAuthenticated: true,
    isLoading: false,
    permissions: ['server.view', 'server.manage'],
    sessionId: 'mock-session-id',
    login: jest.fn(),
    logout: jest.fn(),
    checkPermission: jest.fn().mockReturnValue(true),
    hasRole: jest.fn().mockReturnValue(true)
  };

  return (
    <AuthContext.Provider value={mockAuthState}>
      {children}
    </AuthContext.Provider>
  );
};
```

## Related Documentation
- [Services Documentation](../services/README.md)
- [API Integration Documentation](../../../../api/README.md)
- [Security Documentation](../../docs/security.md)
- [H-CareManager Project Documentation](../../docs.md)

/**
 * H‑CareCloud Project – Billing Invoicing Component
 * <AUTHOR> <<EMAIL>>
 * @scrum_master <PERSON><PERSON> <<EMAIL>>
 * @company Hostwek LTD – WekTurbo Dev
 * @website https://hostwek.com/wekturbo
 * @support <EMAIL>
 * @version 1.0.8
 * @year 2025
 */

'use client';

import { CloudDownload, Download } from 'lucide-react';
import { Link } from 'react-router';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { hcmApi } from '@/services/hcm-api';
import { logger } from '@/lib/logger';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface IInvoicingItem {
  id: string;
  invoice_number: string;
  date: string;
  amount: number;
  status: 'pending' | 'paid' | 'failed' | 'upcoming';
  description?: string;
  download_url?: string;
}
type IInvoicingItems = Array<IInvoicingItem>;

const Invoicing = () => {
  const [invoices, setInvoices] = useState<IInvoicingItems>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = async () => {
    try {
      const billingHistory = await hcmApi.getBillingHistory();
      // Convert BillingHistory to IInvoicingItem format
      const invoiceData: IInvoicingItems = billingHistory.map(item => ({
        id: item.id.toString(),
        invoice_number: item.invoice_number || `INV-${item.id}`,
        date: item.billing_date,
        amount: item.amount,
        status: item.status as 'pending' | 'paid' | 'failed' | 'upcoming',
        description: `Payment for ${item.payment_method || 'subscription'}`,
        download_url: item.status === 'paid' ? `/api/billing/invoice/${item.id}/download` : undefined,
      }));
      setInvoices(invoiceData);
    } catch (error) {
      toast.error('Failed to load invoices');
      logger.error('Failed to load invoices', {
        component: 'Invoicing',
        action: 'loadInvoices'
      }, error as Error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string): 'warning' | 'success' | 'destructive' => {
    switch (status) {
      case 'paid': return 'success';
      case 'failed': return 'destructive';
      case 'pending':
      case 'upcoming':
      default: return 'warning';
    }
  };

  const getStatusLabel = (status: string): string => {
    switch (status) {
      case 'paid': return 'Paid';
      case 'failed': return 'Failed';
      case 'pending': return 'Pending';
      case 'upcoming': return 'Upcoming';
      default: return 'Unknown';
    }
  };

  const renderItem = (item: IInvoicingItem) => {
    return (
      <TableRow key={item.id}>
        <TableCell className="text-sm text-foreground font-normal">
          {item.invoice_number}
        </TableCell>
        <TableCell className="lg:text-end">
          <Badge variant={getStatusColor(item.status)} appearance="outline">
            {getStatusLabel(item.status)}
          </Badge>
        </TableCell>
        <TableCell className="text-sm text-foreground font-normal lg:text-end">
          {new Date(item.date).toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
          })}
        </TableCell>
        <TableCell className="text-sm text-secondary-foreground font-normal lg:text-end">
          ${item.amount.toFixed(2)}
        </TableCell>
        <TableCell>
          <Button variant="ghost" mode="icon" disabled={!item.download_url}>
            <Download className="text-blue-500" />
          </Button>
        </TableCell>
      </TableRow>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Billing and Invoicing</CardTitle>
          <Skeleton className="h-10 w-32" />
        </CardHeader>
        <CardContent className="kt-scrollable-x-auto p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-accent/60">
                <TableHead className="min-w-52 h-10">Invoice</TableHead>
                <TableHead className="min-w-24 text-end h-10">Status</TableHead>
                <TableHead className="min-w-32 text-end h-10">Date</TableHead>
                <TableHead className="min-w-20 text-end h-10">Amount</TableHead>
                <TableHead className="w-8 h-10"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 5 }).map((_, i) => (
                <TableRow key={i}>
                  <TableCell><Skeleton className="h-4 w-36" /></TableCell>
                  <TableCell className="lg:text-end"><Skeleton className="h-6 w-16" /></TableCell>
                  <TableCell className="lg:text-end"><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell className="lg:text-end"><Skeleton className="h-4 w-12" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-8" /></TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Billing and Invoicing</CardTitle>
        <Button variant="outline">
          <CloudDownload size={16} />
          Download All
        </Button>
      </CardHeader>
      <CardContent className="kt-scrollable-x-auto p-0">
        <Table>
          <TableHeader>
            <TableRow className="bg-accent/60">
              <TableHead className="min-w-52 h-10">Invoice</TableHead>
              <TableHead className="min-w-24 text-end h-10">Status</TableHead>
              <TableHead className="min-w-32 text-end h-10">Date</TableHead>
              <TableHead className="min-w-20 text-end h-10">Amount</TableHead>
              <TableHead className="w-8 h-10"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {invoices.length > 0 ? (
              invoices.map((item) => {
                return renderItem(item);
              })
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8 text-secondary-foreground">
                  No invoices found. Your billing history will appear here.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
      <CardFooter className="justify-center">
        <Button mode="link" underlined="dashed" asChild>
          <Link to="/account/billing/history">View all Payments</Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export { Invoicing, type IInvoicingItem, type IInvoicingItems };

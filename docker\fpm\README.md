# H-CareCloud PHP-FPM Configuration

## Overview
This directory contains the PHP-FPM (FastCGI Process Manager) configuration for H-CareCloud Hospital Management System. PHP-FPM provides optimized PHP processing with improved performance, security, and resource management for the Laravel-based HMS application.

## Architecture

### PHP-FPM Structure
```
/docker/fpm/
├── docker-entrypoint.sh    # Container initialization script
├── ensure-dirs.sh          # Directory creation and permissions
├── php.ini                 # PHP configuration settings
└── www.conf                # PHP-FPM pool configuration
```

### Container Integration
- **Base Image**: Official PHP-FPM Alpine Linux image
- **Laravel Support**: Optimized for Laravel framework requirements
- **Database Connectivity**: MySQL/MariaDB connection pooling
- **File Handling**: Optimized for medical file uploads and processing

## Configuration Files

### php.ini Settings
```ini
# Performance Optimization
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
post_max_size = 100M
upload_max_filesize = 50M

# Security Settings
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off
session.cookie_httponly = On
session.cookie_secure = On

# Error Handling
display_errors = Off
log_errors = On
error_log = /var/log/php/error.log

# OPcache Configuration
opcache.enable = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 10000
opcache.validate_timestamps = 0
```

### www.conf Pool Settings
```ini
[www]
user = www-data
group = www-data
listen = 9000
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

# Process Management
pm = dynamic
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 20
pm.max_requests = 1000

# Performance Tuning
request_terminate_timeout = 300
request_slowlog_timeout = 10
slowlog = /var/log/php/slow.log

# Security
security.limit_extensions = .php
```

## Performance Optimization

### Process Management
- **Dynamic Scaling**: Automatically adjusts worker processes based on load
- **Memory Management**: Optimized memory allocation for medical data processing
- **Connection Pooling**: Efficient database connection management
- **Request Handling**: Optimized for concurrent user sessions

### OPcache Configuration
```ini
# Production OPcache Settings
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 10000
opcache.validate_timestamps = 0
opcache.save_comments = 0
opcache.fast_shutdown = 1
```

### File Upload Optimization
```ini
# Medical File Upload Settings
file_uploads = On
upload_max_filesize = 50M
max_file_uploads = 20
post_max_size = 100M
max_input_vars = 3000
```

## Security Configuration

### HIPAA Compliance
- **Data Encryption**: Secure handling of PHI (Protected Health Information)
- **Access Logging**: Comprehensive request logging for audit trails
- **Session Security**: Secure session management with proper timeouts
- **File Permissions**: Restricted file access and execution permissions

### Security Headers
```ini
# Security Headers
session.cookie_secure = On
session.cookie_httponly = On
session.cookie_samesite = Strict
session.use_strict_mode = 1
session.gc_maxlifetime = 3600
```

### Error Handling
```ini
# Production Error Settings
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php/error.log
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
```

## Environment Configuration

### Development Settings
```ini
# Development Environment
display_errors = On
error_reporting = E_ALL
opcache.validate_timestamps = 1
opcache.revalidate_freq = 2
xdebug.mode = debug
```

### Production Settings
```ini
# Production Environment
display_errors = Off
error_reporting = E_ERROR | E_WARNING | E_PARSE
opcache.validate_timestamps = 0
opcache.max_accelerated_files = 20000
realpath_cache_size = 4096K
```

### Environment Variables
```bash
# PHP-FPM Configuration
PHP_MEMORY_LIMIT=512M
PHP_MAX_EXECUTION_TIME=300
PHP_UPLOAD_MAX_FILESIZE=50M
PHP_POST_MAX_SIZE=100M

# OPcache Settings
PHP_OPCACHE_MEMORY=256
PHP_OPCACHE_MAX_FILES=10000
PHP_OPCACHE_VALIDATE=0

# Security Settings
PHP_EXPOSE_PHP=Off
PHP_ALLOW_URL_FOPEN=Off
PHP_SESSION_COOKIE_SECURE=On
```

## Laravel Integration

### Laravel Optimization
```ini
# Laravel-Specific Settings
memory_limit = 512M
max_execution_time = 300
max_input_vars = 3000
post_max_size = 100M

# Composer Optimization
opcache.enable = 1
opcache.max_accelerated_files = 20000
realpath_cache_size = 4096K
realpath_cache_ttl = 600
```

### Database Configuration
```ini
# MySQL Connection Settings
mysqli.default_host = mysql
mysqli.default_user = hcarecloud_user
mysqli.default_pw = secure_password
mysqli.reconnect = On
mysqli.cache_size = 2000
```

### Session Management
```ini
# Laravel Session Configuration
session.driver = database
session.lifetime = 120
session.expire_on_close = false
session.encrypt = true
session.files = /var/www/html/storage/framework/sessions
```

## Monitoring and Logging

### Performance Monitoring
```ini
# Slow Query Logging
request_slowlog_timeout = 10
slowlog = /var/log/php/slow.log

# Process Monitoring
pm.status_path = /status
ping.path = /ping
ping.response = pong
```

### Log Configuration
```bash
# Log Files
/var/log/php/error.log      # PHP errors
/var/log/php/slow.log       # Slow requests
/var/log/php/access.log     # Access logging
/var/log/php/fpm.log        # FPM process logs
```

### Health Checks
```bash
# PHP-FPM Status Check
curl http://localhost/status

# PHP-FPM Ping Check
curl http://localhost/ping

# Process Status
ps aux | grep php-fpm
```

## Troubleshooting

### Common Issues
1. **Memory Exhaustion**: Increase memory_limit or optimize code
2. **Slow Requests**: Check slow.log and optimize database queries
3. **File Upload Failures**: Verify upload_max_filesize and post_max_size
4. **Connection Timeouts**: Adjust max_execution_time and request_terminate_timeout

### Debug Commands
```bash
# Check PHP-FPM status
docker exec fpm php-fpm -t

# View PHP configuration
docker exec fpm php -i

# Monitor PHP-FPM processes
docker exec fpm ps aux | grep php-fpm

# Check error logs
docker exec fpm tail -f /var/log/php/error.log
```

### Performance Tuning
```bash
# Monitor memory usage
docker stats fpm

# Check OPcache status
docker exec fpm php -r "print_r(opcache_get_status());"

# Analyze slow queries
docker exec fpm tail -f /var/log/php/slow.log
```

## Maintenance

### Regular Tasks
- **Log Rotation**: Rotate PHP error and access logs
- **OPcache Monitoring**: Monitor cache hit rates and memory usage
- **Performance Review**: Analyze slow request logs
- **Security Updates**: Keep PHP version and extensions updated

### Backup Procedures
```bash
# Backup PHP configuration
cp /docker/fpm/php.ini /backups/config/php.ini.$(date +%Y%m%d)
cp /docker/fpm/www.conf /backups/config/www.conf.$(date +%Y%m%d)

# Backup session data
tar -czf /backups/sessions/sessions_$(date +%Y%m%d).tar.gz /var/www/html/storage/framework/sessions/
```

## Related Documentation
- [Nginx Configuration Documentation](../nginx/README.md)
- [MySQL Database Documentation](../mysql/README.md)
- [Docker Infrastructure Documentation](../README.md)
- [Main Project Documentation](../scripts/hcm/themes/hcmpro/docs.md)
